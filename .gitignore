# 环境变量和敏感信息
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# FastAPI和uvicorn
*.log

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 前端构建文件
/frontend/dist/
/frontend/build/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
*.tmp

# 上传文件临时目录
uploads/
files/

# Docker相关
.dockerignore

# 测试覆盖率
coverage/
*.coverage
.nyc_output

# Pytest
.pytest_cache/

# 数据库备份
*.db.backup
*.sql.backup