```mermaid
graph TD
    A[文档上传Webhook] --> B[处理上传文档]
    B --> C[文档切分]
    C --> D["生成向量嵌入<br/>(Qwen3-Embedding-4B)"]
    D --> E[存储向量]
    E --> F[上传响应]
    
    G[查询Webhook] --> H[处理查询]
    H --> I["查询向量化<br/>(Qwen3-Embedding-4B)"]
    I --> J[检索相关文档]
    J --> K["文档重排序<br/>(BGE-Reranker-v2-M3)"]
    K --> L["生成回答<br/>(Qwen3:8B)"]
    L --> M[检查流式输出]
    M --> N{流式输出?}
    N -->|是| O[流式响应]
    N -->|否| P[JSON响应]
    
    style A fill:#e1f5fe
    style G fill:#e1f5fe
    style D fill:#f3e5f5
    style I fill:#f3e5f5
    style K fill:#fff3e0
    style L fill:#e8f5e8
    style O fill:#fce4ec
    style P fill:#fce4ec
```