# 智能聊天系统前后端需求文档

## 1. 项目概述

### 1.1 项目背景
基于已部署的n8n RAG工作流，开发完整的前后端聊天系统。n8n已提供核心的文档处理和智能问答API，现需要构建用户友好的Web界面和业务逻辑层。

### 1.2 现有API能力
根据n8n workflow，已具备以下核心API：

#### 1.2.1 文档上传API
- **接口**: `POST /webhook/rag-upload`
- **功能**: 文档上传、切分、向量化、存储
- **支持格式**: txt, md, pdf, docx
- **返回**: 处理状态和切片数量

#### 1.2.2 智能问答API  
- **接口**: `POST /webhook/rag-query`
- **功能**: 基于知识库的智能问答
- **特性**: 支持流式输出，返回答案和源文档
- **模型**: 使用Qwen3系列模型

### 1.3 系统目标
- 构建现代化的聊天界面，支持流式对话体验
- 实现完整的文档管理功能
- 提供多轮对话和上下文管理
- 确保良好的用户体验和系统性能

## 2. 系统架构设计

### 2.1 整体架构
```
用户界面 (React) → 后端API (FastAPI) → n8n Workflows → Ollama Models
        ↓                    ↓
    本地存储          数据库 (SQLite)
```

### 2.2 技术栈选择

#### 2.2.1 前端技术栈
- **框架**: React 18 + TypeScript
- **样式**: Tailwind CSS
- **状态管理**: React Query/SWR
- **流式处理**: EventSource API (Server-Sent Events)
- **文件上传**: React Dropzone
- **Markdown渲染**: React Markdown
- **构建工具**: Vite

#### 2.2.2 后端技术栈
- **框架**: FastAPI (Python 3.9+)
- **数据库**: SQLite (文件位置: dbfile/aichatdb.db)
- **ORM**: SQLAlchemy
- **HTTP客户端**: httpx (异步)
- **服务器**: uvicorn
- **任务队列**: 内置异步处理

## 3. 项目结构设计

### 3.1 前后端分离结构
```
AIKnowledge/
├── backend/                    # 后端服务
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── core/              # 核心配置
│   │   │   ├── __init__.py
│   │   │   ├── config.py      # 配置管理
│   │   │   ├── database.py    # 数据库连接
│   │   │   └── security.py    # 安全配置
│   │   ├── api/               # API路由
│   │   │   ├── __init__.py
│   │   │   ├── v1/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── chat.py    # 聊天相关API
│   │   │   │   └── documents.py # 文档相关API
│   │   │   └── dependencies.py # 依赖注入
│   │   ├── models/            # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── chat.py        # 会话和消息模型
│   │   │   └── document.py    # 文档模型
│   │   ├── schemas/           # Pydantic模式
│   │   │   ├── __init__.py
│   │   │   ├── chat.py        # 聊天相关schema
│   │   │   └── document.py    # 文档相关schema
│   │   ├── services/          # 业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── chat_service.py # 聊天服务
│   │   │   ├── document_service.py # 文档服务
│   │   │   └── n8n_client.py  # n8n API客户端
│   │   └── utils/             # 工具函数
│   │       ├── __init__.py
│   │       ├── exceptions.py  # 异常处理
│   │       └── logger.py      # 日志配置
│   ├── requirements.txt       # Python依赖
│   ├── Dockerfile            # Docker配置
│   └── pytest.ini           # 测试配置
│
├── frontend/                  # 前端应用
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/        # 组件
│   │   │   ├── common/        # 通用组件
│   │   │   ├── chat/          # 聊天相关组件
│   │   │   │   ├── ChatInterface.tsx
│   │   │   │   ├── MessageInput.tsx
│   │   │   │   ├── MessageList.tsx
│   │   │   │   └── StreamingMessage.tsx
│   │   │   ├── document/      # 文档相关组件
│   │   │   │   ├── DocumentUpload.tsx
│   │   │   │   ├── DocumentManager.tsx
│   │   │   │   └── DocumentList.tsx
│   │   │   └── sources/       # 源文档引用组件
│   │   │       └── SourceReferences.tsx
│   │   ├── hooks/             # 自定义Hook
│   │   │   ├── useChat.ts
│   │   │   ├── useDocument.ts
│   │   │   └── useStreaming.ts
│   │   ├── services/          # API服务
│   │   │   ├── api.ts         # API客户端
│   │   │   ├── chatService.ts # 聊天服务
│   │   │   └── documentService.ts # 文档服务
│   │   ├── types/             # 类型定义
│   │   │   ├── chat.ts        # 聊天相关类型
│   │   │   └── document.ts    # 文档相关类型
│   │   ├── utils/             # 工具函数
│   │   │   ├── constants.ts   # 常量
│   │   │   ├── helpers.ts     # 帮助函数
│   │   │   └── formatting.ts  # 格式化工具
│   │   ├── styles/            # 样式文件
│   │   │   ├── globals.css    # 全局样式
│   │   │   └── components.css # 组件样式
│   │   ├── App.tsx           # 应用入口
│   │   ├── main.tsx          # 主入口
│   │   └── vite-env.d.ts     # Vite类型定义
│   ├── package.json          # 依赖管理
│   ├── vite.config.ts        # Vite配置
│   ├── tailwind.config.js    # Tailwind配置
│   ├── tsconfig.json         # TypeScript配置
│   └── Dockerfile            # Docker配置
│
├── dbfile/                   # 数据库文件
│   ├── aichatdb.db          # SQLite数据库（已存在）
│   └── init_database.sql    # 初始化脚本
│
├── docker-compose.yml        # Docker Compose配置
├── .env                     # 环境变量
├── .gitignore              # Git忽略文件
└── README.md               # 项目说明
```

## 4. 后端开发需求

### 4.1 核心功能模块

#### 4.1.1 会话管理服务
**需求描述**: 管理用户聊天会话和对话历史

**功能详情**:
- 生成和维护唯一session_id
- 存储对话历史记录（用户消息+AI回复）
- 实现上下文管理（因为n8n API无状态）
- 会话超时和清理机制

#### 4.1.2 消息处理服务
**需求描述**: 处理用户消息，调用n8n API，管理流式响应

**功能详情**:
- 接收用户消息并构建上下文
- 调用n8n查询API（/webhook/rag-query）
- 处理流式响应和标准响应
- 保存对话记录

#### 4.1.3 文档管理服务
**需求描述**: 管理用户上传的文档，提供文档列表和元数据管理

**功能详情**:
- 接收文件上传请求
- 调用n8n上传API（/webhook/rag-upload）
- 存储文档元数据（名称、大小、类型、上传时间）
- 提供文档列表查询
- 文档删除功能（标记删除）

#### 4.1.4 n8n代理服务层
**需求描述**: 封装n8n API调用，处理错误和重试

**功能详情**:
- 统一n8n API调用接口
- 处理网络错误和重试机制
- 响应格式标准化
- 请求日志和监控

## 5. 前端开发需求

### 5.1 核心功能组件

#### 5.1.1 聊天界面组件
**组件名**: `ChatInterface`
**功能需求**:
- 消息列表展示（用户消息、AI回复、系统提示）
- 支持Markdown渲染（代码高亮、表格、链接等）
- 流式消息显示（打字机效果）
- 消息状态指示（发送中、已送达、失败）
- 消息时间戳显示
- 滚动到底部自动跟随

#### 5.1.2 消息输入组件
**组件名**: `MessageInput`
**功能需求**:
- 多行文本输入（自动高度调整）
- 快捷键支持（Enter发送，Shift+Enter换行）
- 输入状态指示（正在输入、发送中）
- 字符计数和限制提示
- 发送按钮状态管理

#### 5.1.3 文档上传组件
**组件名**: `DocumentUpload`
**功能需求**:
- 拖拽上传支持
- 文件选择器
- 文件格式验证（txt, md, pdf, docx）
- 文件大小验证（最大100MB）
- 上传进度显示
- 批量上传支持

#### 5.1.4 文档管理组件
**组件名**: `DocumentManager`
**功能需求**:
- 已上传文档列表展示
- 文档信息显示（名称、大小、类型、上传时间）
- 文档状态指示（处理中、已完成、失败）
- 文档删除功能
- 搜索和过滤功能

#### 5.1.5 源文档引用组件
**组件名**: `SourceReferences`
**功能需求**:
- 显示当前AI回复的源文档片段
- 相似度分数显示
- 源文档链接和定位
- 引用内容高亮显示
- 展开/折叠详细内容

### 5.2 流式响应处理
**需求描述**: 实现Server-Sent Events处理，支持流式消息显示

**功能详情**:
- EventSource API处理
- 流式消息缓冲和显示
- 自动重连机制
- 错误处理和降级方案

### 5.3 状态管理
**需求描述**: 使用React Query管理服务器状态，useState管理本地状态

**功能详情**:
- 会话状态管理
- 消息状态管理
- 文档状态管理
- UI状态管理

## 6. 开发计划

### 6.1 第一阶段：项目结构和后端核心功能
- 创建前后端项目结构
- 搭建FastAPI框架
- 实现数据库模型和连接
- 开发会话管理API
- 开发消息处理API
- 实现n8n代理服务

### 6.2 第二阶段：前端基础界面
- 搭建React + TypeScript + Vite项目
- 实现基础聊天界面
- 开发消息输入和显示组件
- 实现流式响应处理
- 基础样式和布局

### 6.3 第三阶段：文档管理功能
- 开发文档上传API
- 实现文档管理界面
- 开发文件拖拽上传组件
- 实现上传进度和状态管理
- 开发源文档引用显示

### 6.4 第四阶段：优化和完善
- 流式响应优化
- 错误处理和重试机制
- 性能优化（虚拟滚动等）
- 响应式设计和移动端适配
- 部署配置和文档

## 7. 技术要求

### 7.1 性能要求
- 消息发送后1秒内开始流式输出
- 文档列表查询<300ms
- 会话历史查询<500ms
- 支持50+并发会话
- 支持最大100MB文件上传

### 7.2 安全要求
- 文件类型白名单验证
- 文件大小限制
- 请求频率限制（每分钟60次）
- 输入内容过滤和验证

### 7.3 可靠性要求
- API调用重试机制（最多3次）
- 数据库连接池管理
- 异常处理和错误日志
- 优雅关闭和重启

## 8. 验收标准

### 8.1 功能验收
- 用户可以成功上传文档并看到处理状态
- 用户可以进行多轮对话，系统维护上下文
- 流式输出工作正常，响应流畅
- 源文档引用准确显示
- 文档管理功能完整可用

### 8.2 性能验收
- 消息发送响应时间<1秒
- 文件上传进度实时显示
- 长对话列表滚动流畅
- 移动端体验良好

### 8.3 用户体验验收
- 界面美观，操作直观
- 错误提示友好明确
- 加载状态提示完善
- 首次使用引导清晰