# 智能聊天系统 (AI Knowledge Chat System)

基于n8n RAG工作流的前后端分离智能聊天系统，支持文档上传、智能问答和流式对话体验。

## 📋 项目概述

本项目构建在已部署的n8n RAG工作流基础上，提供完整的Web界面和业务逻辑层。系统支持多种文档格式上传、智能问答、会话管理和源文档引用功能。

### 🏗️ 系统架构

```
用户界面 (React) → 后端API (FastAPI) → n8n Workflows → Ollama Models
        ↓                    ↓
    本地存储          数据库 (SQLite)
```

## 🚀 技术栈

### 后端技术栈
- **框架**: FastAPI (Python 3.9+)
- **数据库**: SQLite
- **ORM**: SQLAlchemy
- **HTTP客户端**: httpx (异步)
- **服务器**: uvicorn

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式**: Tailwind CSS
- **状态管理**: React Query/SWR
- **流式处理**: EventSource API (Server-Sent Events)
- **文件上传**: React Dropzone
- **Markdown渲染**: React Markdown

## 📁 项目结构

```
AIKnowledge/
├── backend/                    # 后端服务
│   ├── app/
│   │   ├── core/              # 核心配置
│   │   │   ├── config.py      # 环境配置
│   │   │   ├── database.py    # 数据库连接
│   │   │   └── security.py    # 安全配置
│   │   ├── api/v1/            # API路由
│   │   │   ├── chat.py        # 聊天相关API
│   │   │   └── documents.py   # 文档相关API
│   │   ├── models/            # 数据模型
│   │   │   ├── chat.py        # 会话和消息模型
│   │   │   └── document.py    # 文档模型
│   │   ├── schemas/           # Pydantic模式
│   │   │   ├── chat.py        # 聊天相关schema
│   │   │   └── document.py    # 文档相关schema
│   │   ├── services/          # 业务逻辑
│   │   │   ├── chat_service.py # 聊天服务
│   │   │   ├── document_service.py # 文档服务
│   │   │   └── n8n_client.py  # n8n API客户端
│   │   └── utils/             # 工具函数
│   ├── requirements.txt       # Python依赖
│   └── Dockerfile            # Docker配置
│
├── frontend/                  # 前端应用
│   ├── src/
│   │   ├── components/        # 组件
│   │   │   ├── chat/         # 聊天相关组件
│   │   │   ├── document/     # 文档相关组件
│   │   │   └── sources/      # 源文档引用组件
│   │   ├── hooks/            # 自定义Hook
│   │   ├── services/         # API服务
│   │   ├── types/            # TypeScript类型定义
│   │   ├── utils/            # 工具函数
│   │   └── styles/           # 样式文件
│   ├── package.json          # 依赖管理
│   ├── vite.config.ts        # Vite配置
│   └── Dockerfile            # Docker配置
│
├── dbfile/                   # 数据库文件
│   └── aichatdb.db          # SQLite数据库
│
├── docker-compose.yml        # Docker Compose配置
├── .env                     # 环境变量
└── README.md               # 项目说明
```

## 🛠️ 开发环境搭建

### 前置要求
- Python 3.9+
- Node.js 16+
- Docker和Docker Compose (可选)
- n8n RAG工作流已部署并运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd AIKnowledge
```

### 2. 后端设置
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp ../.env.example .env
# 编辑.env文件，配置数据库和n8n URL

# 运行后端服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. 前端设置
```bash
cd frontend

# 安装依赖
npm install
# 或 yarn install

# 启动开发服务器
npm run dev
# 或 yarn dev
```

### 4. 使用Docker (推荐)
```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d
```

## 📚 API接口文档

### 会话管理
- `POST /api/chat/sessions` - 创建新会话
- `GET /api/chat/sessions/{id}` - 获取会话信息
- `GET /api/chat/sessions/{id}/messages` - 获取会话历史
- `DELETE /api/chat/sessions/{id}` - 删除会话

### 消息处理
- `POST /api/chat/message` - 发送消息（标准响应）
- `GET /api/chat/message/stream` - 流式消息响应

### 文档管理
- `POST /api/documents/upload` - 上传文档
- `GET /api/documents` - 获取文档列表
- `GET /api/documents/{id}` - 获取文档详情
- `DELETE /api/documents/{id}` - 删除文档

## 🎯 核心功能

### 智能对话
- ✅ 基于知识库的智能问答
- ✅ 流式响应（打字机效果）
- ✅ 多轮对话上下文管理
- ✅ 消息状态指示

### 文档管理
- ✅ 支持多种格式：txt, md, pdf, docx
- ✅ 拖拽上传功能
- ✅ 上传进度显示
- ✅ 文档状态跟踪

### 源文档引用
- ✅ 显示AI回复的源文档片段
- ✅ 相似度分数展示
- ✅ 引用内容高亮
- ✅ 展开/折叠功能

### 用户体验
- ✅ 响应式设计（桌面/平板/移动）
- ✅ 深色/浅色主题切换
- ✅ 实时状态反馈
- ✅ 错误处理和重试

## 🧪 测试

### 运行后端测试
```bash
cd backend
pytest
```

### 运行前端测试
```bash
cd frontend
npm test
```

## 📦 部署

### 生产环境部署
1. 配置生产环境变量
2. 构建前端静态文件
3. 使用Docker Compose部署

```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 配置说明

### 环境变量配置
- `DATABASE_URL`: SQLite数据库路径
- `N8N_WEBHOOK_BASE_URL`: n8n webhook基础URL
- `MAX_FILE_SIZE`: 最大文件上传大小
- `RATE_LIMIT_PER_MINUTE`: API访问频率限制

## 🤝 开发指南

### 代码规范
- 后端遵循PEP 8规范
- 前端使用ESLint和Prettier
- 提交信息使用Conventional Commits

### 开发流程
1. 创建功能分支
2. 编写代码和测试
3. 提交代码审查
4. 合并到主分支

## 📈 性能指标

### 目标性能
- API响应时间95% < 1秒
- 文件上传成功率 > 95%
- 流式响应延迟 < 200ms
- 系统可用性 > 99.5%

## 🐛 故障排除

### 常见问题
1. **n8n连接失败**: 检查N8N_WEBHOOK_BASE_URL配置
2. **数据库连接问题**: 确认数据库文件路径和权限
3. **文件上传失败**: 检查文件大小和格式限制
4. **流式响应中断**: 检查网络连接和服务器状态

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢n8n社区提供的RAG工作流解决方案。

---

**注意**: 本项目仍在开发中，功能可能会有所变动。如有问题或建议，请提交Issue。