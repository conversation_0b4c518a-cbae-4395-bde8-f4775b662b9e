"""
聊天相关的API端点
"""
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from fastapi.security import HTTP<PERSON>earer
import structlog

from app.services import (
    MessageProcessor, get_message_processor,
    MessageType, MessageProcessingError
)
from app.schemas.chat import (
    ChatRequest, ChatResponse, StreamChatResponse,
    ChatSessionResponse, ChatHistoryResponse,
    ChatSessionCreate, ChatSessionUpdate,
    ChatSessionListResponse, MessageRole
)
from app.services.chat_service import get_chat_service, ChatService
from app.core.database import get_db
from sqlalchemy.orm import Session


router = APIRouter()
security = HTTPBearer(auto_error=False)
logger = structlog.get_logger(__name__)


@router.post("/message", response_model=ChatResponse)
async def send_message(
    request: ChatRequest,
    message_processor: MessageProcessor = Depends(get_message_processor),
    db: Session = Depends(get_db)
):
    """
    发送聊天消息
    
    - **message**: 用户消息内容
    - **session_id**: 会话ID（可选，不提供则创建新会话）
    - **stream**: 是否流式响应（此端点仅支持非流式）
    """
    try:
        logger.info(
            "收到聊天消息请求",
            session_id=request.session_id,
            message_length=len(request.message),
            stream=request.stream
        )
        
        # 强制非流式响应
        if request.stream:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="此端点不支持流式响应，请使用 /stream 端点"
            )
        
        # 处理消息
        response = await message_processor.process_message(
            request=request,
            message_type=MessageType.TEXT
        )
        
        logger.info(
            "聊天消息处理完成",
            session_id=response.session_id,
            message_id=response.message_id,
            response_length=len(response.message)
        )
        
        return response
        
    except MessageProcessingError as e:
        logger.error("消息处理失败", error=str(e), error_type=e.error_type)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"消息处理失败: {str(e)}"
        )
    except Exception as e:
        logger.error("聊天消息请求异常", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.post("/stream")
async def stream_message(
    request: ChatRequest,
    message_processor: MessageProcessor = Depends(get_message_processor)
):
    """
    流式聊天消息
    
    - **message**: 用户消息内容
    - **session_id**: 会话ID（可选，不提供则创建新会话）
    - **stream**: 是否流式响应（此端点强制为流式）
    """
    try:
        logger.info(
            "收到流式聊天请求",
            session_id=request.session_id,
            message_length=len(request.message)
        )
        
        # 强制流式响应
        request.stream = True
        
        # 获取流式响应生成器
        stream_generator = await message_processor.process_message(
            request=request,
            message_type=MessageType.TEXT
        )
        
        async def generate_response():
            """生成流式响应"""
            try:
                async for chunk in stream_generator:
                    yield f"data: {chunk}\n\n"
                
                # 发送结束标志
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error("流式响应生成失败", error=str(e))
                error_response = StreamChatResponse(
                    chunk=f"[ERROR] {str(e)}",
                    session_id=request.session_id or "unknown",
                    is_complete=True
                )
                yield f"data: {error_response.model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except MessageProcessingError as e:
        logger.error("流式消息处理失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"流式消息处理失败: {str(e)}"
        )
    except Exception as e:
        logger.error("流式聊天请求异常", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/history/{session_id}", response_model=ChatHistoryResponse)
async def get_chat_history(
    session_id: str,
    limit: Optional[int] = 50,
    chat_service: ChatService = Depends(get_chat_service),
    message_processor: MessageProcessor = Depends(get_message_processor)
):
    """
    获取聊天历史
    
    - **session_id**: 会话ID
    - **limit**: 消息数量限制（默认50）
    """
    try:
        logger.info("获取聊天历史", session_id=session_id, limit=limit)
        
        # 检查会话是否存在
        session = chat_service.get_session(session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"会话 {session_id} 不存在"
            )
        
        # 获取消息历史
        messages = await message_processor.get_message_history(
            session_id=session_id,
            limit=limit
        )
        
        # 构造响应
        response = ChatHistoryResponse(
            session=ChatSessionResponse(
                id=session.id,
                title=session.title,
                user_id=session.user_id,
                created_at=session.created_at,
                updated_at=session.updated_at,
                messages_count=len(messages)
            ),
            messages=[
                {
                    "id": msg["id"],
                    "session_id": session_id,
                    "role": msg["role"],
                    "content": msg["content"],
                    "sources": msg.get("sources"),
                    "created_at": msg.get("created_at"),
                    "content_preview": msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
                }
                for msg in messages
            ]
        )
        
        logger.info(
            "聊天历史获取成功",
            session_id=session_id,
            messages_count=len(messages)
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取聊天历史失败", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取聊天历史失败: {str(e)}"
        )


@router.post("/document-qa", response_model=ChatResponse)
async def document_qa(
    request: ChatRequest,
    message_processor: MessageProcessor = Depends(get_message_processor)
):
    """
    文档问答
    
    基于已上传的文档回答问题
    - **message**: 问题内容
    - **session_id**: 会话ID（可选）
    - **stream**: 是否流式响应（此端点仅支持非流式）
    """
    try:
        logger.info(
            "收到文档问答请求",
            session_id=request.session_id,
            question_length=len(request.message)
        )
        
        # 强制非流式响应
        if request.stream:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="此端点不支持流式响应，请使用 /document-qa-stream 端点"
            )
        
        # 处理文档问答
        response = await message_processor.process_message(
            request=request,
            message_type=MessageType.DOCUMENT_QA
        )
        
        logger.info(
            "文档问答处理完成",
            session_id=response.session_id,
            message_id=response.message_id,
            response_length=len(response.message),
            sources_count=len(response.sources) if response.sources else 0
        )
        
        return response
        
    except MessageProcessingError as e:
        logger.error("文档问答处理失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档问答处理失败: {str(e)}"
        )
    except Exception as e:
        logger.error("文档问答请求异常", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.post("/document-qa-stream")
async def document_qa_stream(
    request: ChatRequest,
    message_processor: MessageProcessor = Depends(get_message_processor)
):
    """
    流式文档问答
    
    基于已上传的文档进行流式问答
    - **message**: 问题内容  
    - **session_id**: 会话ID（可选）
    """
    try:
        logger.info(
            "收到流式文档问答请求",
            session_id=request.session_id,
            question_length=len(request.message)
        )
        
        # 强制流式响应
        request.stream = True
        
        # 获取流式响应生成器
        stream_generator = await message_processor.process_message(
            request=request,
            message_type=MessageType.DOCUMENT_QA
        )
        
        async def generate_response():
            """生成流式响应"""
            try:
                async for chunk in stream_generator:
                    yield f"data: {chunk}\n\n"
                
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error("流式文档问答生成失败", error=str(e))
                error_response = StreamChatResponse(
                    chunk=f"[ERROR] {str(e)}",
                    session_id=request.session_id or "unknown",
                    is_complete=True
                )
                yield f"data: {error_response.model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except MessageProcessingError as e:
        logger.error("流式文档问答处理失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"流式文档问答处理失败: {str(e)}"
        )
    except Exception as e:
        logger.error("流式文档问答请求异常", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/processor/status")
async def get_processor_status(
    message_processor: MessageProcessor = Depends(get_message_processor)
):
    """
    获取消息处理器状态
    
    返回处理器的健康状态和统计信息
    """
    try:
        health_info = await message_processor.health_check()
        
        return {
            "status": "ok",
            "health": health_info,
            "timestamp": health_info.get("timestamp")
        }
        
    except Exception as e:
        logger.error("获取处理器状态失败", error=str(e))
        return {
            "status": "error",
            "error": str(e),
            "timestamp": None
        }


@router.post("/processor/cleanup")
async def cleanup_processor(
    max_age_minutes: int = 60,
    message_processor: MessageProcessor = Depends(get_message_processor)
):
    """
    清理处理器旧状态
    
    - **max_age_minutes**: 最大保留时间（分钟，默认60）
    """
    try:
        removed_count = message_processor.clear_old_processing_status(max_age_minutes)
        
        return {
            "status": "ok",
            "removed_count": removed_count,
            "max_age_minutes": max_age_minutes
        }
        
    except Exception as e:
        logger.error("清理处理器状态失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理失败: {str(e)}"
        )