"""
文档管理相关的API端点
"""
import os
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query, Form
from sqlalchemy.orm import Session
import structlog

from app.services import (
    DocumentService, get_document_service,
    DocumentValidationError, DocumentServiceError
)
from app.schemas.document import (
    DocumentResponse, DocumentListResponse, DocumentUploadResponse,
    DocumentUpdate, DocumentProcessingStatus, ProcessingStatusEnum
)
from app.core.database import get_db


router = APIRouter()
logger = structlog.get_logger(__name__)


@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    上传文档
    
    - **file**: 文档文件（支持 .txt, .md, .pdf, .docx, .doc, .rtf, .html）
    
    上传的文档将自动发送到AI服务进行处理和向量化
    """
    try:
        logger.info(
            "收到文档上传请求",
            filename=file.filename,
            content_type=file.content_type,
            size=file.size if hasattr(file, 'size') else 'unknown'
        )
        
        # 读取文件内容
        file_content = await file.read()
        
        if not file_content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="上传的文件内容为空"
            )
        
        # 调用文档服务上传
        upload_response = await document_service.upload_document(
            file_content=file_content,
            filename=file.filename,
            content_type=file.content_type,
            db=db
        )
        
        logger.info(
            "文档上传成功",
            document_id=upload_response.document_id,
            filename=upload_response.filename,
            size_mb=upload_response.file_size_mb
        )
        
        return upload_response
        
    except DocumentValidationError as e:
        logger.error("文档验证失败", filename=file.filename, error=str(e), field=e.field)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文档验证失败: {str(e)}"
        )
    except DocumentServiceError as e:
        logger.error("文档服务错误", filename=file.filename, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档处理失败: {str(e)}"
        )
    except Exception as e:
        logger.error("文档上传异常", filename=file.filename, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/", response_model=Dict[str, Any])
async def list_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态过滤，多个状态用逗号分隔"),
    file_type: Optional[str] = Query(None, description="文件类型过滤，多个类型用逗号分隔"),
    search: Optional[str] = Query(None, description="搜索查询（文件名）"),
    sort_by: str = Query("upload_time", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序顺序"),
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    获取文档列表
    
    - **page**: 页码（从1开始）
    - **page_size**: 每页数量（1-100）
    - **status**: 状态过滤（pending, processing, completed, failed）
    - **file_type**: 文件类型过滤（.txt, .pdf等）
    - **search**: 搜索查询（在文件名中搜索）
    - **sort_by**: 排序字段（upload_time, filename, file_size）
    - **sort_order**: 排序顺序（asc, desc）
    """
    try:
        logger.info(
            "获取文档列表",
            page=page,
            page_size=page_size,
            status=status,
            file_type=file_type,
            search=search
        )
        
        # 解析状态过滤
        status_filter = None
        if status:
            try:
                status_list = [s.strip() for s in status.split(',')]
                status_filter = [ProcessingStatusEnum(s) for s in status_list]
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的状态值: {str(e)}"
                )
        
        # 解析文件类型过滤
        file_type_filter = None
        if file_type:
            file_type_filter = [ft.strip() for ft in file_type.split(',')]
        
        # 获取文档列表
        result = document_service.list_documents(
            page=page,
            page_size=page_size,
            status_filter=status_filter,
            file_type_filter=file_type_filter,
            search_query=search,
            sort_by=sort_by,
            sort_order=sort_order,
            db=db
        )
        
        logger.info(
            "文档列表获取成功",
            total=result["total"],
            page=page,
            returned_count=len(result["documents"])
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取文档列表失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档列表失败: {str(e)}"
        )


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: str,
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    获取特定文档详情
    
    - **document_id**: 文档ID
    """
    try:
        logger.info("获取文档详情", document_id=document_id)
        
        document = document_service.get_document(document_id, db=db)
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {document_id} 不存在"
            )
        
        logger.info(
            "文档详情获取成功",
            document_id=document_id,
            filename=document.filename,
            status=document.processing_status
        )
        
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取文档详情失败", document_id=document_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档详情失败: {str(e)}"
        )


@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: str,
    update_data: DocumentUpdate,
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    更新文档信息
    
    - **document_id**: 文档ID
    - **update_data**: 更新数据
    
    注意：只能更新filename, processing_status, chunks_count, error_message等字段
    """
    try:
        logger.info("更新文档", document_id=document_id, update_data=update_data.model_dump(exclude_unset=True))
        
        updated_document = document_service.update_document(
            document_id=document_id,
            update_data=update_data,
            db=db
        )
        
        if not updated_document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {document_id} 不存在"
            )
        
        logger.info("文档更新成功", document_id=document_id, filename=updated_document.filename)
        
        return updated_document
        
    except HTTPException:
        raise
    except DocumentServiceError as e:
        logger.error("文档更新失败", document_id=document_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档更新失败: {str(e)}"
        )
    except Exception as e:
        logger.error("文档更新异常", document_id=document_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    hard_delete: bool = Query(False, description="是否硬删除（完全删除，默认为软删除）"),
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    删除文档
    
    - **document_id**: 文档ID
    - **hard_delete**: 是否硬删除（默认为软删除，即标记为失败状态）
    
    软删除会保留文档记录但标记为删除状态，硬删除会完全移除文档记录
    """
    try:
        logger.info("删除文档", document_id=document_id, hard_delete=hard_delete)
        
        success = document_service.delete_document(
            document_id=document_id,
            soft_delete=not hard_delete,
            db=db
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {document_id} 不存在"
            )
        
        delete_type = "硬删除" if hard_delete else "软删除"
        logger.info("文档删除成功", document_id=document_id, delete_type=delete_type)
        
        return {
            "message": f"文档 {delete_type} 成功",
            "document_id": document_id,
            "hard_delete": hard_delete
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("文档删除失败", document_id=document_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档删除失败: {str(e)}"
        )


@router.get("/{document_id}/status", response_model=DocumentProcessingStatus)
async def get_document_processing_status(
    document_id: str,
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    获取文档处理状态
    
    - **document_id**: 文档ID
    
    返回文档的详细处理状态，包括进度、分块数量、错误信息等
    """
    try:
        logger.info("获取文档处理状态", document_id=document_id)
        
        processing_status = await document_service.get_processing_status(
            document_id=document_id,
            db=db
        )
        
        if not processing_status:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {document_id} 不存在"
            )
        
        logger.info(
            "文档处理状态获取成功",
            document_id=document_id,
            status=processing_status.processing_status,
            progress=processing_status.progress
        )
        
        return processing_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取文档处理状态失败", document_id=document_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档处理状态失败: {str(e)}"
        )


@router.get("/statistics/overview")
async def get_documents_statistics(
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    获取文档统计信息
    
    返回系统中文档的各种统计数据，包括：
    - 总文档数
    - 各状态分布
    - 文件类型分布
    - 总文件大小
    - 最近上传统计
    """
    try:
        logger.info("获取文档统计信息")
        
        statistics = document_service.get_statistics(db=db)
        
        logger.info(
            "文档统计信息获取成功",
            total_documents=statistics["total_documents"],
            total_size_mb=statistics["total_size_mb"]
        )
        
        return {
            "status": "success",
            "data": statistics,
            "timestamp": "2024-01-01T00:00:00"  # 可以添加实际时间戳
        }
        
    except Exception as e:
        logger.error("获取文档统计信息失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档统计信息失败: {str(e)}"
        )


@router.post("/cleanup")
async def cleanup_failed_documents(
    older_than_hours: int = Query(24, ge=1, le=168, description="清理多少小时前的失败文档"),
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    清理失败的文档记录
    
    - **older_than_hours**: 清理多少小时前的失败文档（1-168小时）
    
    这个操作会永久删除处理失败且超过指定时间的文档记录
    """
    try:
        logger.info("开始清理失败文档", older_than_hours=older_than_hours)
        
        cleaned_count = document_service.cleanup_failed_documents(
            older_than_hours=older_than_hours,
            db=db
        )
        
        logger.info("失败文档清理完成", cleaned_count=cleaned_count)
        
        return {
            "message": f"清理完成，共删除 {cleaned_count} 个失败文档",
            "cleaned_count": cleaned_count,
            "older_than_hours": older_than_hours
        }
        
    except Exception as e:
        logger.error("清理失败文档异常", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理失败文档失败: {str(e)}"
        )


@router.get("/health/check")
async def document_service_health_check(
    document_service: DocumentService = Depends(get_document_service),
    db: Session = Depends(get_db)
):
    """
    文档服务健康检查
    
    检查文档服务的各个组件状态：
    - 数据库连接
    - 文件系统访问
    - n8n服务连通性
    - 基本统计信息
    """
    try:
        health_info = {
            "service": "document_service",
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00",
            "components": {}
        }
        
        # 检查数据库连接
        try:
            stats = document_service.get_statistics(db=db)
            health_info["components"]["database"] = {
                "status": "healthy",
                "total_documents": stats["total_documents"]
            }
        except Exception as e:
            health_info["components"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_info["status"] = "degraded"
        
        # 检查上传目录
        try:
            upload_dir = document_service.upload_dir
            if upload_dir.exists() and upload_dir.is_dir():
                health_info["components"]["upload_directory"] = {
                    "status": "healthy",
                    "path": str(upload_dir),
                    "writable": os.access(upload_dir, os.W_OK)
                }
            else:
                health_info["components"]["upload_directory"] = {
                    "status": "unhealthy",
                    "error": "Upload directory does not exist"
                }
                health_info["status"] = "degraded"
        except Exception as e:
            health_info["components"]["upload_directory"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_info["status"] = "degraded"
        
        # 检查n8n服务
        try:
            n8n_health = await document_service.n8n_client.health_check()
            health_info["components"]["n8n_service"] = n8n_health
            if n8n_health.get("status") != "healthy":
                health_info["status"] = "degraded"
        except Exception as e:
            health_info["components"]["n8n_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_info["status"] = "degraded"
        
        return health_info
        
    except Exception as e:
        logger.error("文档服务健康检查失败", error=str(e))
        return {
            "service": "document_service",
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00"
        }