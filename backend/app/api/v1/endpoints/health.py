"""
健康检查相关的API端点
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db, db_manager

router = APIRouter()


@router.get("/db")
async def database_health(db: Session = Depends(get_db)):
    """数据库健康检查"""
    return db_manager.health_check()


@router.get("/system")
async def system_health():
    """系统健康检查"""
    return {
        "status": "healthy",
        "service": "AI Knowledge Chat System",
        "component": "API Server"
    }