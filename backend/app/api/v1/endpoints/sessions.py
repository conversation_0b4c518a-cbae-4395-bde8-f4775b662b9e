"""
会话管理相关的API端点
"""
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
import structlog

from app.services.chat_service import get_chat_service, ChatService
from app.schemas.chat import (
    ChatSessionResponse, ChatSessionCreate, ChatSessionUpdate,
    ChatSessionListResponse
)
from app.core.database import get_db

router = APIRouter()
logger = structlog.get_logger(__name__)


@router.get("/", response_model=ChatSessionListResponse)
async def list_sessions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    chat_service: ChatService = Depends(get_chat_service),
    db: Session = Depends(get_db)
):
    """
    获取会话列表
    
    - **page**: 页码（从1开始）
    - **page_size**: 每页数量（1-100）
    - **user_id**: 用户ID过滤（可选）
    """
    try:
        logger.info("获取会话列表", page=page, page_size=page_size, user_id=user_id)
        
        if user_id:
            # 如果指定了用户ID，使用get_user_sessions方法
            offset = (page - 1) * page_size
            sessions = chat_service.get_user_sessions(
                user_id=user_id,
                limit=page_size,
                offset=offset,
                db=db
            )
        else:
            # 如果没有指定用户ID，直接查询数据库（简化实现）
            from app.models.chat import ChatSession
            from sqlalchemy import desc
            
            offset = (page - 1) * page_size
            sessions = db.query(ChatSession).order_by(
                desc(ChatSession.updated_at)
            ).offset(offset).limit(page_size).all()
        
        # 转换为响应格式
        session_responses = [
            ChatSessionResponse(
                id=session.id,
                title=session.title,
                user_id=session.user_id,
                created_at=session.created_at,
                updated_at=session.updated_at,
                messages_count=0  # 简化实现，可以后续优化
            )
            for session in sessions
        ]
        
        # 获取总数（简化实现）
        has_next = len(sessions) == page_size
        total = len(sessions) if len(sessions) < page_size else (page * page_size + 1)
        
        response = ChatSessionListResponse(
            sessions=session_responses,
            total=total,
            page=page,
            page_size=page_size,
            has_next=has_next
        )
        
        logger.info("会话列表获取成功", total=len(sessions), page=page)
        return response
        
    except Exception as e:
        logger.error("获取会话列表失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话列表失败: {str(e)}"
        )


@router.post("/", response_model=ChatSessionResponse)
async def create_session(
    session_data: ChatSessionCreate,
    chat_service: ChatService = Depends(get_chat_service),
    db: Session = Depends(get_db)
):
    """
    创建新会话
    
    - **title**: 会话标题（可选）
    - **user_id**: 用户ID（可选）
    """
    try:
        logger.info("创建新会话", title=session_data.title, user_id=session_data.user_id)
        
        session = chat_service.create_session(
            title=session_data.title,
            user_id=session_data.user_id,
            db=db
        )
        
        response = ChatSessionResponse(
            id=session.id,
            title=session.title,
            user_id=session.user_id,
            created_at=session.created_at,
            updated_at=session.updated_at,
            messages_count=0
        )
        
        logger.info("会话创建成功", session_id=session.id, title=session.title)
        return response
        
    except Exception as e:
        logger.error("创建会话失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建会话失败: {str(e)}"
        )


@router.get("/{session_id}", response_model=ChatSessionResponse)
async def get_session(
    session_id: str,
    chat_service: ChatService = Depends(get_chat_service),
    db: Session = Depends(get_db)
):
    """
    获取特定会话详情
    
    - **session_id**: 会话ID
    """
    try:
        logger.info("获取会话详情", session_id=session_id)
        
        session = chat_service.get_session(session_id, db=db)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"会话 {session_id} 不存在"
            )
        
        response = ChatSessionResponse(
            id=session.id,
            title=session.title,
            user_id=session.user_id,
            created_at=session.created_at,
            updated_at=session.updated_at,
            messages_count=session.messages_count or 0
        )
        
        logger.info("会话详情获取成功", session_id=session_id, title=session.title)
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取会话详情失败", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话详情失败: {str(e)}"
        )


@router.put("/{session_id}", response_model=ChatSessionResponse)
async def update_session(
    session_id: str,
    update_data: ChatSessionUpdate,
    chat_service: ChatService = Depends(get_chat_service),
    db: Session = Depends(get_db)
):
    """
    更新会话信息
    
    - **session_id**: 会话ID
    - **title**: 新的会话标题（可选）
    """
    try:
        logger.info("更新会话", session_id=session_id, update_data=update_data.model_dump(exclude_unset=True))
        
        # 使用update_session_title方法
        if update_data.title is not None:
            success = chat_service.update_session_title(
                session_id=session_id,
                title=update_data.title,
                db=db
            )
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"会话 {session_id} 不存在"
                )
        
        # 获取更新后的会话
        updated_session = chat_service.get_session(session_id, db=db)
        
        if not updated_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"会话 {session_id} 不存在"
            )
        
        response = ChatSessionResponse(
            id=updated_session.id,
            title=updated_session.title,
            user_id=updated_session.user_id,
            created_at=updated_session.created_at,
            updated_at=updated_session.updated_at,
            messages_count=0  # 简化实现
        )
        
        logger.info("会话更新成功", session_id=session_id, title=updated_session.title)
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("更新会话失败", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新会话失败: {str(e)}"
        )


@router.delete("/{session_id}")
async def delete_session(
    session_id: str,
    hard_delete: bool = Query(False, description="是否硬删除（完全删除，默认为软删除）"),
    chat_service: ChatService = Depends(get_chat_service),
    db: Session = Depends(get_db)
):
    """
    删除会话
    
    - **session_id**: 会话ID
    - **hard_delete**: 是否硬删除（当前只支持硬删除）
    """
    try:
        logger.info("删除会话", session_id=session_id, hard_delete=hard_delete)
        
        # ChatService的delete_session只支持硬删除
        success = chat_service.delete_session(
            session_id=session_id,
            db=db
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"会话 {session_id} 不存在"
            )
        
        logger.info("会话删除成功", session_id=session_id)
        
        return {
            "message": f"会话 {session_id} 删除成功",
            "session_id": session_id,
            "hard_delete": True  # 当前只支持硬删除
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("删除会话失败", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除会话失败: {str(e)}"
        )


@router.get("/{session_id}/summary")
async def get_session_summary(
    session_id: str,
    chat_service: ChatService = Depends(get_chat_service),
    db: Session = Depends(get_db)
):
    """
    获取会话摘要信息
    
    - **session_id**: 会话ID
    """
    try:
        logger.info("获取会话摘要", session_id=session_id)
        
        session = chat_service.get_session(session_id, db=db)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"会话 {session_id} 不存在"
            )
        
        # 这里可以实现更复杂的摘要逻辑
        summary = {
            "session_id": session.id,
            "title": session.title,
            "user_id": session.user_id,
            "created_at": session.created_at,
            "updated_at": session.updated_at,
            "messages_count": session.messages_count or 0,
            "status": "active" if session.created_at else "inactive"
        }
        
        logger.info("会话摘要获取成功", session_id=session_id)
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("获取会话摘要失败", session_id=session_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话摘要失败: {str(e)}"
        )