"""
应用配置设置
"""
import os
from typing import Any, Dict, Optional, Union

from pydantic_settings import BaseSettings
from pydantic import field_validator


class Settings(BaseSettings):
    """应用配置类"""
    
    model_config = {
        "case_sensitive": True,
        "extra": "ignore"  # 允许额外的环境变量
    }
    
    # API基本配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "AI Knowledge Chat System"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///dbfile/aichatdb.db"  # 使用相对于项目根目录的路径
    
    def __init__(self, **kwargs):
        """初始化配置，动态设置数据库路径"""
        super().__init__(**kwargs)
        # 确保数据库路径是绝对路径
        import os
        # 从当前文件位置计算项目根目录: backend/app/core/config.py -> 项目根目录
        current_file = os.path.abspath(__file__)  # 获取当前文件的绝对路径
        backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))  # backend目录
        project_root = os.path.dirname(backend_dir)  # 项目根目录
        db_path = os.path.join(project_root, "dbfile", "aichatdb.db")
        self.DATABASE_URL = f"sqlite:///{db_path}"
    
    # CORS配置
    BACKEND_CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
    ]
    
    @field_validator("BACKEND_CORS_ORIGINS", mode='before')
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, list]) -> list:
        """处理CORS源列表"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        raise ValueError(v)
    
    # n8n集成配置
    N8N_BASE_URL: str = "http://localhost:5678"
    N8N_WEBHOOK_RAG_UPLOAD: str = "/webhook/rag-upload"
    N8N_WEBHOOK_RAG_QUERY: str = "/webhook/rag-query"
    
    @property
    def n8n_rag_upload_url(self) -> str:
        """获取n8n文档上传URL"""
        return f"{self.N8N_BASE_URL}{self.N8N_WEBHOOK_RAG_UPLOAD}"
    
    @property
    def n8n_rag_query_url(self) -> str:
        """获取n8n查询URL"""
        return f"{self.N8N_BASE_URL}{self.N8N_WEBHOOK_RAG_QUERY}"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # 会话配置
    SESSION_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7天
    SESSION_SECRET_KEY: str = "your-secret-key-here"  # 在生产环境中应该从环境变量读取
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: list = [".txt", ".md", ".pdf", ".docx", ".doc"]
    UPLOAD_DIR: str = "./uploads"
    
    # API超时配置
    API_TIMEOUT: int = 30


# 创建全局设置实例
settings = Settings()


def get_settings() -> Settings:
    """获取设置实例"""
    return settings