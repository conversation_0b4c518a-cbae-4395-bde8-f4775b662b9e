"""
数据库配置和连接管理
"""
import os
from typing import Generator

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

from app.core.config import settings

# 创建基础模型类
Base = declarative_base()

# 元数据对象，用于表结构管理
metadata = MetaData()

# 同步数据库引擎 (用于SQLite)
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    connect_args={
        "check_same_thread": False,  # SQLite特定配置
        "timeout": 30,  # 设置超时时间
        "isolation_level": None  # 启用autocommit模式，避免长时间锁定
    },
    pool_pre_ping=True,  # 连接前检查连接是否有效
    pool_recycle=3600,   # 1小时后回收连接
)

# 同步会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖函数
    用于FastAPI的依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有数据库表"""
    # 导入所有模型以确保它们被注册到Base.metadata
    from app.models import ChatSession, ChatMessage, Document
    
    Base.metadata.create_all(bind=engine)


def check_database_connection() -> bool:
    """
    检查数据库连接是否正常
    
    Returns:
        bool: 连接成功返回True，失败返回False
    """
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return True
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False


def ensure_database_exists():
    """确保数据库文件存在并且可以连接"""
    # 确保数据库目录存在
    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    db_dir = os.path.dirname(db_path)

    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)

    # 创建数据库表
    try:
        create_tables()

        # 启用WAL模式和其他优化设置
        optimize_sqlite_settings()

        print(f"数据库初始化成功: {db_path}")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise


def optimize_sqlite_settings():
    """优化SQLite设置以提高并发性能"""
    try:
        with engine.connect() as connection:
            # 启用WAL模式（Write-Ahead Logging）提高并发性能
            connection.execute(text("PRAGMA journal_mode=WAL"))

            # 设置同步模式为NORMAL（平衡性能和安全性）
            connection.execute(text("PRAGMA synchronous=NORMAL"))

            # 设置缓存大小（以页为单位，每页通常4KB）
            connection.execute(text("PRAGMA cache_size=10000"))

            # 设置临时存储在内存中
            connection.execute(text("PRAGMA temp_store=memory"))

            # 设置忙等待超时（毫秒）
            connection.execute(text("PRAGMA busy_timeout=30000"))

            # 启用外键约束
            connection.execute(text("PRAGMA foreign_keys=ON"))

            connection.commit()

        print("SQLite优化设置已应用")
    except Exception as e:
        print(f"SQLite优化设置失败: {e}")
        # 不抛出异常，因为这些是优化设置，不是必需的


def validate_database_schema():
    """验证数据库Schema是否与模型匹配"""
    from app.models import ChatSession, ChatMessage, Document
    
    try:
        with engine.connect() as connection:
            # 检查必要的表是否存在
            tables = connection.execute(text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
            table_names = [table[0] for table in tables]
            
            required_tables = ['chat_sessions', 'chat_messages', 'documents']
            missing_tables = [table for table in required_tables if table not in table_names]
            
            if missing_tables:
                print(f"警告：缺少数据库表: {missing_tables}")
                return False
            
            print("数据库Schema验证通过")
            return True
            
    except Exception as e:
        print(f"数据库Schema验证失败: {e}")
        return False


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def close_session(self, session: Session):
        """关闭数据库会话"""
        session.close()
    
    def health_check(self) -> dict:
        """数据库健康检查"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("SELECT 1")).scalar()
                schema_valid = validate_database_schema()
                return {
                    "status": "healthy" if schema_valid else "degraded",
                    "database": "connected",
                    "schema_valid": schema_valid,
                    "result": result
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e)
            }


# 创建全局数据库管理器实例
db_manager = DatabaseManager()