"""
数据库配置和连接管理
"""
import os
from typing import Generator

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

from app.core.config import settings

# 创建基础模型类
Base = declarative_base()

# 元数据对象，用于表结构管理
metadata = MetaData()

# 同步数据库引擎 (用于PostgreSQL)
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_size=20,        # 连接池大小
    max_overflow=30,     # 连接池溢出大小
    pool_pre_ping=True,  # 连接前检查连接是否有效
    pool_recycle=3600,   # 1小时后回收连接
    connect_args={
        "connect_timeout": 30,  # 连接超时
        "application_name": "AIKnowledge_Backend"  # 应用名称，便于监控
    }
)

# 同步会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖函数
    用于FastAPI的依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有数据库表"""
    # 导入所有模型以确保它们被注册到Base.metadata
    from app.models import ChatSession, ChatMessage, Document
    
    Base.metadata.create_all(bind=engine)


def check_database_connection() -> bool:
    """
    检查数据库连接是否正常
    
    Returns:
        bool: 连接成功返回True，失败返回False
    """
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return True
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False


def ensure_database_exists():
    """确保数据库连接正常并创建表"""
    try:
        # 创建数据库表
        create_tables()

        # 应用PostgreSQL优化设置
        optimize_postgresql_settings()

        print(f"数据库初始化成功: {settings.DATABASE_URL}")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise


def optimize_postgresql_settings():
    """优化PostgreSQL设置以提高性能"""
    try:
        with engine.connect() as connection:
            # 设置连接的一些优化参数
            # 这些设置只对当前连接有效，不会影响全局设置

            # 设置工作内存（用于排序和哈希操作）
            connection.execute(text("SET work_mem = '16MB'"))

            # 设置维护工作内存（用于VACUUM、CREATE INDEX等操作）
            connection.execute(text("SET maintenance_work_mem = '64MB'"))

            # 设置随机页面成本（SSD优化）
            connection.execute(text("SET random_page_cost = 1.1"))

            # 启用并行查询（如果支持）
            connection.execute(text("SET max_parallel_workers_per_gather = 2"))

            connection.commit()

        print("PostgreSQL优化设置已应用")
    except Exception as e:
        print(f"PostgreSQL优化设置失败: {e}")
        # 不抛出异常，因为这些是优化设置，不是必需的


def validate_database_schema():
    """验证数据库Schema是否与模型匹配"""
    from app.models import ChatSession, ChatMessage, Document

    try:
        with engine.connect() as connection:
            # 检查必要的表是否存在（PostgreSQL查询）
            tables = connection.execute(text(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
            )).fetchall()
            table_names = [table[0] for table in tables]

            required_tables = ['chat_sessions', 'chat_messages', 'documents']
            missing_tables = [table for table in required_tables if table not in table_names]

            if missing_tables:
                print(f"警告：缺少数据库表: {missing_tables}")
                return False

            print("数据库Schema验证通过")
            return True

    except Exception as e:
        print(f"数据库Schema验证失败: {e}")
        return False


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def close_session(self, session: Session):
        """关闭数据库会话"""
        session.close()
    
    def health_check(self) -> dict:
        """数据库健康检查"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("SELECT 1")).scalar()
                schema_valid = validate_database_schema()
                return {
                    "status": "healthy" if schema_valid else "degraded",
                    "database": "connected",
                    "schema_valid": schema_valid,
                    "result": result
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e)
            }


# 创建全局数据库管理器实例
db_manager = DatabaseManager()