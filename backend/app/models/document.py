"""
文档相关的数据库模型
"""
import uuid
from datetime import datetime
from typing import Optional
from enum import Enum

from sqlalchemy import Column, String, Text, DateTime, Integer
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class ProcessingStatus(Enum):
    """文档处理状态枚举"""
    PENDING = "pending"      # 等待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 处理完成
    FAILED = "failed"        # 处理失败


class Document(Base):
    """文档模型"""
    __tablename__ = "documents"
    
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_type: Mapped[str] = mapped_column(String(50), nullable=False)  # 文件类型 (pdf, txt, md, etc.)
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)  # 文件大小(字节)
    upload_time: Mapped[Optional[datetime]] = mapped_column(DateTime, default=datetime.utcnow)
    processing_status: Mapped[Optional[str]] = mapped_column(String(20), default=ProcessingStatus.PENDING.value)
    chunks_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, default=0)  # 分块数量
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 错误信息
    
    def __repr__(self) -> str:
        return f"<Document(id={self.id}, filename={self.filename}, status={self.processing_status})>"
    
    @property
    def file_size_mb(self) -> float:
        """获取文件大小(MB)"""
        return round(self.file_size / (1024 * 1024), 2)
    
    @property
    def is_processed(self) -> bool:
        """检查文档是否已处理完成"""
        return self.processing_status == ProcessingStatus.COMPLETED.value
    
    @property
    def is_processing(self) -> bool:
        """检查文档是否正在处理"""
        return self.processing_status == ProcessingStatus.PROCESSING.value
    
    @property
    def has_error(self) -> bool:
        """检查文档是否处理失败"""
        return self.processing_status == ProcessingStatus.FAILED.value