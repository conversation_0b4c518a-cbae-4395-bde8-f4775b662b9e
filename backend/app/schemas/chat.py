"""
聊天相关的Pydantic Schema
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
import uuid

from pydantic import BaseModel, Field, ConfigDict, field_validator


class MessageRole(str, Enum):
    """消息角色枚举"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


# ChatSession Schemas
class ChatSessionBase(BaseModel):
    """聊天会话基础Schema"""
    title: Optional[str] = Field(None, max_length=255, description="会话标题")
    user_id: Optional[str] = Field(None, max_length=36, description="用户ID")


class ChatSessionCreate(ChatSessionBase):
    """创建聊天会话的请求Schema"""
    pass


class ChatSessionUpdate(BaseModel):
    """更新聊天会话的请求Schema"""
    title: Optional[str] = Field(None, max_length=255, description="会话标题")


class ChatSessionResponse(ChatSessionBase):
    """聊天会话响应Schema"""
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(..., description="会话ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    messages_count: Optional[int] = Field(0, description="消息数量")

    @field_validator('id', mode='before')
    @classmethod
    def validate_id(cls, v):
        """将UUID对象转换为字符串"""
        if isinstance(v, uuid.UUID):
            return str(v)
        return v


# ChatMessage Schemas
class ChatMessageBase(BaseModel):
    """聊天消息基础Schema"""
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., min_length=1, description="消息内容")
    sources: Optional[Dict[str, Any]] = Field(None, description="引用的文档来源")


class ChatMessageCreate(ChatMessageBase):
    """创建聊天消息的请求Schema"""
    session_id: str = Field(..., max_length=36, description="会话ID")


class ChatMessageResponse(ChatMessageBase):
    """聊天消息响应Schema"""
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(..., description="消息ID")
    session_id: str = Field(..., description="会话ID")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    content_preview: Optional[str] = Field(None, description="内容预览")

    @field_validator('id', 'session_id', mode='before')
    @classmethod
    def validate_uuid_fields(cls, v):
        """将UUID对象转换为字符串"""
        if isinstance(v, uuid.UUID):
            return str(v)
        return v


# 组合 Schemas
class ChatHistoryResponse(BaseModel):
    """聊天历史响应Schema"""
    session: ChatSessionResponse = Field(..., description="会话信息")
    messages: List[ChatMessageResponse] = Field(..., description="消息列表")


class ChatSessionWithMessages(ChatSessionResponse):
    """包含消息的会话响应Schema"""
    messages: List[ChatMessageResponse] = Field([], description="会话消息列表")


# 聊天请求/响应 Schemas
class ChatRequest(BaseModel):
    """聊天请求Schema"""
    message: str = Field(..., min_length=1, max_length=10000, description="用户消息")
    session_id: Optional[str] = Field(None, max_length=36, description="会话ID，不提供则创建新会话")
    stream: bool = Field(False, description="是否流式响应")


class ChatResponse(BaseModel):
    """聊天响应Schema"""
    message: str = Field(..., description="AI回复内容")
    session_id: str = Field(..., description="会话ID")
    message_id: str = Field(..., description="消息ID")
    sources: Optional[List[Dict[str, Any]]] = Field(None, description="引用的文档来源")
    created_at: datetime = Field(..., description="回复时间")

    @field_validator('session_id', 'message_id', mode='before')
    @classmethod
    def validate_uuid_fields(cls, v):
        """将UUID对象转换为字符串"""
        if isinstance(v, uuid.UUID):
            return str(v)
        return v


class StreamChatResponse(BaseModel):
    """流式聊天响应Schema"""
    chunk: str = Field(..., description="文本块")
    session_id: str = Field(..., description="会话ID")
    message_id: Optional[str] = Field(None, description="消息ID")
    is_complete: bool = Field(False, description="是否为完整响应的最后一块")
    sources: Optional[List[Dict[str, Any]]] = Field(None, description="引用的文档来源")

    @field_validator('session_id', 'message_id', mode='before')
    @classmethod
    def validate_uuid_fields(cls, v):
        """将UUID对象转换为字符串"""
        if isinstance(v, uuid.UUID):
            return str(v)
        return v


# 会话列表 Schema
class ChatSessionListResponse(BaseModel):
    """会话列表响应Schema"""
    sessions: List[ChatSessionResponse] = Field(..., description="会话列表")
    total: int = Field(..., description="总数量")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(10, description="每页数量")
    has_next: bool = Field(False, description="是否有下一页")