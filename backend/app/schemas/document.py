"""
文档相关的Pydantic Schema
"""
from datetime import datetime
from typing import Optional, List
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict


class ProcessingStatusEnum(str, Enum):
    """文档处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


# Document Base Schema
class DocumentBase(BaseModel):
    """文档基础Schema"""
    filename: str = Field(..., max_length=255, description="文件名")
    file_type: str = Field(..., max_length=50, description="文件类型")
    file_size: int = Field(..., gt=0, description="文件大小(字节)")


class DocumentCreate(DocumentBase):
    """创建文档的请求Schema"""
    pass


class DocumentUpdate(BaseModel):
    """更新文档的请求Schema"""
    filename: Optional[str] = Field(None, max_length=255, description="文件名")
    processing_status: Optional[ProcessingStatusEnum] = Field(None, description="处理状态")
    chunks_count: Optional[int] = Field(None, ge=0, description="分块数量")
    error_message: Optional[str] = Field(None, description="错误信息")


class DocumentResponse(DocumentBase):
    """文档响应Schema"""
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="文档ID")
    upload_time: Optional[datetime] = Field(None, description="上传时间")
    processing_status: Optional[str] = Field(None, description="处理状态")
    chunks_count: Optional[int] = Field(None, description="分块数量")
    error_message: Optional[str] = Field(None, description="错误信息")
    file_size_mb: Optional[float] = Field(None, description="文件大小(MB)")
    is_processed: Optional[bool] = Field(None, description="是否已处理完成")
    is_processing: Optional[bool] = Field(None, description="是否正在处理")
    has_error: Optional[bool] = Field(None, description="是否有错误")


class DocumentListResponse(BaseModel):
    """文档列表响应Schema"""
    documents: List[DocumentResponse] = Field(..., description="文档列表")
    total: int = Field(..., description="总数量")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(10, description="每页数量")


# 文档上传相关 Schemas
class DocumentUploadResponse(BaseModel):
    """文档上传响应Schema"""
    document_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    file_size_mb: float = Field(..., description="文件大小(MB)")
    upload_time: datetime = Field(..., description="上传时间")
    processing_status: ProcessingStatusEnum = Field(..., description="处理状态")
    message: str = Field(..., description="上传结果消息")


class DocumentProcessingStatus(BaseModel):
    """文档处理状态Schema"""
    document_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    processing_status: ProcessingStatusEnum = Field(..., description="处理状态")
    progress: Optional[float] = Field(None, ge=0, le=100, description="处理进度百分比")
    chunks_count: Optional[int] = Field(None, description="已处理的分块数量")
    error_message: Optional[str] = Field(None, description="错误信息")
    estimated_time_remaining: Optional[int] = Field(None, description="预计剩余时间(秒)")


# 文档搜索/查询 Schemas
class DocumentSearchRequest(BaseModel):
    """文档搜索请求Schema"""
    query: str = Field(..., min_length=1, max_length=1000, description="搜索查询")
    document_ids: Optional[List[str]] = Field(None, description="限制搜索的文档ID列表")
    limit: int = Field(10, ge=1, le=100, description="返回结果数量限制")


class DocumentSearchResult(BaseModel):
    """文档搜索结果Schema"""
    document_id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    chunk_content: str = Field(..., description="匹配的文档片段")
    relevance_score: float = Field(..., ge=0, le=1, description="相关性分数")
    page_number: Optional[int] = Field(None, description="页码(如果适用)")


class DocumentSearchResponse(BaseModel):
    """文档搜索响应Schema"""
    query: str = Field(..., description="搜索查询")
    results: List[DocumentSearchResult] = Field(..., description="搜索结果")
    total_results: int = Field(..., description="总结果数量")
    search_time_ms: float = Field(..., description="搜索耗时(毫秒)")