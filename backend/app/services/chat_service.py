"""
聊天会话管理服务
"""
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.core.database import get_db, SessionLocal
from app.models.chat import ChatSession, ChatMessage
from app.core.config import settings


class ChatService:
    """聊天会话管理服务"""
    
    def __init__(self):
        self._executor = ThreadPoolExecutor(max_workers=4)  # 用于并发处理
        self._session_cache: Dict[str, Dict[str, Any]] = {}  # 会话缓存
        
    def create_session(
        self,
        user_id: Optional[str] = None,
        title: Optional[str] = None,
        db: Optional[Session] = None
    ) -> ChatSession:
        """
        创建新的聊天会话
        
        Args:
            user_id: 用户ID（可选）
            title: 会话标题（可选）
            db: 数据库会话（可选）
            
        Returns:
            ChatSession: 新创建的会话对象
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            # 生成唯一会话ID
            session_id = str(uuid.uuid4())
            
            # 如果没有提供标题，生成默认标题
            if not title:
                title = self._generate_default_title()
            
            # 创建会话
            session = ChatSession(
                id=session_id,
                user_id=user_id,
                title=title,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(session)
            db.commit()
            db.refresh(session)
            
            # 初始化会话缓存
            self._init_session_cache(session_id)
            
            return session
            
        except Exception as e:
            db.rollback()
            raise Exception(f"创建会话失败: {str(e)}")
        finally:
            if should_close_db:
                db.close()
    
    def get_session(
        self,
        session_id: str,
        db: Optional[Session] = None
    ) -> Optional[ChatSession]:
        """
        根据ID获取会话
        
        Args:
            session_id: 会话ID
            db: 数据库会话（可选）
            
        Returns:
            ChatSession: 会话对象，如果不存在则返回None
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            session = db.query(ChatSession).filter(
                ChatSession.id == session_id
            ).first()
            
            if session:
                # 更新最后访问时间
                session.updated_at = datetime.utcnow()
                db.commit()
                
                # 确保缓存存在
                self._ensure_session_cache(session_id)
            
            return session
            
        finally:
            if should_close_db:
                db.close()
    
    def get_user_sessions(
        self,
        user_id: str,
        limit: int = 50,
        offset: int = 0,
        db: Optional[Session] = None
    ) -> List[ChatSession]:
        """
        获取用户的所有会话
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            offset: 偏移量
            db: 数据库会话（可选）
            
        Returns:
            List[ChatSession]: 会话列表
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            sessions = db.query(ChatSession).filter(
                ChatSession.user_id == user_id
            ).order_by(desc(ChatSession.updated_at)).offset(offset).limit(limit).all()
            
            return sessions
            
        finally:
            if should_close_db:
                db.close()
    
    def update_session_title(
        self,
        session_id: str,
        title: str,
        db: Optional[Session] = None
    ) -> bool:
        """
        更新会话标题
        
        Args:
            session_id: 会话ID
            title: 新标题
            db: 数据库会话（可选）
            
        Returns:
            bool: 是否更新成功
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            session = db.query(ChatSession).filter(
                ChatSession.id == session_id
            ).first()
            
            if not session:
                return False
            
            session.title = title
            session.updated_at = datetime.utcnow()
            db.commit()
            
            return True
            
        except Exception:
            db.rollback()
            return False
        finally:
            if should_close_db:
                db.close()
    
    def delete_session(
        self,
        session_id: str,
        db: Optional[Session] = None
    ) -> bool:
        """
        删除会话及其所有消息
        
        Args:
            session_id: 会话ID
            db: 数据库会话（可选）
            
        Returns:
            bool: 是否删除成功
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            session = db.query(ChatSession).filter(
                ChatSession.id == session_id
            ).first()
            
            if not session:
                return False
            
            # 删除会话（级联删除消息）
            db.delete(session)
            db.commit()
            
            # 清理缓存
            self._clear_session_cache(session_id)
            
            return True
            
        except Exception:
            db.rollback()
            return False
        finally:
            if should_close_db:
                db.close()
    
    def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        sources: Optional[Dict[str, Any]] = None,
        db: Optional[Session] = None
    ) -> Optional[ChatMessage]:
        """
        向会话添加消息
        
        Args:
            session_id: 会话ID
            role: 消息角色 ('user', 'assistant', 'system')
            content: 消息内容
            sources: 引用的文档来源（可选）
            db: 数据库会话（可选）
            
        Returns:
            ChatMessage: 新创建的消息对象
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            # 验证会话存在
            session = db.query(ChatSession).filter(
                ChatSession.id == session_id
            ).first()
            
            if not session:
                raise ValueError(f"会话 {session_id} 不存在")
            
            # 创建消息
            message = ChatMessage(
                id=str(uuid.uuid4()),
                session_id=session_id,
                role=role,
                content=content,
                sources=sources,
                created_at=datetime.utcnow()
            )
            
            db.add(message)
            
            # 更新会话时间
            session.updated_at = datetime.utcnow()
            
            # 如果是第一条消息且会话没有标题，自动生成标题
            if not session.title or session.title.startswith("新对话"):
                if role == "user":
                    session.title = self._generate_title_from_content(content)
            
            db.commit()
            db.refresh(message)
            
            # 更新会话上下文缓存
            self._update_session_context(session_id, message)
            
            return message
            
        except Exception as e:
            db.rollback()
            raise Exception(f"添加消息失败: {str(e)}")
        finally:
            if should_close_db:
                db.close()
    
    def get_session_messages(
        self,
        session_id: str,
        limit: Optional[int] = None,
        db: Optional[Session] = None
    ) -> List[ChatMessage]:
        """
        获取会话的所有消息
        
        Args:
            session_id: 会话ID
            limit: 消息数量限制
            db: 数据库会话（可选）
            
        Returns:
            List[ChatMessage]: 消息列表
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            query = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id
            ).order_by(ChatMessage.created_at)
            
            if limit:
                query = query.limit(limit)
            
            return query.all()
            
        finally:
            if should_close_db:
                db.close()
    
    def get_session_context(
        self,
        session_id: str,
        max_messages: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取会话上下文（用于n8n API调用）
        
        Args:
            session_id: 会话ID
            max_messages: 最大消息数量
            
        Returns:
            List[Dict]: 格式化的消息上下文
        """
        # 先从缓存获取
        if session_id in self._session_cache:
            cached_context = self._session_cache[session_id].get("context", [])
            if cached_context:
                return cached_context[-max_messages:]
        
        # 从数据库获取
        db = SessionLocal()
        try:
            messages = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id
            ).order_by(desc(ChatMessage.created_at)).limit(max_messages).all()
            
            # 格式化消息
            context = []
            for message in reversed(messages):  # 按时间正序排列
                context.append({
                    "role": message.role,
                    "content": message.content,
                    "timestamp": message.created_at.isoformat() if message.created_at else None,
                    "sources": message.sources
                })
            
            # 更新缓存
            self._update_context_cache(session_id, context)
            
            return context
            
        finally:
            db.close()
    
    def cleanup_expired_sessions(
        self,
        hours: int = 24 * 7,  # 默认7天
        db: Optional[Session] = None
    ) -> int:
        """
        清理过期的会话
        
        Args:
            hours: 过期时间（小时）
            db: 数据库会话（可选）
            
        Returns:
            int: 删除的会话数量
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            expiry_time = datetime.utcnow() - timedelta(hours=hours)
            
            # 查找过期会话
            expired_sessions = db.query(ChatSession).filter(
                ChatSession.updated_at < expiry_time
            ).all()
            
            session_ids = [session.id for session in expired_sessions]
            deleted_count = len(session_ids)
            
            # 删除过期会话
            db.query(ChatSession).filter(
                ChatSession.updated_at < expiry_time
            ).delete()
            
            db.commit()
            
            # 清理缓存
            for session_id in session_ids:
                self._clear_session_cache(session_id)
            
            return deleted_count
            
        except Exception:
            db.rollback()
            return 0
        finally:
            if should_close_db:
                db.close()
    
    def get_session_stats(
        self,
        session_id: str,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        获取会话统计信息
        
        Args:
            session_id: 会话ID
            db: 数据库会话（可选）
            
        Returns:
            Dict: 会话统计信息
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            session = db.query(ChatSession).filter(
                ChatSession.id == session_id
            ).first()
            
            if not session:
                return {}
            
            # 统计消息数量
            message_counts = db.query(
                ChatMessage.role,
                func.count(ChatMessage.id)
            ).filter(
                ChatMessage.session_id == session_id
            ).group_by(ChatMessage.role).all()
            
            total_messages = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id
            ).count()
            
            # 计算会话时长
            first_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id
            ).order_by(ChatMessage.created_at).first()
            
            last_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id
            ).order_by(desc(ChatMessage.created_at)).first()
            
            duration = None
            if first_message and last_message and first_message.created_at and last_message.created_at:
                duration = (last_message.created_at - first_message.created_at).total_seconds()
            
            return {
                "session_id": session_id,
                "title": session.title,
                "created_at": session.created_at.isoformat() if session.created_at else None,
                "updated_at": session.updated_at.isoformat() if session.updated_at else None,
                "total_messages": total_messages,
                "message_counts": {role: count for role, count in message_counts},
                "duration_seconds": duration
            }
            
        finally:
            if should_close_db:
                db.close()
    
    # 私有方法
    def _generate_default_title(self) -> str:
        """生成默认会话标题"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        return f"新对话 - {timestamp}"
    
    def _generate_title_from_content(self, content: str, max_length: int = 30) -> str:
        """根据内容生成会话标题"""
        if not content:
            return self._generate_default_title()
        
        # 清理内容并截取
        clean_content = content.strip().replace('\n', ' ').replace('\r', ' ')
        if len(clean_content) <= max_length:
            return clean_content
        
        return clean_content[:max_length] + "..."
    
    def _init_session_cache(self, session_id: str) -> None:
        """初始化会话缓存"""
        self._session_cache[session_id] = {
            "context": [],
            "last_updated": datetime.utcnow()
        }
    
    def _ensure_session_cache(self, session_id: str) -> None:
        """确保会话缓存存在"""
        if session_id not in self._session_cache:
            self._init_session_cache(session_id)
    
    def _update_session_context(self, session_id: str, message: ChatMessage) -> None:
        """更新会话上下文缓存"""
        self._ensure_session_cache(session_id)
        
        context_item = {
            "role": message.role,
            "content": message.content,
            "timestamp": message.created_at.isoformat() if message.created_at else None,
            "sources": message.sources
        }
        
        self._session_cache[session_id]["context"].append(context_item)
        self._session_cache[session_id]["last_updated"] = datetime.utcnow()
        
        # 限制缓存大小
        max_context_length = 20
        if len(self._session_cache[session_id]["context"]) > max_context_length:
            self._session_cache[session_id]["context"] = \
                self._session_cache[session_id]["context"][-max_context_length:]
    
    def _update_context_cache(self, session_id: str, context: List[Dict[str, Any]]) -> None:
        """更新上下文缓存"""
        self._ensure_session_cache(session_id)
        self._session_cache[session_id]["context"] = context
        self._session_cache[session_id]["last_updated"] = datetime.utcnow()
    
    def _clear_session_cache(self, session_id: str) -> None:
        """清理会话缓存"""
        if session_id in self._session_cache:
            del self._session_cache[session_id]
    
    def clear_all_cache(self) -> None:
        """清理所有缓存"""
        self._session_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cached_sessions": len(self._session_cache),
            "total_cached_messages": sum(
                len(cache["context"]) for cache in self._session_cache.values()
            )
        }


# 创建全局服务实例
chat_service = ChatService()


def get_chat_service() -> ChatService:
    """获取聊天服务实例"""
    return chat_service 