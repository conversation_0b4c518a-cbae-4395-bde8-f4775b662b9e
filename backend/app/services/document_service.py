"""
文档管理服务
负责文档上传、存储、查询、状态跟踪和删除等核心功能
"""

import os
import uuid
import mimetypes
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple, BinaryIO
from pathlib import Path
import structlog

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, or_, and_

from app.core.database import SessionLocal
from app.core.config import settings
from app.models.document import Document, ProcessingStatus
from app.schemas.document import (
    DocumentCreate, DocumentUpdate, DocumentResponse,
    DocumentUploadResponse, DocumentProcessingStatus,
    DocumentSearchRequest, DocumentSearchResult, DocumentSearchResponse,
    ProcessingStatusEnum
)
from app.services.n8n_client import N8nClient, get_n8n_client, N8nAPIError


logger = structlog.get_logger(__name__)


class DocumentValidationError(Exception):
    """文档验证相关错误"""
    def __init__(self, message: str, field: str = None):
        super().__init__(message)
        self.field = field


class DocumentServiceError(Exception):
    """文档服务相关错误"""
    def __init__(self, message: str, error_type: str = "unknown"):
        super().__init__(message)
        self.error_type = error_type


class DocumentService:
    """文档管理服务类"""
    
    def __init__(
        self,
        n8n_client: Optional[N8nClient] = None,
        upload_dir: Optional[str] = None
    ):
        """
        初始化文档服务
        
        Args:
            n8n_client: n8n客户端实例
            upload_dir: 文档上传目录
        """
        self.n8n_client = n8n_client or get_n8n_client()
        self.upload_dir = Path(upload_dir or settings.UPLOAD_DIR)
        
        # 确保上传目录存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的文件类型和对应的MIME类型
        self.supported_types = {
            '.txt': 'text/plain',
            '.md': 'text/markdown', 
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.rtf': 'application/rtf',
            '.html': 'text/html',
            '.htm': 'text/html'
        }
        
        logger.info(
            "文档服务初始化",
            upload_dir=str(self.upload_dir),
            supported_types=list(self.supported_types.keys())
        )
    
    def validate_file(
        self,
        filename: str,
        file_size: int,
        content_type: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        验证文件
        
        Args:
            filename: 文件名
            file_size: 文件大小(字节)
            content_type: 文件MIME类型
            
        Returns:
            Tuple[file_type, mime_type]: 文件类型和MIME类型
            
        Raises:
            DocumentValidationError: 验证失败时抛出
        """
        # 验证文件名
        if not filename or not filename.strip():
            raise DocumentValidationError("文件名不能为空", "filename")
        
        # 获取文件扩展名
        file_path = Path(filename)
        file_ext = file_path.suffix.lower()
        
        # 验证文件类型
        if file_ext not in self.supported_types:
            supported_exts = ', '.join(self.supported_types.keys())
            raise DocumentValidationError(
                f"不支持的文件类型 {file_ext}，支持的类型: {supported_exts}",
                "file_type"
            )
        
        # 验证文件大小
        if file_size <= 0:
            raise DocumentValidationError("文件大小必须大于0", "file_size")
        
        max_size = settings.MAX_FILE_SIZE
        if file_size > max_size:
            max_size_mb = max_size / (1024 * 1024)
            current_size_mb = file_size / (1024 * 1024)
            raise DocumentValidationError(
                f"文件大小 {current_size_mb:.2f}MB 超过限制 {max_size_mb:.2f}MB",
                "file_size"
            )
        
        # 确定MIME类型
        expected_mime = self.supported_types[file_ext]
        if content_type and content_type != expected_mime:
            # 尝试通过文件名推断
            guessed_type, _ = mimetypes.guess_type(filename)
            if guessed_type and guessed_type != expected_mime:
                logger.warning(
                    "MIME类型不匹配",
                    filename=filename,
                    provided_type=content_type,
                    expected_type=expected_mime,
                    guessed_type=guessed_type
                )
        
        logger.debug(
            "文件验证通过",
            filename=filename,
            file_type=file_ext,
            file_size=file_size,
            mime_type=expected_mime
        )
        
        return file_ext, expected_mime
    
    async def upload_document(
        self,
        file_content: bytes,
        filename: str,
        content_type: Optional[str] = None,
        db: Optional[Session] = None
    ) -> DocumentUploadResponse:
        """
        上传文档
        
        Args:
            file_content: 文件内容
            filename: 文件名
            content_type: 文件MIME类型
            db: 数据库会话
            
        Returns:
            DocumentUploadResponse: 上传结果
            
        Raises:
            DocumentValidationError: 文件验证失败
            DocumentServiceError: 上传处理失败
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            logger.info("开始上传文档", filename=filename, size=len(file_content))
            
            # 验证文件
            file_type, mime_type = self.validate_file(filename, len(file_content), content_type)
            
            # 生成唯一文档ID
            document_id = str(uuid.uuid4())
            
            # 创建文档记录
            document = Document(
                id=document_id,
                filename=filename,
                file_type=file_type,
                file_size=len(file_content),
                upload_time=datetime.utcnow(),
                processing_status=ProcessingStatus.PENDING.value,
                chunks_count=0
            )
            
            db.add(document)
            db.commit()
            db.refresh(document)
            
            logger.info("文档记录已创建", document_id=document_id, filename=filename)
            
            # 异步处理：上传到n8n进行AI处理
            try:
                # 更新状态为处理中
                await self._update_document_status(
                    document_id, ProcessingStatus.PROCESSING, db=db
                )
                
                # 调用n8n API上传文档
                n8n_response = await self.n8n_client.upload_document(
                    file_content=file_content,
                    filename=filename,
                    content_type=mime_type
                )
                
                # 更新处理结果
                if n8n_response and hasattr(n8n_response, 'processing_status'):
                    await self._update_document_processing_result(
                        document_id, n8n_response, db=db
                    )
                else:
                    # 如果n8n没有返回状态，标记为完成
                    await self._update_document_status(
                        document_id, ProcessingStatus.COMPLETED, db=db
                    )
                
                logger.info(
                    "文档上传和处理完成",
                    document_id=document_id,
                    filename=filename
                )
                
            except N8nAPIError as e:
                logger.error("n8n文档处理失败", document_id=document_id, error=str(e))
                await self._update_document_status(
                    document_id, ProcessingStatus.FAILED, error_message=str(e), db=db
                )
                # 不抛出异常，允许文档记录保存但标记为失败
            
            # 构造响应
            upload_response = DocumentUploadResponse(
                document_id=document_id,
                filename=filename,
                file_size=len(file_content),
                file_size_mb=len(file_content) / (1024 * 1024),
                upload_time=document.upload_time,
                processing_status=ProcessingStatusEnum(document.processing_status),
                message="文档上传成功，正在处理中"
            )
            
            return upload_response
            
        except DocumentValidationError:
            if should_close_db:
                db.rollback()
            raise
        except Exception as e:
            if should_close_db:
                db.rollback()
            logger.error("文档上传失败", filename=filename, error=str(e))
            raise DocumentServiceError(f"文档上传失败: {str(e)}", error_type="upload_error")
        finally:
            if should_close_db:
                db.close()
    
    def get_document(
        self,
        document_id: str,
        db: Optional[Session] = None
    ) -> Optional[DocumentResponse]:
        """
        获取文档详情
        
        Args:
            document_id: 文档ID
            db: 数据库会话
            
        Returns:
            DocumentResponse: 文档详情，不存在则返回None
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            
            if not document:
                return None
            
            return self._document_to_response(document)
            
        finally:
            if should_close_db:
                db.close()
    
    def list_documents(
        self,
        page: int = 1,
        page_size: int = 10,
        status_filter: Optional[List[ProcessingStatusEnum]] = None,
        file_type_filter: Optional[List[str]] = None,
        search_query: Optional[str] = None,
        sort_by: str = "upload_time",
        sort_order: str = "desc",
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        获取文档列表
        
        Args:
            page: 页码
            page_size: 每页数量
            status_filter: 状态过滤
            file_type_filter: 文件类型过滤
            search_query: 搜索查询
            sort_by: 排序字段
            sort_order: 排序顺序 (asc/desc)
            db: 数据库会话
            
        Returns:
            Dict: 包含文档列表和分页信息
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            # 构建查询
            query = db.query(Document)
            
            # 状态过滤
            if status_filter:
                status_values = [status.value for status in status_filter]
                query = query.filter(Document.processing_status.in_(status_values))
            
            # 文件类型过滤
            if file_type_filter:
                query = query.filter(Document.file_type.in_(file_type_filter))
            
            # 搜索查询
            if search_query:
                search_pattern = f"%{search_query}%"
                query = query.filter(
                    or_(
                        Document.filename.ilike(search_pattern),
                        Document.file_type.ilike(search_pattern)
                    )
                )
            
            # 排序
            if hasattr(Document, sort_by):
                sort_column = getattr(Document, sort_by)
                if sort_order.lower() == "desc":
                    query = query.order_by(desc(sort_column))
                else:
                    query = query.order_by(asc(sort_column))
            else:
                # 默认按上传时间倒序
                query = query.order_by(desc(Document.upload_time))
            
            # 获取总数
            total = query.count()
            
            # 分页
            offset = (page - 1) * page_size
            documents = query.offset(offset).limit(page_size).all()
            
            # 转换为响应格式
            document_responses = [self._document_to_response(doc) for doc in documents]
            
            return {
                "documents": document_responses,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
            
        finally:
            if should_close_db:
                db.close()
    
    def update_document(
        self,
        document_id: str,
        update_data: DocumentUpdate,
        db: Optional[Session] = None
    ) -> Optional[DocumentResponse]:
        """
        更新文档
        
        Args:
            document_id: 文档ID
            update_data: 更新数据
            db: 数据库会话
            
        Returns:
            DocumentResponse: 更新后的文档，不存在则返回None
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            
            if not document:
                return None
            
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                if hasattr(document, field):
                    setattr(document, field, value)
            
            db.commit()
            db.refresh(document)
            
            logger.info("文档更新成功", document_id=document_id, fields=list(update_dict.keys()))
            
            return self._document_to_response(document)
            
        except Exception as e:
            db.rollback()
            logger.error("文档更新失败", document_id=document_id, error=str(e))
            raise DocumentServiceError(f"文档更新失败: {str(e)}", error_type="update_error")
        finally:
            if should_close_db:
                db.close()
    
    def delete_document(
        self,
        document_id: str,
        soft_delete: bool = True,
        db: Optional[Session] = None
    ) -> bool:
        """
        删除文档
        
        Args:
            document_id: 文档ID
            soft_delete: 是否软删除（标记删除而不是物理删除）
            db: 数据库会话
            
        Returns:
            bool: 删除是否成功
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            
            if not document:
                logger.warning("要删除的文档不存在", document_id=document_id)
                return False
            
            if soft_delete:
                # 软删除：标记为失败状态
                document.processing_status = ProcessingStatus.FAILED.value
                document.error_message = "文档已被删除"
                db.commit()
                logger.info("文档软删除成功", document_id=document_id, filename=document.filename)
            else:
                # 硬删除：从数据库中移除
                filename = document.filename
                db.delete(document)
                db.commit()
                logger.info("文档硬删除成功", document_id=document_id, filename=filename)
            
            return True
            
        except Exception as e:
            db.rollback()
            logger.error("文档删除失败", document_id=document_id, error=str(e))
            return False
        finally:
            if should_close_db:
                db.close()
    
    async def get_processing_status(
        self,
        document_id: str,
        db: Optional[Session] = None
    ) -> Optional[DocumentProcessingStatus]:
        """
        获取文档处理状态
        
        Args:
            document_id: 文档ID
            db: 数据库会话
            
        Returns:
            DocumentProcessingStatus: 处理状态信息
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            
            if not document:
                return None
            
            # 计算进度百分比
            progress = None
            if document.processing_status == ProcessingStatus.PENDING.value:
                progress = 0.0
            elif document.processing_status == ProcessingStatus.PROCESSING.value:
                progress = 50.0  # 估算进度
            elif document.processing_status == ProcessingStatus.COMPLETED.value:
                progress = 100.0
            elif document.processing_status == ProcessingStatus.FAILED.value:
                progress = 0.0
            
            return DocumentProcessingStatus(
                document_id=document_id,
                filename=document.filename,
                processing_status=ProcessingStatusEnum(document.processing_status),
                progress=progress,
                chunks_count=document.chunks_count,
                error_message=document.error_message,
                estimated_time_remaining=None  # 可以根据实际情况计算
            )
            
        finally:
            if should_close_db:
                db.close()
    
    def get_statistics(self, db: Optional[Session] = None) -> Dict[str, Any]:
        """
        获取文档统计信息
        
        Args:
            db: 数据库会话
            
        Returns:
            Dict: 统计信息
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            # 总文档数
            total_documents = db.query(Document).count()
            
            # 各状态文档数
            status_counts = {}
            for status in ProcessingStatus:
                count = db.query(Document).filter(
                    Document.processing_status == status.value
                ).count()
                status_counts[status.value] = count
            
            # 文件类型统计
            type_counts = db.query(
                Document.file_type,
                func.count(Document.id)
            ).group_by(Document.file_type).all()
            
            type_stats = {file_type: count for file_type, count in type_counts}
            
            # 总文件大小
            total_size = db.query(func.sum(Document.file_size)).scalar() or 0
            
            # 最近24小时上传数
            yesterday = datetime.utcnow() - timedelta(days=1)
            recent_uploads = db.query(Document).filter(
                Document.upload_time >= yesterday
            ).count()
            
            return {
                "total_documents": total_documents,
                "status_distribution": status_counts,
                "type_distribution": type_stats,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "recent_uploads_24h": recent_uploads,
                "supported_types": list(self.supported_types.keys()),
                "max_file_size_mb": settings.MAX_FILE_SIZE / (1024 * 1024)
            }
            
        finally:
            if should_close_db:
                db.close()
    
    def cleanup_failed_documents(
        self,
        older_than_hours: int = 24,
        db: Optional[Session] = None
    ) -> int:
        """
        清理失败的文档记录
        
        Args:
            older_than_hours: 清理多少小时前的失败文档
            db: 数据库会话
            
        Returns:
            int: 清理的文档数量
        """
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=older_than_hours)
            
            # 查找要清理的文档
            failed_docs = db.query(Document).filter(
                and_(
                    Document.processing_status == ProcessingStatus.FAILED.value,
                    Document.upload_time < cutoff_time
                )
            ).all()
            
            cleaned_count = len(failed_docs)
            
            # 删除文档
            for doc in failed_docs:
                db.delete(doc)
            
            db.commit()
            
            if cleaned_count > 0:
                logger.info("清理失败文档完成", count=cleaned_count, older_than_hours=older_than_hours)
            
            return cleaned_count
            
        except Exception as e:
            db.rollback()
            logger.error("清理失败文档异常", error=str(e))
            return 0
        finally:
            if should_close_db:
                db.close()
    
    # 私有方法
    async def _update_document_status(
        self,
        document_id: str,
        status: ProcessingStatus,
        error_message: Optional[str] = None,
        chunks_count: Optional[int] = None,
        db: Optional[Session] = None
    ) -> None:
        """更新文档状态"""
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            if document:
                document.processing_status = status.value
                if error_message is not None:
                    document.error_message = error_message
                if chunks_count is not None:
                    document.chunks_count = chunks_count
                
                db.commit()
                
                logger.debug(
                    "文档状态已更新",
                    document_id=document_id,
                    status=status.value,
                    error_message=error_message
                )
                
        finally:
            if should_close_db:
                db.close()
    
    async def _update_document_processing_result(
        self,
        document_id: str,
        n8n_response: Any,
        db: Optional[Session] = None
    ) -> None:
        """根据n8n响应更新文档处理结果"""
        try:
            # 解析n8n响应中的状态和块数
            status = ProcessingStatus.COMPLETED
            chunks_count = 0
            error_message = None
            
            if hasattr(n8n_response, 'processing_status'):
                status_str = n8n_response.processing_status
                if status_str == "completed":
                    status = ProcessingStatus.COMPLETED
                elif status_str == "failed":
                    status = ProcessingStatus.FAILED
                    error_message = getattr(n8n_response, 'message', "处理失败")
                elif status_str == "processing":
                    status = ProcessingStatus.PROCESSING
            
            # 获取分块数量（如果有的话）
            if hasattr(n8n_response, 'chunks_count'):
                chunks_count = n8n_response.chunks_count
            
            await self._update_document_status(
                document_id, status, error_message, chunks_count, db
            )
            
        except Exception as e:
            logger.error("更新文档处理结果失败", document_id=document_id, error=str(e))
            await self._update_document_status(
                document_id, ProcessingStatus.FAILED, f"结果更新失败: {str(e)}", db=db
            )
    
    def _document_to_response(self, document: Document) -> DocumentResponse:
        """将Document模型转换为DocumentResponse"""
        return DocumentResponse(
            id=document.id,
            filename=document.filename,
            file_type=document.file_type,
            file_size=document.file_size,
            upload_time=document.upload_time,
            processing_status=document.processing_status,
            chunks_count=document.chunks_count,
            error_message=document.error_message,
            file_size_mb=document.file_size_mb,
            is_processed=document.is_processed,
            is_processing=document.is_processing,
            has_error=document.has_error
        )


# 全局文档服务实例
document_service = DocumentService()


def get_document_service() -> DocumentService:
    """获取文档服务实例（依赖注入）"""
    return document_service