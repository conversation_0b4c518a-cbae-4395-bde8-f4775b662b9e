"""
消息处理服务
负责处理聊天消息的核心逻辑，包括AI服务集成、流式响应、错误处理等
"""

import asyncio
import json
import logging
import time
from typing import Optional, Dict, Any, List, AsyncGenerator, Union, Tuple
from enum import Enum
from datetime import datetime, timedelta
import structlog

from sqlalchemy.orm import Session

from app.services.chat_service import ChatService, get_chat_service
from app.services.n8n_client import N8nClient, get_n8n_client, N8nAPIError
from app.schemas.chat import (
    ChatRequest, ChatResponse, StreamChatResponse, 
    MessageRole, ChatMessageCreate, ChatSessionCreate
)
from app.core.database import get_db, SessionLocal
from app.core.config import settings


logger = structlog.get_logger(__name__)


class MessageType(str, Enum):
    """消息类型枚举"""
    TEXT = "text"                    # 纯文本对话
    DOCUMENT_QA = "document_qa"      # 文档问答
    SUMMARY = "summary"              # 摘要生成
    CODE_HELP = "code_help"          # 代码帮助
    TRANSLATION = "translation"      # 翻译


class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"              # 等待处理
    PROCESSING = "processing"        # 处理中
    COMPLETED = "completed"          # 处理完成
    FAILED = "failed"               # 处理失败
    RETRYING = "retrying"           # 重试中


class MessageProcessingError(Exception):
    """消息处理相关错误"""
    def __init__(self, message: str, error_type: str = "unknown", retry_count: int = 0):
        super().__init__(message)
        self.error_type = error_type
        self.retry_count = retry_count


class MessageProcessor:
    """消息处理核心类"""
    
    def __init__(
        self, 
        chat_service: Optional[ChatService] = None,
        n8n_client: Optional[N8nClient] = None,
        max_retries: int = 3,
        retry_delays: List[float] = None
    ):
        """
        初始化消息处理器
        
        Args:
            chat_service: 聊天服务实例
            n8n_client: n8n客户端实例
            max_retries: 最大重试次数
            retry_delays: 重试延迟列表（秒）
        """
        self.chat_service = chat_service or get_chat_service()
        self.n8n_client = n8n_client or get_n8n_client()
        self.max_retries = max_retries
        self.retry_delays = retry_delays or [1.0, 2.0, 4.0]  # 指数退避
        
        # 处理状态跟踪
        self._processing_sessions: Dict[str, Dict[str, Any]] = {}
        
        logger.info(
            "消息处理器初始化",
            max_retries=self.max_retries,
            retry_delays=self.retry_delays
        )
    
    async def process_message(
        self,
        request: ChatRequest,
        message_type: MessageType = MessageType.TEXT,
        context: Optional[Dict[str, Any]] = None
    ) -> Union[ChatResponse, AsyncGenerator[str, None]]:
        """
        处理消息的主入口方法
        
        Args:
            request: 聊天请求
            message_type: 消息类型
            context: 额外上下文信息
            
        Returns:
            ChatResponse: 普通响应
            AsyncGenerator[str, None]: 流式响应
            
        Raises:
            MessageProcessingError: 处理失败时抛出
        """
        processing_id = f"{request.session_id or 'new'}_{int(time.time())}"
        
        logger.info(
            "开始处理消息",
            processing_id=processing_id,
            message_type=message_type,
            session_id=request.session_id,
            stream=request.stream,
            message_length=len(request.message)
        )
        
        try:
            # 更新处理状态
            self._update_processing_status(processing_id, ProcessingStatus.PROCESSING)
            
            # 准备会话
            session_id = await self._prepare_session(request)
            
            # 添加用户消息到历史
            await self._add_user_message(session_id, request.message)
            
            # 根据消息类型和流式选项处理
            if request.stream:
                return self._process_stream_message(
                    session_id, request, message_type, context, processing_id
                )
            else:
                return await self._process_standard_message(
                    session_id, request, message_type, context, processing_id
                )
                
        except Exception as e:
            self._update_processing_status(processing_id, ProcessingStatus.FAILED)
            logger.error(
                "消息处理失败",
                processing_id=processing_id,
                error=str(e),
                session_id=request.session_id
            )
            raise MessageProcessingError(f"消息处理失败: {str(e)}", error_type="processing_error")
    
    async def _prepare_session(self, request: ChatRequest) -> str:
        """
        准备聊天会话
        
        Args:
            request: 聊天请求
            
        Returns:
            str: 会话ID
        """
        if request.session_id:
            # 检查现有会话
            session = self.chat_service.get_session(request.session_id)
            if session:
                logger.debug("使用现有会话", session_id=request.session_id)
                return request.session_id
            else:
                logger.warning("会话不存在，创建新会话", requested_session_id=request.session_id)
        
        # 创建新会话
        session = self.chat_service.create_session(
            title="AI对话 - " + request.message[:30] + ("..." if len(request.message) > 30 else "")
        )
        
        logger.info("创建新会话", session_id=session.id, title=session.title)
        return session.id
    
    async def _add_user_message(self, session_id: str, content: str) -> None:
        """
        添加用户消息到历史记录
        
        Args:
            session_id: 会话ID
            content: 消息内容
        """
        try:
            self.chat_service.add_message(
                session_id=session_id,
                role=MessageRole.USER.value,
                content=content
            )
            logger.debug("用户消息已添加", session_id=session_id)
        except Exception as e:
            logger.error("添加用户消息失败", session_id=session_id, error=str(e))
            raise MessageProcessingError(f"添加用户消息失败: {str(e)}", error_type="database_error")
    
    async def _process_standard_message(
        self,
        session_id: str,
        request: ChatRequest,
        message_type: MessageType,
        context: Optional[Dict[str, Any]],
        processing_id: str
    ) -> ChatResponse:
        """
        处理标准（非流式）消息
        
        Args:
            session_id: 会话ID
            request: 聊天请求
            message_type: 消息类型
            context: 上下文信息
            processing_id: 处理ID
            
        Returns:
            ChatResponse: 标准响应
        """
        retry_count = 0
        last_error = None
        
        while retry_count <= self.max_retries:
            try:
                # 构建AI请求
                ai_request = self._build_ai_request(session_id, request, message_type, context)
                
                # 调用AI服务
                ai_response = await self._call_ai_service(ai_request, stream=False)
                
                # 添加AI响应到历史
                await self._add_ai_message(session_id, ai_response.message, ai_response.sources)
                
                # 更新处理状态
                self._update_processing_status(processing_id, ProcessingStatus.COMPLETED)
                
                logger.info(
                    "标准消息处理完成",
                    processing_id=processing_id,
                    session_id=session_id,
                    response_length=len(ai_response.message),
                    sources_count=len(ai_response.sources) if ai_response.sources else 0
                )
                
                return ChatResponse(
                    message=ai_response.message,
                    session_id=session_id,
                    message_id=ai_response.message_id,
                    sources=ai_response.sources,
                    created_at=datetime.now()
                )
                
            except N8nAPIError as e:
                last_error = e
                retry_count += 1
                
                if retry_count <= self.max_retries:
                    delay = self.retry_delays[min(retry_count - 1, len(self.retry_delays) - 1)]
                    
                    self._update_processing_status(processing_id, ProcessingStatus.RETRYING)
                    
                    logger.warning(
                        "AI服务调用失败，准备重试",
                        processing_id=processing_id,
                        retry_count=retry_count,
                        max_retries=self.max_retries,
                        delay=delay,
                        error=str(e)
                    )
                    
                    await asyncio.sleep(delay)
                else:
                    logger.error(
                        "AI服务调用重试次数用尽",
                        processing_id=processing_id,
                        retry_count=retry_count,
                        error=str(e)
                    )
                    break
            except Exception as e:
                logger.error(
                    "消息处理异常",
                    processing_id=processing_id,
                    error=str(e),
                    retry_count=retry_count
                )
                raise MessageProcessingError(f"消息处理异常: {str(e)}", error_type="unknown_error")
        
        # 所有重试都失败
        self._update_processing_status(processing_id, ProcessingStatus.FAILED)
        raise MessageProcessingError(
            f"AI服务调用失败，已重试{self.max_retries}次: {str(last_error)}",
            error_type="ai_service_error",
            retry_count=retry_count
        )
    
    async def _process_stream_message(
        self,
        session_id: str,
        request: ChatRequest,
        message_type: MessageType,
        context: Optional[Dict[str, Any]],
        processing_id: str
    ) -> AsyncGenerator[str, None]:
        """
        处理流式消息
        
        Args:
            session_id: 会话ID
            request: 聊天请求
            message_type: 消息类型
            context: 上下文信息
            processing_id: 处理ID
            
        Yields:
            str: 流式文本块
        """
        accumulated_response = ""
        message_id = None
        sources = None
        
        try:
            # 构建AI请求
            ai_request = self._build_ai_request(session_id, request, message_type, context)
            
            # 获取流式响应
            stream = await self._call_ai_service(ai_request, stream=True)
            
            logger.info("开始流式响应", processing_id=processing_id, session_id=session_id)
            
            async for chunk in stream:
                if chunk:
                    accumulated_response += chunk
                    
                    # 构造流式响应
                    stream_response = StreamChatResponse(
                        chunk=chunk,
                        session_id=session_id,
                        message_id=message_id,
                        is_complete=False
                    )
                    
                    yield stream_response.model_dump_json()
            
            # 流式响应完成，添加完整响应到历史
            if accumulated_response:
                message = await self._add_ai_message(session_id, accumulated_response, sources)
                message_id = message.id if message else None
            
            # 发送完成标志
            final_response = StreamChatResponse(
                chunk="",
                session_id=session_id,
                message_id=message_id,
                is_complete=True,
                sources=sources
            )
            
            yield final_response.model_dump_json()
            
            self._update_processing_status(processing_id, ProcessingStatus.COMPLETED)
            
            logger.info(
                "流式消息处理完成",
                processing_id=processing_id,
                session_id=session_id,
                total_length=len(accumulated_response)
            )
            
        except Exception as e:
            self._update_processing_status(processing_id, ProcessingStatus.FAILED)
            
            # 发送错误响应
            error_response = StreamChatResponse(
                chunk=f"[ERROR] 处理失败: {str(e)}",
                session_id=session_id,
                message_id=None,
                is_complete=True
            )
            
            yield error_response.model_dump_json()
            
            logger.error(
                "流式消息处理失败",
                processing_id=processing_id,
                session_id=session_id,
                error=str(e)
            )
    
    def _build_ai_request(
        self,
        session_id: str,
        request: ChatRequest,
        message_type: MessageType,
        context: Optional[Dict[str, Any]]
    ) -> ChatRequest:
        """
        构建AI服务请求
        
        Args:
            session_id: 会话ID
            request: 原始请求
            message_type: 消息类型
            context: 上下文信息
            
        Returns:
            ChatRequest: 构建的AI请求
        """
        # 基础请求
        ai_request = ChatRequest(
            message=request.message,
            session_id=session_id,
            stream=request.stream
        )
        
        # 根据消息类型调整请求
        if message_type == MessageType.DOCUMENT_QA:
            # 文档问答可能需要特殊的提示词
            ai_request.message = self._enhance_message_for_document_qa(request.message)
        elif message_type == MessageType.SUMMARY:
            ai_request.message = f"请总结以下内容：\n\n{request.message}"
        elif message_type == MessageType.CODE_HELP:
            ai_request.message = f"作为编程助手，请帮助解决以下问题：\n\n{request.message}"
        elif message_type == MessageType.TRANSLATION:
            ai_request.message = f"请翻译以下内容：\n\n{request.message}"
        
        # 添加上下文信息
        if context:
            logger.debug("添加上下文信息", session_id=session_id, context_keys=list(context.keys()))
        
        return ai_request
    
    def _enhance_message_for_document_qa(self, message: str) -> str:
        """
        为文档问答增强消息内容
        
        Args:
            message: 原始消息
            
        Returns:
            str: 增强后的消息
        """
        return f"基于已上传的文档回答以下问题：\n\n{message}"
    
    async def _call_ai_service(
        self,
        request: ChatRequest,
        stream: bool
    ) -> Union[ChatResponse, AsyncGenerator[str, None]]:
        """
        调用AI服务
        
        Args:
            request: AI请求
            stream: 是否流式
            
        Returns:
            AI响应或流式生成器
        """
        try:
            return await self.n8n_client.query_chat(request, stream=stream)
        except N8nAPIError as e:
            logger.error("AI服务调用失败", error=str(e), session_id=request.session_id)
            raise
        except Exception as e:
            logger.error("AI服务调用异常", error=str(e), session_id=request.session_id)
            raise N8nAPIError(f"AI服务调用异常: {str(e)}")
    
    async def _add_ai_message(
        self,
        session_id: str,
        content: str,
        sources: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Any]:
        """
        添加AI响应消息到历史记录
        
        Args:
            session_id: 会话ID
            content: 消息内容
            sources: 引用来源
            
        Returns:
            创建的消息对象
        """
        try:
            message = self.chat_service.add_message(
                session_id=session_id,
                role=MessageRole.ASSISTANT.value,
                content=content,
                sources={"sources": sources} if sources else None
            )
            logger.debug("AI消息已添加", session_id=session_id, message_id=message.id if message else None)
            return message
        except Exception as e:
            logger.error("添加AI消息失败", session_id=session_id, error=str(e))
            # 不抛出异常，因为这不应该影响响应
            return None
    
    def _update_processing_status(self, processing_id: str, status: ProcessingStatus) -> None:
        """
        更新处理状态
        
        Args:
            processing_id: 处理ID
            status: 新状态
        """
        if processing_id not in self._processing_sessions:
            self._processing_sessions[processing_id] = {}
        
        self._processing_sessions[processing_id].update({
            "status": status,
            "updated_at": datetime.now(),
            "processing_id": processing_id
        })
        
        logger.debug("处理状态更新", processing_id=processing_id, status=status)
    
    def get_processing_status(self, processing_id: str) -> Optional[Dict[str, Any]]:
        """
        获取处理状态
        
        Args:
            processing_id: 处理ID
            
        Returns:
            处理状态信息
        """
        return self._processing_sessions.get(processing_id)
    
    def clear_old_processing_status(self, max_age_minutes: int = 60) -> int:
        """
        清理旧的处理状态记录
        
        Args:
            max_age_minutes: 最大保留时间（分钟）
            
        Returns:
            清理的记录数量
        """
        cutoff_time = datetime.now() - timedelta(minutes=max_age_minutes)
        to_remove = []
        
        for processing_id, status_info in self._processing_sessions.items():
            if status_info.get("updated_at", datetime.min) < cutoff_time:
                to_remove.append(processing_id)
        
        for processing_id in to_remove:
            del self._processing_sessions[processing_id]
        
        if to_remove:
            logger.info("清理旧的处理状态", removed_count=len(to_remove))
        
        return len(to_remove)
    
    async def get_message_history(
        self,
        session_id: str,
        limit: Optional[int] = None,
        message_types: Optional[List[MessageRole]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取格式化的消息历史
        
        Args:
            session_id: 会话ID
            limit: 消息数量限制
            message_types: 要包含的消息类型
            
        Returns:
            格式化的消息历史
        """
        try:
            messages = self.chat_service.get_session_messages(session_id, limit)
            
            formatted_messages = []
            for msg in messages:
                if message_types and msg.role not in [mt.value for mt in message_types]:
                    continue
                
                formatted_msg = {
                    "id": msg.id,
                    "role": msg.role,
                    "content": msg.content,
                    "created_at": msg.created_at.isoformat() if msg.created_at else None,
                    "sources": msg.sources
                }
                formatted_messages.append(formatted_msg)
            
            return formatted_messages
            
        except Exception as e:
            logger.error("获取消息历史失败", session_id=session_id, error=str(e))
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        执行健康检查
        
        Returns:
            健康状态信息
        """
        health_info = {
            "processor_status": "healthy",
            "processing_sessions": len(self._processing_sessions),
            "chat_service": "unknown",
            "n8n_service": "unknown",
            "timestamp": datetime.now().isoformat()
        }
        
        # 检查聊天服务
        try:
            cache_stats = self.chat_service.get_cache_stats()
            health_info["chat_service"] = "healthy"
            health_info["chat_cache_stats"] = cache_stats
        except Exception as e:
            health_info["chat_service"] = f"unhealthy: {str(e)}"
        
        # 检查n8n服务
        try:
            n8n_health = await self.n8n_client.health_check()
            health_info["n8n_service"] = n8n_health["status"]
            health_info["n8n_details"] = n8n_health
        except Exception as e:
            health_info["n8n_service"] = f"unhealthy: {str(e)}"
        
        return health_info


# 全局消息处理器实例
message_processor = MessageProcessor()


async def get_message_processor() -> MessageProcessor:
    """获取消息处理器实例（依赖注入）"""
    return message_processor