"""
n8n API客户端
处理文档上传和智能问答请求
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, AsyncGenerator, Union
from urllib.parse import urljoin

import httpx
from httpx import AsyncClient, Response
import structlog

from app.core.config import settings
from app.schemas.document import DocumentUploadResponse, DocumentProcessingStatus
from app.schemas.chat import ChatRequest, ChatResponse


logger = structlog.get_logger(__name__)


class N8nAPIError(Exception):
    """n8n API相关错误"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class N8nClient:
    """n8n API客户端"""
    
    def __init__(self, base_url: Optional[str] = None, timeout: Optional[int] = None):
        """
        初始化n8n客户端
        
        Args:
            base_url: n8n服务器基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url or settings.N8N_BASE_URL
        self.timeout = timeout or settings.API_TIMEOUT
        self.upload_url = urljoin(self.base_url, settings.N8N_WEBHOOK_RAG_UPLOAD)
        self.query_url = urljoin(self.base_url, settings.N8N_WEBHOOK_RAG_QUERY)
        
        # 配置HTTP客户端
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout, connect=10.0),
            "limits": httpx.Limits(max_connections=100, max_keepalive_connections=20),
            "follow_redirects": True,
        }
        
        logger.info(
            "n8n客户端初始化",
            base_url=self.base_url,
            upload_url=self.upload_url,
            query_url=self.query_url,
            timeout=self.timeout
        )
    
    async def _make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> Response:
        """
        发送HTTP请求的通用方法
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 传递给httpx的额外参数
            
        Returns:
            Response对象
            
        Raises:
            N8nAPIError: 请求失败时抛出
        """
        async with AsyncClient(**self.client_config) as client:
            try:
                logger.debug(
                    "发送n8n请求",
                    method=method,
                    url=url,
                    params=kwargs.get('params'),
                    has_files=bool(kwargs.get('files')),
                    has_data=bool(kwargs.get('data'))
                )
                
                response = await client.request(method, url, **kwargs)
                
                # 记录响应
                logger.debug(
                    "收到n8n响应",
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    url=url
                )
                
                return response
                
            except httpx.TimeoutException as e:
                logger.error("n8n请求超时", url=url, timeout=self.timeout)
                raise N8nAPIError(f"请求超时: {url}", status_code=408)
            except httpx.ConnectError as e:
                logger.error("n8n连接失败", url=url, error=str(e))
                raise N8nAPIError(f"连接失败: {url}", status_code=503)
            except httpx.HTTPStatusError as e:
                logger.error("n8n HTTP错误", status_code=e.response.status_code, url=url)
                raise N8nAPIError(f"HTTP错误: {e.response.status_code}", status_code=e.response.status_code)
            except Exception as e:
                logger.error("n8n请求异常", url=url, error=str(e))
                raise N8nAPIError(f"请求异常: {str(e)}")
    
    async def upload_document(
        self,
        file_content: bytes,
        filename: str,
        content_type: str = "application/octet-stream"
    ) -> DocumentUploadResponse:
        """
        上传文档到n8n进行处理
        
        Args:
            file_content: 文件内容
            filename: 文件名
            content_type: 文件MIME类型
            
        Returns:
            DocumentUploadResponse: 上传响应
            
        Raises:
            N8nAPIError: 上传失败时抛出
        """
        logger.info("开始上传文档", filename=filename, size=len(file_content))
        
        # 准备文件数据
        files = {
            "file": (filename, file_content, content_type)
        }
        
        try:
            response = await self._make_request(
                "POST",
                self.upload_url,
                files=files,
                data={"filename": filename}
            )
            
            # 检查响应状态
            if response.status_code != 200:
                error_msg = f"文档上传失败: HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data.get('message', '未知错误')}"
                except:
                    error_msg += f" - {response.text}"
                
                logger.error("文档上传失败", 
                           filename=filename,
                           status_code=response.status_code,
                           response=response.text)
                
                raise N8nAPIError(error_msg, status_code=response.status_code)
            
            # 解析响应数据
            response_text = response.text
            logger.debug("n8n原始响应", filename=filename, response=response_text[:500])

            try:
                if not response_text.strip():
                    # 空响应，可能是工作流没有正确配置
                    logger.warning("n8n返回空响应", filename=filename)
                    response_data = {
                        "document_id": f"fallback_{int(datetime.now().timestamp())}",
                        "status": "processing",
                        "message": "文档已接收，但n8n工作流可能未正确配置"
                    }
                else:
                    response_data = response.json()
            except json.JSONDecodeError as e:
                logger.error("n8n响应格式错误", filename=filename, response=response_text, error=str(e))
                # 尝试从响应中提取有用信息
                response_data = {
                    "document_id": f"error_{int(datetime.now().timestamp())}",
                    "status": "failed",
                    "message": f"响应解析失败: {response_text[:100]}"
                }
                # 不抛出异常，允许继续处理
            
            # 构造响应对象
            upload_response = DocumentUploadResponse(
                document_id=response_data.get("document_id", ""),
                filename=filename,
                file_size=len(file_content),
                file_size_mb=len(file_content) / (1024 * 1024),
                upload_time=datetime.now(),
                processing_status=response_data.get("status", "processing"),
                message=response_data.get("message", "文档上传成功")
            )
            
            chunks_count = response_data.get("chunks_count", 0)
            logger.info("文档上传成功", 
                       filename=filename,
                       document_id=upload_response.document_id,
                       chunks_count=chunks_count)
            
            return upload_response
            
        except N8nAPIError:
            raise
        except Exception as e:
            logger.error("文档上传异常", filename=filename, error=str(e))
            raise N8nAPIError(f"文档上传异常: {str(e)}")
    
    async def query_chat(
        self,
        request: ChatRequest,
        stream: bool = False
    ) -> Union[ChatResponse, AsyncGenerator[str, None]]:
        """
        发送聊天查询请求
        
        Args:
            request: 聊天请求对象
            stream: 是否使用流式响应
            
        Returns:
            ChatResponse: 标准响应模式
            AsyncGenerator[str, None]: 流式响应模式
            
        Raises:
            N8nAPIError: 查询失败时抛出
        """
        logger.info("发送聊天查询", 
                   message=request.message[:100] + "..." if len(request.message) > 100 else request.message,
                   session_id=request.session_id,
                   stream=stream)
        
        # 准备请求数据
        request_data = {
            "message": request.message,
            "session_id": request.session_id,
            "stream": stream
        }
        
        try:
            if stream:
                # 流式响应
                return self._stream_chat_response(request_data)
            else:
                # 标准响应
                return await self._standard_chat_response(request_data)
                
        except N8nAPIError:
            raise
        except Exception as e:
            logger.error("聊天查询异常", error=str(e), session_id=request.session_id)
            raise N8nAPIError(f"聊天查询异常: {str(e)}")
    
    async def _standard_chat_response(self, request_data: Dict[str, Any]) -> ChatResponse:
        """处理标准聊天响应"""
        response = await self._make_request(
            "POST",
            self.query_url,
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            error_msg = f"聊天查询失败: HTTP {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f" - {error_data.get('message', '未知错误')}"
            except:
                error_msg += f" - {response.text}"
            
            logger.error("聊天查询失败", 
                       status_code=response.status_code,
                       response=response.text)
            
            raise N8nAPIError(error_msg, status_code=response.status_code)
        
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            logger.error("n8n聊天响应格式错误", response=response.text)
            raise N8nAPIError("响应格式错误", status_code=response.status_code)
        
        # 构造响应对象
        chat_response = ChatResponse(
            message=response_data.get("answer", ""),
            session_id=response_data.get("session_id", ""),
            message_id=response_data.get("message_id", ""),
            sources=response_data.get("sources", []),
            created_at=datetime.now()
        )
        
        sources_count = len(chat_response.sources) if chat_response.sources else 0
        logger.info("聊天查询成功", 
                   session_id=chat_response.session_id,
                   answer_length=len(chat_response.message),
                   sources_count=sources_count)
        
        return chat_response
    
    async def _stream_chat_response(self, request_data: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """处理流式聊天响应"""
        async with AsyncClient(**self.client_config) as client:
            try:
                async with client.stream(
                    "POST",
                    self.query_url,
                    json=request_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status_code != 200:
                        error_msg = f"流式聊天查询失败: HTTP {response.status_code}"
                        logger.error("流式聊天查询失败", status_code=response.status_code)
                        raise N8nAPIError(error_msg, status_code=response.status_code)
                    
                    # 处理流式响应
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            # 处理Server-Sent Events格式
                            if chunk.startswith("data: "):
                                data = chunk[6:].strip()
                                if data and data != "[DONE]":
                                    try:
                                        # 尝试解析JSON数据
                                        chunk_data = json.loads(data)
                                        if "delta" in chunk_data:
                                            yield chunk_data["delta"]
                                        elif "content" in chunk_data:
                                            yield chunk_data["content"]
                                        else:
                                            yield data
                                    except json.JSONDecodeError:
                                        # 如果不是JSON，直接返回文本
                                        yield data
                            else:
                                yield chunk
                    
                    logger.info("流式聊天查询完成", 
                               session_id=request_data.get("session_id"))
                    
            except httpx.TimeoutException:
                logger.error("流式聊天查询超时")
                raise N8nAPIError("流式查询超时", status_code=408)
            except httpx.ConnectError:
                logger.error("流式聊天查询连接失败")
                raise N8nAPIError("流式查询连接失败", status_code=503)
            except N8nAPIError:
                raise
            except Exception as e:
                logger.error("流式聊天查询异常", error=str(e))
                raise N8nAPIError(f"流式查询异常: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """
        检查n8n服务健康状态
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        try:
            # 尝试连接n8n服务
            response = await self._make_request(
                "GET",
                self.base_url,
                headers={"User-Agent": "AI-Knowledge-Chat-Health-Check"}
            )
            
            return {
                "status": "healthy",
                "base_url": self.base_url,
                "response_time": response.elapsed.total_seconds() if response.elapsed else 0,
                "status_code": response.status_code
            }
            
        except N8nAPIError as e:
            logger.error("n8n健康检查失败", error=str(e))
            return {
                "status": "unhealthy",
                "base_url": self.base_url,
                "error": str(e),
                "status_code": e.status_code
            }
        except Exception as e:
            logger.error("n8n健康检查异常", error=str(e))
            return {
                "status": "unhealthy",
                "base_url": self.base_url,
                "error": str(e),
                "status_code": None
            }
    
    async def close(self):
        """关闭客户端连接"""
        logger.info("关闭n8n客户端")
        # httpx.AsyncClient 在上下文管理器中自动关闭


# 全局n8n客户端实例
n8n_client = N8nClient()


def get_n8n_client() -> N8nClient:
    """获取n8n客户端实例（依赖注入）"""
    return n8n_client 