# 超时配置说明

## 概述

本系统针对使用本地Ollama模型进行文档处理进行了超时优化。由于本地模型处理速度相对较慢，我们设置了更长的超时时间以确保处理能够完成。

## 超时配置参数

### 1. API_TIMEOUT (30秒)
- **用途**: 默认API请求超时
- **适用**: 一般的API调用，如获取文档列表、状态查询等
- **建议**: 保持默认值，这些操作通常很快

### 2. N8N_UPLOAD_TIMEOUT (300秒 = 5分钟)
- **用途**: 文档上传到n8n工作流的超时时间
- **适用**: 文档上传、文本提取、向量化、存储等完整流程
- **说明**: 包含了文档解析、文本切分、向量嵌入等步骤
- **调整建议**: 
  - 小文档(< 1MB): 可以减少到180秒(3分钟)
  - 大文档(> 5MB): 可能需要增加到600秒(10分钟)

### 3. N8N_QUERY_TIMEOUT (120秒 = 2分钟)
- **用途**: 聊天查询的超时时间
- **适用**: 用户提问、向量搜索、AI回答生成
- **说明**: 包含了向量搜索、上下文检索、AI推理等步骤
- **调整建议**:
  - 简单问题: 可以减少到60秒
  - 复杂问题: 可能需要增加到180秒

### 4. OLLAMA_PROCESSING_TIMEOUT (600秒 = 10分钟)
- **用途**: Ollama模型处理的最大超时时间
- **适用**: 大型文档的深度处理
- **说明**: 预留时间，用于特别大的文档或复杂的处理任务

## 环境变量配置

你可以通过环境变量来覆盖这些默认值：

```bash
# 设置文档上传超时为8分钟
export N8N_UPLOAD_TIMEOUT=480

# 设置查询超时为3分钟
export N8N_QUERY_TIMEOUT=180

# 设置Ollama处理超时为15分钟
export OLLAMA_PROCESSING_TIMEOUT=900
```

## 性能优化建议

### 1. Ollama模型优化
- 使用较小的模型（如llama2:7b而不是llama2:70b）
- 确保有足够的GPU内存
- 考虑使用量化模型

### 2. 文档预处理
- 限制单个文档大小（建议< 10MB）
- 预先清理文档格式
- 分批处理大量文档

### 3. 系统资源
- 确保足够的CPU和内存
- 使用SSD存储提高I/O性能
- 监控系统资源使用情况

## 故障排除

### 超时错误处理
1. **文档上传超时**: 
   - 检查Ollama服务状态
   - 减小文档大小
   - 增加N8N_UPLOAD_TIMEOUT值

2. **查询超时**:
   - 简化问题描述
   - 检查向量数据库状态
   - 增加N8N_QUERY_TIMEOUT值

3. **系统监控**:
   - 查看后端日志: `tail -f backend/logs/app.log`
   - 监控Ollama状态: `ollama ps`
   - 检查n8n工作流执行状态

## 日志监控

系统会记录详细的超时信息：

```
INFO: 使用长超时上传文档, timeout=300
WARNING: n8n文档处理超时, suggestion=建议稍后重试或检查Ollama服务状态
```

通过这些日志可以判断是否需要调整超时配置。
