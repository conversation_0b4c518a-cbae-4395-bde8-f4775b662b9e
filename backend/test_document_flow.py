"""
文档管理服务的端到端测试
用于验证DocumentService的完整功能
"""
import asyncio
import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.document_service import DocumentService, DocumentValidationError
from app.schemas.document import DocumentUpdate, ProcessingStatusEnum
from app.core.database import ensure_database_exists


async def test_document_service():
    """测试文档服务的基本功能"""
    print("🚀 开始测试文档管理服务...")
    
    try:
        # 初始化数据库
        print("📦 初始化数据库...")
        ensure_database_exists()
        
        # 创建临时上传目录
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 创建临时上传目录: {temp_dir}")
            
            # 创建文档服务
            print("⚙️ 创建文档服务...")
            document_service = DocumentService(upload_dir=temp_dir)
            
            # 测试文件验证
            print("\n🔍 测试文件验证...")
            
            # 测试有效文件
            try:
                file_type, mime_type = document_service.validate_file(
                    filename="test.txt",
                    file_size=1024,
                    content_type="text/plain"
                )
                print(f"✅ 有效文件验证成功: {file_type} -> {mime_type}")
            except Exception as e:
                print(f"❌ 有效文件验证失败: {e}")
            
            # 测试无效文件类型
            try:
                document_service.validate_file("test.xyz", 1024)
                print("❌ 应该拒绝无效文件类型")
            except DocumentValidationError as e:
                print(f"✅ 正确拒绝无效文件类型: {e}")
            
            # 测试文件大小限制
            try:
                document_service.validate_file("test.txt", 100 * 1024 * 1024)  # 100MB
                print("❌ 应该拒绝过大文件")
            except DocumentValidationError as e:
                print(f"✅ 正确拒绝过大文件: {e}")
            
            # 测试文档上传
            print("\n📤 测试文档上传...")
            test_content = "这是一个测试文档的内容。\n包含多行文本用于测试AI处理。"
            test_filename = "test_document.txt"
            
            try:
                upload_response = await document_service.upload_document(
                    file_content=test_content.encode('utf-8'),
                    filename=test_filename,
                    content_type="text/plain"
                )
                
                print(f"✅ 文档上传成功!")
                print(f"   文档ID: {upload_response.document_id}")
                print(f"   文件名: {upload_response.filename}")
                print(f"   大小: {upload_response.file_size_mb:.4f} MB")
                print(f"   状态: {upload_response.processing_status}")
                
                document_id = upload_response.document_id
                
            except Exception as e:
                print(f"❌ 文档上传失败: {e}")
                document_id = "test-fallback-id"
            
            # 测试获取文档详情
            print("\n📄 测试获取文档详情...")
            try:
                document = document_service.get_document(document_id)
                if document:
                    print(f"✅ 获取文档详情成功!")
                    print(f"   ID: {document.id}")
                    print(f"   文件名: {document.filename}")
                    print(f"   类型: {document.file_type}")
                    print(f"   大小: {document.file_size_mb:.4f} MB")
                    print(f"   状态: {document.processing_status}")
                    print(f"   上传时间: {document.upload_time}")
                    print(f"   是否已处理: {document.is_processed}")
                    print(f"   是否处理中: {document.is_processing}")
                    print(f"   是否有错误: {document.has_error}")
                else:
                    print("⚠️ 文档不存在（可能是n8n服务不可用导致上传失败）")
            except Exception as e:
                print(f"❌ 获取文档详情失败: {e}")
            
            # 测试处理状态查询
            print("\n📊 测试处理状态查询...")
            try:
                status = await document_service.get_processing_status(document_id)
                if status:
                    print(f"✅ 获取处理状态成功!")
                    print(f"   文档ID: {status.document_id}")
                    print(f"   文件名: {status.filename}")
                    print(f"   状态: {status.processing_status}")
                    print(f"   进度: {status.progress}%")
                    print(f"   分块数: {status.chunks_count}")
                    if status.error_message:
                        print(f"   错误信息: {status.error_message}")
                else:
                    print("⚠️ 处理状态不存在")
            except Exception as e:
                print(f"❌ 获取处理状态失败: {e}")
            
            # 测试文档列表
            print("\n📋 测试文档列表...")
            try:
                doc_list = document_service.list_documents(
                    page=1,
                    page_size=10,
                    sort_by="upload_time",
                    sort_order="desc"
                )
                
                print(f"✅ 获取文档列表成功!")
                print(f"   总数: {doc_list['total']}")
                print(f"   当前页: {doc_list['page']}")
                print(f"   每页数量: {doc_list['page_size']}")
                print(f"   总页数: {doc_list['total_pages']}")
                print(f"   返回文档数: {len(doc_list['documents'])}")
                
                # 显示前3个文档
                for i, doc in enumerate(doc_list['documents'][:3]):
                    print(f"   {i+1}. {doc.filename} ({doc.file_type}, {doc.processing_status})")
                    
            except Exception as e:
                print(f"❌ 获取文档列表失败: {e}")
            
            # 测试带过滤的文档列表
            print("\n🔍 测试过滤文档列表...")
            try:
                filtered_list = document_service.list_documents(
                    page=1,
                    page_size=5,
                    status_filter=[ProcessingStatusEnum.COMPLETED, ProcessingStatusEnum.PROCESSING],
                    file_type_filter=[".txt", ".md"],
                    search_query="test"
                )
                
                print(f"✅ 过滤文档列表成功!")
                print(f"   过滤后总数: {filtered_list['total']}")
                print(f"   返回文档数: {len(filtered_list['documents'])}")
                
            except Exception as e:
                print(f"❌ 过滤文档列表失败: {e}")
            
            # 测试文档更新
            print("\n✏️ 测试文档更新...")
            try:
                update_data = DocumentUpdate(
                    filename="updated_test_document.txt",
                    chunks_count=10
                )
                
                updated_doc = document_service.update_document(
                    document_id=document_id,
                    update_data=update_data
                )
                
                if updated_doc:
                    print(f"✅ 文档更新成功!")
                    print(f"   新文件名: {updated_doc.filename}")
                    print(f"   分块数: {updated_doc.chunks_count}")
                else:
                    print("⚠️ 文档更新失败（文档可能不存在）")
                    
            except Exception as e:
                print(f"❌ 文档更新失败: {e}")
            
            # 测试统计信息
            print("\n📈 测试统计信息...")
            try:
                stats = document_service.get_statistics()
                
                print(f"✅ 获取统计信息成功!")
                print(f"   总文档数: {stats['total_documents']}")
                print(f"   总大小: {stats['total_size_mb']} MB")
                print(f"   最近24小时上传: {stats['recent_uploads_24h']}")
                print(f"   支持类型: {', '.join(stats['supported_types'])}")
                print(f"   最大文件大小: {stats['max_file_size_mb']} MB")
                
                # 状态分布
                print("   状态分布:")
                for status, count in stats['status_distribution'].items():
                    print(f"     {status}: {count}")
                
                # 类型分布
                if stats['type_distribution']:
                    print("   类型分布:")
                    for file_type, count in stats['type_distribution'].items():
                        print(f"     {file_type}: {count}")
                
            except Exception as e:
                print(f"❌ 获取统计信息失败: {e}")
            
            # 测试多文档上传
            print("\n📤 测试多文档上传...")
            test_files = [
                ("markdown_test.md", "# 测试Markdown文档\n\n这是一个测试文档。", "text/markdown"),
                ("another_test.txt", "另一个测试文本文件的内容。", "text/plain")
            ]
            
            uploaded_ids = []
            for filename, content, content_type in test_files:
                try:
                    response = await document_service.upload_document(
                        file_content=content.encode('utf-8'),
                        filename=filename,
                        content_type=content_type
                    )
                    uploaded_ids.append(response.document_id)
                    print(f"✅ {filename} 上传成功 (ID: {response.document_id})")
                except Exception as e:
                    print(f"❌ {filename} 上传失败: {e}")
            
            # 测试清理功能
            print("\n🧹 测试清理功能...")
            try:
                cleaned_count = document_service.cleanup_failed_documents(
                    older_than_hours=0  # 清理所有失败文档
                )
                print(f"✅ 清理完成，删除了 {cleaned_count} 个失败文档")
            except Exception as e:
                print(f"❌ 清理失败: {e}")
            
            # 测试软删除
            print("\n🗑️ 测试软删除...")
            if uploaded_ids:
                test_id = uploaded_ids[0]
                try:
                    success = document_service.delete_document(test_id, soft_delete=True)
                    if success:
                        print(f"✅ 软删除成功 (ID: {test_id})")
                        
                        # 验证软删除效果
                        deleted_doc = document_service.get_document(test_id)
                        if deleted_doc and deleted_doc.has_error:
                            print(f"✅ 软删除验证成功：文档标记为失败状态")
                        else:
                            print(f"⚠️ 软删除验证异常：文档状态不符合预期")
                    else:
                        print(f"❌ 软删除失败")
                except Exception as e:
                    print(f"❌ 软删除异常: {e}")
            
            # 测试硬删除
            print("\n💥 测试硬删除...")
            if len(uploaded_ids) > 1:
                test_id = uploaded_ids[1]
                try:
                    success = document_service.delete_document(test_id, soft_delete=False)
                    if success:
                        print(f"✅ 硬删除成功 (ID: {test_id})")
                        
                        # 验证硬删除效果
                        deleted_doc = document_service.get_document(test_id)
                        if deleted_doc is None:
                            print(f"✅ 硬删除验证成功：文档已完全删除")
                        else:
                            print(f"⚠️ 硬删除验证异常：文档仍然存在")
                    else:
                        print(f"❌ 硬删除失败")
                except Exception as e:
                    print(f"❌ 硬删除异常: {e}")
            
            print("\n🎉 文档管理服务测试完成!")
            
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def test_edge_cases():
    """测试边界情况和错误处理"""
    print("\n🔧 测试边界情况和错误处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        document_service = DocumentService(upload_dir=temp_dir)
        
        # 测试空文件上传
        print("测试空文件上传...")
        try:
            await document_service.upload_document(
                file_content=b"",
                filename="empty.txt"
            )
            print("❌ 应该拒绝空文件")
        except Exception as e:
            print(f"✅ 正确拒绝空文件: {type(e).__name__}")
        
        # 测试无效文档ID查询
        print("测试无效文档ID查询...")
        result = document_service.get_document("nonexistent-id")
        if result is None:
            print("✅ 正确处理不存在的文档ID")
        else:
            print("❌ 应该返回None对于不存在的文档")
        
        # 测试无效分页参数
        print("测试无效分页参数...")
        try:
            # 虽然在服务层没有直接验证，但API层会验证
            result = document_service.list_documents(page=0, page_size=-1)
            print("⚠️ 服务层未验证无效分页参数（由API层处理）")
        except Exception as e:
            print(f"✅ 正确处理无效分页参数: {e}")
        
        print("✅ 边界情况测试完成!")


def main():
    """主函数"""
    print("=" * 60)
    print("🧪 AI知识聊天系统 - 文档管理服务测试")
    print("=" * 60)
    
    # 运行测试
    asyncio.run(test_document_service())
    asyncio.run(test_edge_cases())
    
    print("\n" + "=" * 60)
    print("✨ 测试完成! 文档管理服务功能验证结束")
    print("=" * 60)


if __name__ == "__main__":
    main()