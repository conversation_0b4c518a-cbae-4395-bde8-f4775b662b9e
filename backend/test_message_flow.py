"""
消息处理服务的端到端测试
用于验证MessageProcessor的完整功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.message_processor import MessageProcessor, MessageType
from app.schemas.chat import ChatRequest
from app.core.database import ensure_database_exists


async def test_message_processor():
    """测试消息处理器的基本功能"""
    print("🚀 开始测试消息处理服务...")
    
    try:
        # 初始化数据库
        print("📦 初始化数据库...")
        ensure_database_exists()
        
        # 创建消息处理器
        print("⚙️ 创建消息处理器...")
        processor = MessageProcessor()
        
        # 测试健康检查
        print("🔍 执行健康检查...")
        health = await processor.health_check()
        print(f"健康状态: {health}")
        
        # 测试标准消息处理
        print("\n💬 测试标准消息处理...")
        request = ChatRequest(
            message="你好！这是一个测试消息。",
            session_id=None,  # 创建新会话
            stream=False
        )
        
        try:
            response = await processor.process_message(request, MessageType.TEXT)
            print(f"✅ 标准消息处理成功!")
            print(f"   会话ID: {response.session_id}")
            print(f"   消息ID: {response.message_id}")
            print(f"   AI回复: {response.message[:100]}...")
            
            # 保存会话ID用于后续测试
            session_id = response.session_id
            
        except Exception as e:
            print(f"❌ 标准消息处理失败: {e}")
            # 如果AI服务不可用，创建一个虚拟会话ID继续测试
            session_id = "test-session-fallback"
        
        # 测试消息历史获取
        print("\n📚 测试消息历史获取...")
        try:
            history = await processor.get_message_history(session_id, limit=10)
            print(f"✅ 获取消息历史成功! 共 {len(history)} 条消息")
            for i, msg in enumerate(history[:3]):  # 显示前3条
                print(f"   {i+1}. [{msg['role']}] {msg['content'][:50]}...")
        except Exception as e:
            print(f"❌ 获取消息历史失败: {e}")
        
        # 测试流式消息处理（模拟）
        print("\n🌊 测试流式消息处理...")
        stream_request = ChatRequest(
            message="请告诉我关于AI的简短介绍",
            session_id=session_id,
            stream=True
        )
        
        try:
            stream_generator = await processor.process_message(stream_request, MessageType.TEXT)
            print("✅ 流式消息处理启动成功!")
            
            chunk_count = 0
            async for chunk_json in stream_generator:
                chunk_count += 1
                if chunk_count <= 3:  # 只显示前3个块
                    import json
                    try:
                        chunk_data = json.loads(chunk_json)
                        print(f"   块 {chunk_count}: {chunk_data.get('chunk', '')[:30]}...")
                    except:
                        print(f"   块 {chunk_count}: {chunk_json[:30]}...")
                
                if chunk_count >= 10:  # 限制测试块数
                    break
            
            print(f"✅ 流式处理完成! 共处理 {chunk_count} 个块")
            
        except Exception as e:
            print(f"❌ 流式消息处理失败: {e}")
        
        # 测试不同消息类型
        print("\n🎯 测试不同消息类型...")
        message_types = [
            (MessageType.DOCUMENT_QA, "根据文档回答：什么是机器学习？"),
            (MessageType.SUMMARY, "这是一段需要总结的文本内容..."),
            (MessageType.CODE_HELP, "如何在Python中实现快速排序？"),
            (MessageType.TRANSLATION, "Hello, how are you today?")
        ]
        
        for msg_type, test_message in message_types:
            try:
                test_request = ChatRequest(
                    message=test_message,
                    session_id=session_id,
                    stream=False
                )
                
                # 只测试请求构建，不实际调用AI服务
                ai_request = processor._build_ai_request(
                    session_id, test_request, msg_type, None
                )
                print(f"✅ {msg_type.value} 类型请求构建成功")
                print(f"   增强后消息: {ai_request.message[:80]}...")
                
            except Exception as e:
                print(f"❌ {msg_type.value} 类型测试失败: {e}")
        
        # 测试状态管理
        print("\n📊 测试状态管理...")
        processing_id = "test-status-123"
        from app.services.message_processor import ProcessingStatus
        processor._update_processing_status(processing_id, ProcessingStatus.PROCESSING)
        
        status = processor.get_processing_status(processing_id)
        if status:
            print(f"✅ 状态管理正常: {status['status']}")
        else:
            print("❌ 状态管理失败")
        
        # 清理测试
        removed_count = processor.clear_old_processing_status(max_age_minutes=0)
        print(f"✅ 清理旧状态: 移除 {removed_count} 条记录")
        
        print("\n🎉 消息处理服务测试完成!")
        
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def test_error_handling():
    """测试错误处理和重试机制"""
    print("\n🔧 测试错误处理和重试机制...")
    
    # 这里可以添加专门的错误场景测试
    # 比如网络错误、AI服务不可用等
    
    processor = MessageProcessor(max_retries=2, retry_delays=[0.1, 0.2])
    
    # 测试重试配置
    print(f"✅ 重试配置: 最大重试 {processor.max_retries} 次")
    print(f"✅ 重试延迟: {processor.retry_delays}")


def main():
    """主函数"""
    print("=" * 60)
    print("🧪 AI知识聊天系统 - 消息处理服务测试")
    print("=" * 60)
    
    # 运行测试
    asyncio.run(test_message_processor())
    asyncio.run(test_error_handling())
    
    print("\n" + "=" * 60)
    print("✨ 测试完成! 消息处理服务功能验证结束")
    print("=" * 60)


if __name__ == "__main__":
    main()