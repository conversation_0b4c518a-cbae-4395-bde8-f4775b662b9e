#!/usr/bin/env python3
"""
简化的测试服务器脚本，用于调试启动问题
"""

if __name__ == "__main__":
    print("=== FastAPI 启动测试 ===")
    
    # 1. 测试基本导入
    try:
        import fastapi
        print(f"✅ FastAPI {fastapi.__version__} 导入成功")
    except Exception as e:
        print(f"❌ FastAPI 导入失败: {e}")
        exit(1)
    
    # 2. 测试uvicorn导入
    try:
        import uvicorn
        print(f"✅ Uvicorn 导入成功")
    except Exception as e:
        print(f"❌ Uvicorn 导入失败: {e}")
        exit(1)
    
    # 3. 测试配置导入
    try:
        from app.core.config import settings
        print(f"✅ 配置导入成功，数据库: {settings.DATABASE_URL}")
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        exit(1)
    
    # 4. 测试数据库连接
    try:
        from app.core.database import check_database_connection
        if check_database_connection():
            print("✅ 数据库连接成功")
        else:
            print("❌ 数据库连接失败")
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
    
    # 5. 测试main模块
    try:
        import main
        print(f"✅ Main模块导入成功: {main.app}")
    except Exception as e:
        print(f"❌ Main模块导入失败: {e}")
        exit(1)
    
    # 6. 启动服务器
    print("\n=== 启动服务器 ===")
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc() 