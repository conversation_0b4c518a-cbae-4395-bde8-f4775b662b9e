"""
DocumentService服务的单元测试
"""
import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from pathlib import Path

from app.services.document_service import (
    DocumentService, DocumentValidationError, DocumentServiceError
)
from app.schemas.document import DocumentUpdate, ProcessingStatusEnum
from app.models.document import Document, ProcessingStatus
from app.services.n8n_client import N8nClient, N8nAPIError


class TestDocumentService:
    """DocumentService测试类"""
    
    @pytest.fixture
    def mock_n8n_client(self):
        """模拟n8n客户端"""
        client = Mock(spec=N8nClient)
        client.upload_document = AsyncMock(return_value=Mock(
            processing_status="completed",
            chunks_count=5,
            message="处理成功"
        ))
        client.health_check = AsyncMock(return_value={"status": "healthy"})
        return client
    
    @pytest.fixture
    def temp_upload_dir(self):
        """创建临时上传目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def document_service(self, mock_n8n_client, temp_upload_dir):
        """创建DocumentService实例"""
        return DocumentService(
            n8n_client=mock_n8n_client,
            upload_dir=temp_upload_dir
        )
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        session.refresh = Mock()
        session.query = Mock()
        return session
    
    def test_validate_file_success(self, document_service):
        """测试文件验证成功"""
        # 测试有效的PDF文件
        file_type, mime_type = document_service.validate_file(
            filename="test.pdf",
            file_size=1024 * 1024,  # 1MB
            content_type="application/pdf"
        )
        
        assert file_type == ".pdf"
        assert mime_type == "application/pdf"
        
        # 测试有效的文本文件
        file_type, mime_type = document_service.validate_file(
            filename="document.txt",
            file_size=5000
        )
        
        assert file_type == ".txt"
        assert mime_type == "text/plain"
    
    def test_validate_file_invalid_filename(self, document_service):
        """测试无效文件名验证"""
        with pytest.raises(DocumentValidationError) as exc_info:
            document_service.validate_file("", 1000)
        
        assert "文件名不能为空" in str(exc_info.value)
        assert exc_info.value.field == "filename"
    
    def test_validate_file_unsupported_type(self, document_service):
        """测试不支持的文件类型"""
        with pytest.raises(DocumentValidationError) as exc_info:
            document_service.validate_file("test.xyz", 1000)
        
        assert "不支持的文件类型" in str(exc_info.value)
        assert exc_info.value.field == "file_type"
    
    def test_validate_file_invalid_size(self, document_service):
        """测试无效文件大小"""
        # 测试零大小
        with pytest.raises(DocumentValidationError) as exc_info:
            document_service.validate_file("test.txt", 0)
        
        assert "文件大小必须大于0" in str(exc_info.value)
        assert exc_info.value.field == "file_size"
        
        # 测试过大文件
        with pytest.raises(DocumentValidationError) as exc_info:
            document_service.validate_file("test.txt", 100 * 1024 * 1024)  # 100MB
        
        assert "超过限制" in str(exc_info.value)
        assert exc_info.value.field == "file_size"
    
    @pytest.mark.asyncio
    async def test_upload_document_success(self, document_service, mock_db_session):
        """测试文档上传成功"""
        # 准备测试数据
        file_content = b"这是一个测试文档的内容"
        filename = "test.txt"
        
        # 模拟数据库操作
        mock_document = Mock()
        mock_document.id = "test-doc-123"
        mock_document.upload_time = datetime.now()
        mock_document.processing_status = ProcessingStatus.COMPLETED.value
        
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'test-doc-123')
        
        # 执行上传
        response = await document_service.upload_document(
            file_content=file_content,
            filename=filename,
            content_type="text/plain",
            db=mock_db_session
        )
        
        # 验证结果
        assert response.filename == filename
        assert response.file_size == len(file_content)
        assert response.file_size_mb == len(file_content) / (1024 * 1024)
        assert response.processing_status in [
            ProcessingStatusEnum.PENDING,
            ProcessingStatusEnum.PROCESSING,
            ProcessingStatusEnum.COMPLETED
        ]
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called()
    
    @pytest.mark.asyncio
    async def test_upload_document_validation_error(self, document_service, mock_db_session):
        """测试上传文档验证失败"""
        with pytest.raises(DocumentValidationError):
            await document_service.upload_document(
                file_content=b"content",
                filename="test.xyz",  # 不支持的文件类型
                db=mock_db_session
            )
        
        # 验证没有进行数据库操作
        mock_db_session.add.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_upload_document_n8n_failure(self, document_service, mock_db_session, mock_n8n_client):
        """测试n8n处理失败的情况"""
        # 模拟n8n失败
        mock_n8n_client.upload_document.side_effect = N8nAPIError("n8n服务不可用", status_code=503)
        
        # 模拟数据库操作
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'test-doc-123')
        
        # 执行上传（不应该抛出异常，而是标记为失败）
        response = await document_service.upload_document(
            file_content=b"test content",
            filename="test.txt",
            db=mock_db_session
        )
        
        # 验证文档记录仍然创建
        assert response.filename == "test.txt"
        mock_db_session.add.assert_called()
    
    def test_list_documents_basic(self, document_service, mock_db_session):
        """测试基本文档列表功能"""
        # 模拟查询结果
        mock_documents = [
            Mock(
                id="doc1", filename="test1.txt", file_type=".txt",
                file_size=1000, upload_time=datetime.now(),
                processing_status="completed", chunks_count=3,
                error_message=None
            ),
            Mock(
                id="doc2", filename="test2.pdf", file_type=".pdf",
                file_size=2000, upload_time=datetime.now(),
                processing_status="processing", chunks_count=0,
                error_message=None
            )
        ]
        
        # 为每个mock添加属性方法
        for doc in mock_documents:
            doc.file_size_mb = doc.file_size / (1024 * 1024)
            doc.is_processed = doc.processing_status == "completed"
            doc.is_processing = doc.processing_status == "processing"
            doc.has_error = doc.processing_status == "failed"
        
        mock_query = Mock()
        mock_query.count.return_value = len(mock_documents)
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = mock_documents
        mock_query.order_by.return_value = mock_query
        
        mock_db_session.query.return_value = mock_query
        
        # 执行查询
        result = document_service.list_documents(page=1, page_size=10, db=mock_db_session)
        
        # 验证结果
        assert result["total"] == 2
        assert len(result["documents"]) == 2
        assert result["page"] == 1
        assert result["page_size"] == 10
        assert result["total_pages"] == 1
        
        # 验证文档内容
        doc_response = result["documents"][0]
        assert doc_response.id == "doc1"
        assert doc_response.filename == "test1.txt"
    
    def test_list_documents_with_filters(self, document_service, mock_db_session):
        """测试带过滤条件的文档列表"""
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 1
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [Mock(
            id="doc1", filename="test.txt", file_type=".txt",
            file_size=1000, upload_time=datetime.now(),
            processing_status="completed", chunks_count=3,
            error_message=None, file_size_mb=1.0,
            is_processed=True, is_processing=False, has_error=False
        )]
        
        mock_db_session.query.return_value = mock_query
        
        # 执行带过滤的查询
        result = document_service.list_documents(
            page=1,
            page_size=10,
            status_filter=[ProcessingStatusEnum.COMPLETED],
            file_type_filter=[".txt"],
            search_query="test",
            db=mock_db_session
        )
        
        # 验证结果
        assert result["total"] == 1
        assert len(result["documents"]) == 1
        
        # 验证filter被调用了多次（状态、类型、搜索）
        assert mock_query.filter.call_count >= 3
    
    def test_get_document_exists(self, document_service, mock_db_session):
        """测试获取存在的文档"""
        mock_document = Mock(
            id="doc1", filename="test.txt", file_type=".txt",
            file_size=1000, upload_time=datetime.now(),
            processing_status="completed", chunks_count=3,
            error_message=None, file_size_mb=1.0,
            is_processed=True, is_processing=False, has_error=False
        )
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        mock_db_session.query.return_value = mock_query
        
        # 执行查询
        result = document_service.get_document("doc1", db=mock_db_session)
        
        # 验证结果
        assert result is not None
        assert result.id == "doc1"
        assert result.filename == "test.txt"
    
    def test_get_document_not_exists(self, document_service, mock_db_session):
        """测试获取不存在的文档"""
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # 执行查询
        result = document_service.get_document("nonexistent", db=mock_db_session)
        
        # 验证结果
        assert result is None
    
    def test_update_document_success(self, document_service, mock_db_session):
        """测试文档更新成功"""
        mock_document = Mock(
            id="doc1", filename="old_name.txt", file_type=".txt",
            file_size=1000, upload_time=datetime.now(),
            processing_status="processing", chunks_count=0,
            error_message=None
        )
        
        # 添加属性方法
        mock_document.file_size_mb = 1.0
        mock_document.is_processed = False
        mock_document.is_processing = True
        mock_document.has_error = False
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        mock_db_session.query.return_value = mock_query
        
        # 准备更新数据
        update_data = DocumentUpdate(
            filename="new_name.txt",
            processing_status=ProcessingStatusEnum.COMPLETED,
            chunks_count=5
        )
        
        # 执行更新
        result = document_service.update_document("doc1", update_data, db=mock_db_session)
        
        # 验证结果
        assert result is not None
        assert mock_document.filename == "new_name.txt"
        assert mock_document.processing_status == ProcessingStatusEnum.COMPLETED
        assert mock_document.chunks_count == 5
        
        mock_db_session.commit.assert_called_once()
    
    def test_delete_document_soft_delete(self, document_service, mock_db_session):
        """测试软删除文档"""
        mock_document = Mock(
            id="doc1", filename="test.txt",
            processing_status="completed"
        )
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        mock_db_session.query.return_value = mock_query
        
        # 执行软删除
        result = document_service.delete_document("doc1", soft_delete=True, db=mock_db_session)
        
        # 验证结果
        assert result is True
        assert mock_document.processing_status == ProcessingStatus.FAILED.value
        assert mock_document.error_message == "文档已被删除"
        
        mock_db_session.commit.assert_called_once()
        mock_db_session.delete.assert_not_called()
    
    def test_delete_document_hard_delete(self, document_service, mock_db_session):
        """测试硬删除文档"""
        mock_document = Mock(id="doc1", filename="test.txt")
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        mock_db_session.query.return_value = mock_query
        
        # 执行硬删除
        result = document_service.delete_document("doc1", soft_delete=False, db=mock_db_session)
        
        # 验证结果
        assert result is True
        
        mock_db_session.delete.assert_called_once_with(mock_document)
        mock_db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_processing_status(self, document_service, mock_db_session):
        """测试获取处理状态"""
        mock_document = Mock(
            id="doc1", filename="test.txt",
            processing_status="processing",
            chunks_count=3,
            error_message=None
        )
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        mock_db_session.query.return_value = mock_query
        
        # 执行查询
        result = await document_service.get_processing_status("doc1", db=mock_db_session)
        
        # 验证结果
        assert result is not None
        assert result.document_id == "doc1"
        assert result.filename == "test.txt"
        assert result.processing_status == ProcessingStatusEnum.PROCESSING
        assert result.progress == 50.0  # 处理中状态的估算进度
        assert result.chunks_count == 3
    
    def test_get_statistics(self, document_service, mock_db_session):
        """测试获取统计信息"""
        # 模拟各种查询结果
        mock_db_session.query.return_value.count.return_value = 10  # 总文档数
        mock_db_session.query.return_value.filter.return_value.count.side_effect = [3, 2, 4, 1]  # 各状态数量
        mock_db_session.query.return_value.group_by.return_value.all.return_value = [
            ('.txt', 5), ('.pdf', 3), ('.docx', 2)
        ]  # 类型统计
        mock_db_session.query.return_value.scalar.return_value = 50 * 1024 * 1024  # 总大小
        
        # 执行统计
        stats = document_service.get_statistics(db=mock_db_session)
        
        # 验证结果
        assert stats["total_documents"] == 10
        assert "status_distribution" in stats
        assert "type_distribution" in stats
        assert stats["total_size_mb"] == 50.0
        assert "supported_types" in stats
    
    def test_cleanup_failed_documents(self, document_service, mock_db_session):
        """测试清理失败文档"""
        # 模拟要清理的文档
        failed_docs = [
            Mock(id="doc1", filename="failed1.txt"),
            Mock(id="doc2", filename="failed2.txt")
        ]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = failed_docs
        mock_db_session.query.return_value = mock_query
        
        # 执行清理
        cleaned_count = document_service.cleanup_failed_documents(
            older_than_hours=24,
            db=mock_db_session
        )
        
        # 验证结果
        assert cleaned_count == 2
        
        # 验证删除操作
        for doc in failed_docs:
            mock_db_session.delete.assert_any_call(doc)
        
        mock_db_session.commit.assert_called_once()
    
    def test_supported_file_types(self, document_service):
        """测试支持的文件类型配置"""
        expected_types = ['.txt', '.md', '.pdf', '.docx', '.doc', '.rtf', '.html', '.htm']
        
        for file_type in expected_types:
            assert file_type in document_service.supported_types
            
        # 验证每种类型都有对应的MIME类型
        for file_type, mime_type in document_service.supported_types.items():
            assert isinstance(mime_type, str)
            assert len(mime_type) > 0
    
    def test_upload_directory_creation(self, temp_upload_dir, mock_n8n_client):
        """测试上传目录创建"""
        # 使用不存在的目录
        non_existent_dir = os.path.join(temp_upload_dir, "non_existent")
        
        # 创建服务应该自动创建目录
        service = DocumentService(
            n8n_client=mock_n8n_client,
            upload_dir=non_existent_dir
        )
        
        # 验证目录被创建
        assert os.path.exists(non_existent_dir)
        assert os.path.isdir(non_existent_dir)


# 集成测试示例
class TestDocumentServiceIntegration:
    """DocumentService集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_document_lifecycle(self):
        """测试完整的文档生命周期"""
        # 这里可以添加真实的集成测试
        # 需要实际的数据库和可能的n8n服务
        pass


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])