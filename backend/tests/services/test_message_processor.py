"""
MessageProcessor服务的单元测试
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.services.message_processor import (
    MessageProcessor, MessageType, ProcessingStatus, MessageProcessingError
)
from app.schemas.chat import ChatRequest, ChatResponse, MessageRole
from app.services.chat_service import ChatService
from app.services.n8n_client import N8nClient, N8nAPIError


class TestMessageProcessor:
    """MessageProcessor测试类"""
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = Mock(spec=ChatService)
        service.create_session.return_value = Mock(id="test-session-123", title="Test Session")
        service.get_session.return_value = Mock(id="test-session-123", title="Test Session")
        service.add_message.return_value = Mock(id="test-message-456", content="Test message")
        service.get_session_messages.return_value = []
        service.get_cache_stats.return_value = {"cached_sessions": 0}
        return service
    
    @pytest.fixture
    def mock_n8n_client(self):
        """模拟n8n客户端"""
        client = Mock(spec=N8nClient)
        client.query_chat = AsyncMock(return_value=ChatResponse(
            message="测试AI回复",
            session_id="test-session-123",
            message_id="ai-message-789",
            sources=[],
            created_at=datetime.now()
        ))
        client.health_check = AsyncMock(return_value={"status": "healthy"})
        return client
    
    @pytest.fixture
    def processor(self, mock_chat_service, mock_n8n_client):
        """创建MessageProcessor实例"""
        return MessageProcessor(
            chat_service=mock_chat_service,
            n8n_client=mock_n8n_client,
            max_retries=2,
            retry_delays=[0.1, 0.2]  # 更短的延迟用于测试
        )
    
    @pytest.mark.asyncio
    async def test_process_standard_message_success(self, processor, mock_chat_service, mock_n8n_client):
        """测试标准消息处理成功场景"""
        # 准备测试数据
        request = ChatRequest(
            message="你好，AI！",
            session_id="test-session-123",
            stream=False
        )
        
        # 执行测试
        response = await processor.process_message(request, MessageType.TEXT)
        
        # 验证结果
        assert isinstance(response, ChatResponse)
        assert response.message == "测试AI回复"
        assert response.session_id == "test-session-123"
        
        # 验证服务调用
        mock_chat_service.add_message.assert_called()
        mock_n8n_client.query_chat.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_message_new_session(self, processor, mock_chat_service, mock_n8n_client):
        """测试创建新会话的消息处理"""
        # 模拟没有现有会话
        mock_chat_service.get_session.return_value = None
        
        request = ChatRequest(
            message="新会话测试",
            session_id=None,  # 不提供会话ID
            stream=False
        )
        
        # 执行测试
        response = await processor.process_message(request, MessageType.TEXT)
        
        # 验证创建了新会话
        mock_chat_service.create_session.assert_called_once()
        assert response.session_id == "test-session-123"
    
    @pytest.mark.asyncio
    async def test_process_message_with_retry(self, processor, mock_chat_service, mock_n8n_client):
        """测试重试机制"""
        # 模拟第一次调用失败，第二次成功
        mock_n8n_client.query_chat.side_effect = [
            N8nAPIError("临时失败", status_code=503),
            ChatResponse(
                message="重试成功",
                session_id="test-session-123",
                message_id="retry-message",
                sources=[],
                created_at=datetime.now()
            )
        ]
        
        request = ChatRequest(
            message="重试测试",
            session_id="test-session-123",
            stream=False
        )
        
        # 执行测试
        response = await processor.process_message(request, MessageType.TEXT)
        
        # 验证重试成功
        assert response.message == "重试成功"
        assert mock_n8n_client.query_chat.call_count == 2
    
    @pytest.mark.asyncio
    async def test_process_message_retry_exhausted(self, processor, mock_chat_service, mock_n8n_client):
        """测试重试次数用尽"""
        # 模拟所有调用都失败
        mock_n8n_client.query_chat.side_effect = N8nAPIError("持续失败", status_code=503)
        
        request = ChatRequest(
            message="失败测试",
            session_id="test-session-123",
            stream=False
        )
        
        # 执行测试并验证异常
        with pytest.raises(MessageProcessingError) as exc_info:
            await processor.process_message(request, MessageType.TEXT)
        
        assert "AI服务调用失败" in str(exc_info.value)
        assert exc_info.value.error_type == "ai_service_error"
        # 验证重试了正确的次数 (初始 + 2次重试 = 3次)
        assert mock_n8n_client.query_chat.call_count == 3
    
    @pytest.mark.asyncio
    async def test_process_stream_message(self, processor, mock_chat_service, mock_n8n_client):
        """测试流式消息处理"""
        # 模拟流式响应
        async def mock_stream():
            for chunk in ["你好", "！", "这是", "流式", "响应"]:
                yield chunk
        
        mock_n8n_client.query_chat.return_value = mock_stream()
        
        request = ChatRequest(
            message="流式测试",
            session_id="test-session-123",
            stream=True
        )
        
        # 执行测试
        stream_generator = await processor.process_message(request, MessageType.TEXT)
        
        # 收集所有流式响应
        chunks = []
        async for chunk_json in stream_generator:
            import json
            chunk_data = json.loads(chunk_json)
            chunks.append(chunk_data)
        
        # 验证结果
        assert len(chunks) > 1  # 至少有内容块和完成标志
        assert any(chunk.get("is_complete", False) for chunk in chunks)  # 有完成标志
        
        # 验证添加了完整响应到历史
        mock_chat_service.add_message.assert_called()
    
    def test_message_type_enhancement(self, processor):
        """测试不同消息类型的增强"""
        # 测试文档问答增强
        enhanced = processor._enhance_message_for_document_qa("什么是AI？")
        assert "基于已上传的文档" in enhanced
        assert "什么是AI？" in enhanced
        
        # 测试构建AI请求
        request = ChatRequest(message="原始消息", session_id="test", stream=False)
        
        # 文档问答类型
        ai_request = processor._build_ai_request("test", request, MessageType.DOCUMENT_QA, None)
        assert "基于已上传的文档" in ai_request.message
        
        # 摘要类型
        ai_request = processor._build_ai_request("test", request, MessageType.SUMMARY, None)
        assert "请总结以下内容" in ai_request.message
        
        # 代码帮助类型
        ai_request = processor._build_ai_request("test", request, MessageType.CODE_HELP, None)
        assert "作为编程助手" in ai_request.message
        
        # 翻译类型
        ai_request = processor._build_ai_request("test", request, MessageType.TRANSLATION, None)
        assert "请翻译以下内容" in ai_request.message
    
    def test_processing_status_management(self, processor):
        """测试处理状态管理"""
        processing_id = "test-processing-123"
        
        # 更新状态
        processor._update_processing_status(processing_id, ProcessingStatus.PROCESSING)
        
        # 获取状态
        status = processor.get_processing_status(processing_id)
        assert status is not None
        assert status["status"] == ProcessingStatus.PROCESSING
        assert status["processing_id"] == processing_id
        
        # 清理旧状态（由于是刚创建的，不应该被清理）
        removed_count = processor.clear_old_processing_status(max_age_minutes=60)
        assert removed_count == 0
        
        # 验证状态仍然存在
        status = processor.get_processing_status(processing_id)
        assert status is not None
    
    @pytest.mark.asyncio
    async def test_get_message_history(self, processor, mock_chat_service):
        """测试获取消息历史"""
        # 模拟消息历史
        mock_messages = [
            Mock(
                id="msg1", role="user", content="用户消息1",
                created_at=datetime.now(), sources=None
            ),
            Mock(
                id="msg2", role="assistant", content="AI回复1",
                created_at=datetime.now(), sources={"sources": []}
            )
        ]
        mock_chat_service.get_session_messages.return_value = mock_messages
        
        # 执行测试
        history = await processor.get_message_history("test-session-123", limit=10)
        
        # 验证结果
        assert len(history) == 2
        assert history[0]["role"] == "user"
        assert history[1]["role"] == "assistant"
        assert all("id" in msg for msg in history)
        assert all("content" in msg for msg in history)
    
    @pytest.mark.asyncio
    async def test_health_check(self, processor, mock_chat_service, mock_n8n_client):
        """测试健康检查"""
        # 执行健康检查
        health = await processor.health_check()
        
        # 验证结果
        assert health["processor_status"] == "healthy"
        assert "processing_sessions" in health
        assert health["chat_service"] == "healthy"
        assert health["n8n_service"] == "healthy"
        assert "timestamp" in health
        
        # 验证调用了相关服务的健康检查
        mock_chat_service.get_cache_stats.assert_called_once()
        mock_n8n_client.health_check.assert_called_once()


# 集成测试示例
class TestMessageProcessorIntegration:
    """MessageProcessor集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_message_flow(self):
        """测试完整的消息处理流程"""
        # 这里可以添加真实的集成测试
        # 需要实际的数据库和可能的n8n服务
        pass


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])