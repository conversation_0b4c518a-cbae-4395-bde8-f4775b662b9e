CREATE TABLE chat_messages (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    sources J<PERSON><PERSON> NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON>N KEY (session_id) REFERENCES chat_sessions(id)
);

CREATE TABLE chat_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NULL,
    title VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE documents (
    id VARCHAR(36) PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INTEGER NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_status VARCHAR(20) DEFAULT 'uploading',
    chunks_count INTEGER DEFAULT 0,
    error_message TEXT NULL
);

CREATE INDEX idx_messages_session_time ON chat_messages(session_id, created_at);

-- 文档状态查询优化  
CREATE INDEX idx_documents_status ON documents(processing_status);

-- 会话更新时间排序
CREATE INDEX idx_sessions_updated ON chat_sessions(updated_at);