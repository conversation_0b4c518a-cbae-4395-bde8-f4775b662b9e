-- 创建扩展（如果需要生成 UUID）
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 会话表
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(36) NULL,
    title VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE chat_sessions IS '存储聊天会话信息';
COMMENT ON COLUMN chat_sessions.user_id IS '关联的用户ID';
COMMENT ON COLUMN chat_sessions.title IS '会话标题';
COMMENT ON COLUMN chat_sessions.created_at IS '创建时间';
COMMENT ON COLUMN chat_sessions.updated_at IS '最后更新时间';

-- 消息表
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    sources <PERSON><PERSON><PERSON> NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
);

COMMENT ON TABLE chat_messages IS '存储聊天消息记录';
COMMENT ON COLUMN chat_messages.session_id IS '关联的会话ID';
COMMENT ON COLUMN chat_messages.role IS '消息角色：user/assistant/system';
COMMENT ON COLUMN chat_messages.content IS '消息内容';
COMMENT ON COLUMN chat_messages.sources IS '消息来源的JSON数据';
COMMENT ON COLUMN chat_messages.created_at IS '消息创建时间';

-- 文档表
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INTEGER NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_status VARCHAR(20) DEFAULT 'uploading',
    chunks_count INTEGER DEFAULT 0,
    error_message TEXT NULL
);

COMMENT ON TABLE documents IS '存储上传的文档信息';
COMMENT ON COLUMN documents.filename IS '原始文件名';
COMMENT ON COLUMN documents.file_type IS '文件类型';
COMMENT ON COLUMN documents.file_size IS '文件大小(字节)';
COMMENT ON COLUMN documents.upload_time IS '上传时间';
COMMENT ON COLUMN documents.processing_status IS '处理状态: uploading/processing/completed/failed';
COMMENT ON COLUMN documents.chunks_count IS '文档分块数量';
COMMENT ON COLUMN documents.error_message IS '处理错误信息';

-- 创建索引
CREATE INDEX idx_messages_session_time ON chat_messages(session_id, created_at);
CREATE INDEX idx_documents_status ON documents(processing_status);
CREATE INDEX idx_sessions_updated ON chat_sessions(updated_at);
CREATE INDEX idx_messages_sources ON chat_messages USING gin(sources);

-- 自动更新触发器函数
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为会话表添加自动更新触发器
CREATE TRIGGER update_chat_sessions_updated_at
BEFORE UPDATE ON chat_sessions
FOR EACH ROW EXECUTE FUNCTION update_updated_at();