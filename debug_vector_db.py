#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试向量数据库状态
"""

import requests
import time

def test_upload_and_query():
    """测试上传和查询的完整流程"""
    print("🔍 调试向量数据库状态...")
    print("=" * 60)
    
    # 1. 先上传一个简单的文档
    print("📤 步骤1: 上传测试文档...")
    test_content = """人工智能（AI）是计算机科学的一个分支。
机器学习是AI的重要组成部分。
深度学习是机器学习的子集。"""
    
    files = {
        'file': ('debug_test.txt', test_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'filename': 'debug_test.txt'
    }
    
    try:
        upload_response = requests.post(
            "http://localhost:5678/webhook/rag-upload",
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"上传状态码: {upload_response.status_code}")
        print(f"上传响应内容: {upload_response.text}")
        print(f"上传响应头: {dict(upload_response.headers)}")

        if upload_response.status_code == 200:
            try:
                result = upload_response.json()
                print(f"✅ 上传成功 - 文档ID: {result.get('document_id')}")
                print(f"   分块数: {result.get('chunks_count')}")
            except Exception as json_error:
                print(f"⚠️ 上传成功但响应不是JSON: {json_error}")
                print("   继续测试查询功能...")
        else:
            print(f"❌ 上传失败: {upload_response.text}")
            return
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return
    
    # 2. 等待处理完成
    print("\n⏳ 步骤2: 等待5秒让向量处理完成...")
    time.sleep(5)
    
    # 3. 测试标准格式查询
    print("\n🔍 步骤3: 测试标准格式查询...")
    query_data = {
        "query": "什么是人工智能？",
        "stream": False
    }
    
    try:
        query_response = requests.post(
            "http://localhost:5678/webhook/rag-query",
            json=query_data,
            timeout=30
        )
        
        print(f"查询状态码: {query_response.status_code}")
        if query_response.status_code == 200:
            result = query_response.json()
            print(f"✅ 查询成功")
            print(f"   回答长度: {len(result.get('answer', ''))}")
            print(f"   相关文档数: {len(result.get('sources', []))}")
            print(f"   数据库大小: {result.get('database_size', 'N/A')}")
            
            # 显示回答内容（前200字符）
            answer = result.get('answer', '')
            if answer:
                print(f"   回答预览: {answer[:200]}...")
            
            # 显示相关文档
            sources = result.get('sources', [])
            if sources:
                print(f"   相关文档预览:")
                for i, source in enumerate(sources[:2]):
                    similarity = source.get('similarity', 0)
                    content = source.get('content', '')[:100]
                    print(f"     [{i}] 相似度: {similarity:.4f}, 内容: {content}...")
            else:
                print("   ⚠️ 没有找到相关文档")
        else:
            print(f"❌ 查询失败: {query_response.text}")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
    
    # 4. 测试前端格式查询
    print("\n🔍 步骤4: 测试前端格式查询...")
    frontend_query_data = {
        "message": "什么是机器学习？",
        "session_id": "debug_session_001"
    }
    
    try:
        frontend_response = requests.post(
            "http://localhost:5678/webhook/rag-query",
            json=frontend_query_data,
            timeout=30
        )
        
        print(f"前端查询状态码: {frontend_response.status_code}")
        if frontend_response.status_code == 200:
            result = frontend_response.json()
            print(f"✅ 前端查询成功")
            print(f"   回答长度: {len(result.get('answer', ''))}")
            print(f"   相关文档数: {len(result.get('sources', []))}")
            print(f"   会话ID: {result.get('session_id', 'N/A')}")
            print(f"   数据库大小: {result.get('database_size', 'N/A')}")
            
            # 显示回答内容（前200字符）
            answer = result.get('answer', '')
            if answer:
                print(f"   回答预览: {answer[:200]}...")
            else:
                print("   ⚠️ 没有生成回答")
        else:
            print(f"❌ 前端查询失败: {frontend_response.text}")
            
    except Exception as e:
        print(f"❌ 前端查询异常: {e}")
    
    print("\n" + "=" * 60)
    print("🔧 调试建议:")
    print("1. 检查n8n执行日志中的console.log输出")
    print("2. 确认向量数据库大小是否 > 0")
    print("3. 确认Ollama服务正常运行")
    print("4. 检查向量嵌入是否正确生成")

if __name__ == "__main__":
    test_upload_and_query()
