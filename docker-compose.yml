version: '3.8'

services:
  # 前端服务
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WEBSOCKET_URL=ws://localhost:8000
      - REACT_APP_MAX_FILE_SIZE=104857600
    depends_on:
      - backend
    networks:
      - app-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://aichatopr:Aiop1234@localhost:5432/aichatdb
      - N8N_WEBHOOK_BASE_URL=http://localhost:5678
      - MAX_FILE_SIZE=104857600
      - RATE_LIMIT_PER_MINUTE=60
      - DEBUG=false
    volumes:
      - ./data:/app/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  app-data: