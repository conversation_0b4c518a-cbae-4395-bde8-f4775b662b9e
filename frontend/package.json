{"name": "ai-knowledge-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 100", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@tanstack/query-persist-client-core": "^5.83.0", "@tanstack/query-sync-storage-persister": "^5.83.0", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.83.0", "@types/react-syntax-highlighter": "^15.5.10", "@types/react-window": "^1.8.8", "axios": "^1.6.2", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "react-router-dom": "^6.18.0", "react-syntax-highlighter": "^15.5.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-window-infinite-loader": "^1.0.9", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}}