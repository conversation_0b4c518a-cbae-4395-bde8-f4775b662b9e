import { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Components
import Header from './components/common/Header';
import { ChatInterface } from './components/chat/ChatInterface';
import { DocumentManager } from './components/document/DocumentManager';
import { SourceSidebar } from './components/sources/SourceSidebar';
import { AnimationProvider, SlideTransition, FadeTransition } from './components/animation';

// Hooks
import { useTheme } from './hooks/useTheme';
import { useResponsive, useMobileScroll } from './hooks';

// Types
import { SourceReference, ChatSessionResponse, ChatMessageResponse, MessageRole } from './types/chat';

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
});

function App() {
  // 初始化主题系统
  useTheme();
  
  // 响应式设计
  const { isMobile, isTablet, isDesktop, isTouchDevice } = useResponsive();
  const { getScrollStyles } = useMobileScroll();

  // 模拟数据 - 临时使用，后续将替换为实际的hook
  const [currentSession, setCurrentSession] = useState<ChatSessionResponse | null>(null);
  const [messages, setMessages] = useState<ChatMessageResponse[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);

  // 布局状态
  const [isDocumentsOpen, setIsDocumentsOpen] = useState(!isMobile && !isTablet);
  const [isSourcesOpen, setIsSourcesOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [currentSources, setCurrentSources] = useState<SourceReference[]>([]);

  // 根据屏幕尺寸调整默认布局
  useEffect(() => {
    if (isMobile || isTablet) {
      setIsDocumentsOpen(false);
      setIsSourcesOpen(false);
    } else if (isDesktop) {
      setIsDocumentsOpen(true);
    }
  }, [isMobile, isTablet, isDesktop]);

  // 模拟函数 - 临时使用
  const handleNewSession = async () => {
    const newSession: ChatSessionResponse = {
      id: Date.now().toString(),
      title: '新对话',
      created_at: new Date().toISOString(),
      messages_count: 0
    };
    setCurrentSession(newSession);
    setMessages([]);
    setCurrentSources([]);
  };

  const handleSendMessage = async (message: string) => {
    if (!currentSession) {
      await handleNewSession();
    }

    const userMessage: ChatMessageResponse = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: message,
      session_id: currentSession?.id || '',
      created_at: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsStreaming(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiMessage: ChatMessageResponse = {
        id: (Date.now() + 1).toString(),
        role: MessageRole.ASSISTANT,
        content: '这是一个模拟的AI回复。在实际应用中，这里会调用后端API来获取真实的AI生成回答。',
        session_id: currentSession?.id || '',
        created_at: new Date().toISOString(),
        sources: {
          source1: {
            content: '这是一个模拟的文档片段内容，用于演示源文档引用功能。在实际应用中，这里会显示真实的文档内容。',
            similarity: 0.85,
            document_name: '示例文档.pdf',
            document_id: 'doc1',
            page: 1
          }
        }
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsStreaming(false);

      // 设置模拟源文档
      setCurrentSources([{
        content: '这是一个模拟的文档片段内容，用于演示源文档引用功能。在实际应用中，这里会显示真实的文档内容。',
        similarity: 0.85,
        document_name: '示例文档.pdf',
        document_id: 'doc1',
        page: 1
      }]);
    }, 1500);
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (currentSession?.id === sessionId) {
      setCurrentSession(null);
      setMessages([]);
      setCurrentSources([]);
    }
  };

  const handleSourceClick = (source: SourceReference) => {
    console.log('源文档点击:', source);
  };

  // 计算布局样式
  const getLayoutStyles = () => {
    if (isMobile) {
      return {
        documentsWidth: 'w-full',
        chatWidth: 'w-full',
        documentsClass: isDocumentsOpen ? 'block' : 'hidden',
        chatClass: !isDocumentsOpen ? 'block' : 'hidden',
      };
    } else if (isTablet) {
      return {
        documentsWidth: isDocumentsOpen ? 'w-1/3' : 'w-0',
        chatWidth: isDocumentsOpen ? 'w-2/3' : 'w-full',
        documentsClass: isDocumentsOpen ? 'block' : 'hidden',
        chatClass: 'block',
      };
    } else {
      // 桌面端
      let documentsWidth = 'w-0';
      let chatWidth = 'w-full';

      if (isDocumentsOpen && !isSourcesOpen) {
        documentsWidth = 'w-1/3';
        chatWidth = 'w-2/3';
      } else if (!isDocumentsOpen && isSourcesOpen) {
        documentsWidth = 'w-0';
        chatWidth = 'w-2/3';
      } else if (isDocumentsOpen && isSourcesOpen) {
        documentsWidth = 'w-1/4';
        chatWidth = 'w-1/2';
      }

      return {
        documentsWidth,
        chatWidth,
        documentsClass: isDocumentsOpen ? 'block' : 'hidden',
        chatClass: 'block',
      };
    }
  };

  const layoutStyles = getLayoutStyles();

  return (
    <QueryClientProvider client={queryClient}>
      <AnimationProvider>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <Header
          onNewSession={handleNewSession}
          onToggleDocuments={() => setIsDocumentsOpen(!isDocumentsOpen)}
          onToggleSources={() => setIsSourcesOpen(!isSourcesOpen)}
          isDocumentsOpen={isDocumentsOpen}
          isSourcesOpen={isSourcesOpen}
          onToggleMobileMenu={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          isMobileMenuOpen={isMobileMenuOpen}
        />

        {/* 主要内容区域 */}
        <div 
          className="flex h-[calc(100vh-4rem)] overflow-hidden"
          style={isTouchDevice ? getScrollStyles() : undefined}
        >
          {/* 文档管理侧边栏 */}
          <SlideTransition
            show={isDocumentsOpen}
            direction="left"
            duration={300}
            className={`${layoutStyles.documentsWidth} border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800`}
          >
            <DocumentManager
              className="h-full"
              onDocumentSelect={(doc) => console.log('文档选择:', doc)}
              onDocumentView={(docId) => console.log('查看文档:', docId)}
            />
          </SlideTransition>

          {/* 聊天主区域 */}
          <FadeTransition
            show={true}
            duration={200}
            className={`${layoutStyles.chatWidth} ${layoutStyles.chatClass} flex flex-col bg-white dark:bg-gray-800`}
          >
            <ChatInterface
              sessionId={currentSession?.id || ''}
              {...(currentSession && { currentSession })}
              messages={messages}
              isStreaming={isStreaming}
              onMessageSend={handleSendMessage}
              onSessionCreate={handleNewSession}
              onSessionDelete={handleDeleteSession}
              className="flex-1"
            />
          </FadeTransition>

          {/* 源文档引用侧边栏 - 桌面端 */}
          {!isMobile && (
            <SlideTransition
              show={isSourcesOpen}
              direction="right"
              duration={300}
              className="w-1/4 border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
              unmountOnExit={true}
            >
              <div className="h-full p-4 overflow-y-auto">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  相关文档
                </h3>
                {currentSources.length > 0 ? (
                  <div className="space-y-4">
                    {currentSources.map((source, index) => (
                      <div 
                        key={index} 
                        className="border border-gray-200 dark:border-gray-600 rounded-lg p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => handleSourceClick(source)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            引用 {index + 1}
                          </span>
                          <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                            {Math.round(source.similarity * 100)}% 匹配
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                          {source.content.length > 200 
                            ? source.content.slice(0, 200) + '...' 
                            : source.content
                          }
                        </p>
                        {source.document_name && (
                          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            来源: {source.document_name}
                            {source.page && ` (第${source.page}页)`}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
                    <div className="text-4xl mb-2">📚</div>
                    <p>暂无相关文档引用</p>
                    <p className="text-sm mt-1">发送消息后这里会显示相关的文档片段</p>
                  </div>
                )}
              </div>
            </SlideTransition>
          )}
        </div>

        {/* 移动端源文档引用 - 底部抽屉 */}
        <SourceSidebar
          sources={currentSources}
          isOpen={isMobile && isSourcesOpen}
          onToggle={() => setIsSourcesOpen(!isSourcesOpen)}
          onSourceClick={handleSourceClick}
        />

        {/* 背景遮罩 - 移动端菜单 */}
        {isMobileMenuOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}
      </div>
      </AnimationProvider>
    </QueryClientProvider>
  );
}

export default App; 