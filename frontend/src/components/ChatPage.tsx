import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { Send, Bot, User } from 'lucide-react'
import { ChatService } from '../services/chatService'
import { ChatRequest, ChatResponse, MessageRole } from '../types'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  sources?: any[]
}

const ChatPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId?: string }>()
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)

  // 初始化会话
  useEffect(() => {
    if (sessionId) {
      setCurrentSessionId(sessionId)
      // 可以在这里加载历史消息
      loadChatHistory(sessionId)
    } else {
      // 创建新会话
      createNewSession()
    }
  }, [sessionId])

  // 创建新会话
  const createNewSession = async () => {
    try {
      const session = await ChatService.createSession({
        title: '新对话'
      })
      setCurrentSessionId(session.id)
    } catch (error) {
      console.error('创建会话失败:', error)
      // 如果创建会话失败，我们仍然可以使用临时会话
      setCurrentSessionId(`temp_${Date.now()}`)
    }
  }

  // 加载聊天历史
  const loadChatHistory = async (sessionId: string) => {
    try {
      const history = await ChatService.getChatHistory(sessionId)
      const formattedMessages: Message[] = history.messages.map(msg => ({
        id: msg.id,
        role: msg.role === MessageRole.USER ? 'user' : 'assistant',
        content: msg.content,
        timestamp: msg.created_at || new Date().toISOString(),
        sources: msg.sources ? [msg.sources] : []
      }))
      setMessages(formattedMessages)
    } catch (error) {
      console.error('加载聊天历史失败:', error)
    }
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: newMessage,
      timestamp: new Date().toISOString(),
    }

    setMessages(prev => [...prev, userMessage])
    setNewMessage('')
    setIsLoading(true)

    try {
      // 构建请求数据
      const request: ChatRequest = {
        message: newMessage,
        stream: false, // 使用普通模式，不使用流式
        ...(currentSessionId && { session_id: currentSessionId })
      }

      console.log('🚀 发送聊天请求:', request)

      // 调用真实的API
      const response: ChatResponse = await ChatService.sendMessage(request)

      console.log('✅ 收到聊天回复:', response)

      // 添加AI回复
      const aiMessage: Message = {
        id: response.message_id || (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.message, // 使用 message 属性而不是 content
        timestamp: new Date().toISOString(),
        sources: response.sources || []
      }

      setMessages(prev => [...prev, aiMessage])
      
      // 更新会话ID（如果服务器返回了新的会话ID）
      if (response.session_id && !currentSessionId) {
        setCurrentSessionId(response.session_id)
      }

    } catch (error) {
      console.error('发送消息失败:', error)
      
      // 显示错误消息
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '抱歉，发送消息时出现错误。请检查网络连接并重试。',
        timestamp: new Date().toISOString(),
      }
      
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex flex-col bg-white rounded-lg shadow-sm border border-gray-200 mx-4 sm:mx-6 lg:mx-8">
      {/* 聊天头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <Bot className="h-8 w-8 text-primary-600" />
          <div>
            <h1 className="text-lg font-semibold text-gray-900">AI助手</h1>
            <p className="text-sm text-gray-500">
              {sessionId ? `会话 ${sessionId}` : '新对话'}
            </p>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <Bot className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">开始新对话</p>
            <p className="text-sm">输入您的问题，我来帮助您找到答案</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`flex max-w-xs lg:max-w-md xl:max-w-lg ${
                  message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                } space-x-3`}
              >
                <div className="flex-shrink-0">
                  {message.role === 'user' ? (
                    <User className="h-8 w-8 text-blue-600 bg-blue-100 rounded-full p-1.5" />
                  ) : (
                    <Bot className="h-8 w-8 text-green-600 bg-green-100 rounded-full p-1.5" />
                  )}
                </div>
                <div
                  className={`p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  {message.sources && message.sources.length > 0 && (
                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        参考文档: {message.sources.length} 个
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
        
        {/* 加载指示器 */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex space-x-3">
              <Bot className="h-8 w-8 text-green-600 bg-green-100 rounded-full p-1.5" />
              <div className="bg-gray-100 text-gray-900 p-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-4">
          <textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的问题..."
            className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            rows={3}
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !newMessage.trim()}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <Send className="h-5 w-5" />
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          按 Enter 发送消息，Shift + Enter 换行
        </p>
      </div>
    </div>
  )
}

export default ChatPage 