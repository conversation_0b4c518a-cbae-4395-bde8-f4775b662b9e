import React, { useState } from 'react';
import { Package, Download, Zap, Bar<PERSON>hart } from 'lucide-react';
import { getBundleStats, calculateOptimization, formatSize, getOptimizationSuggestions } from '../utils/bundleAnalyzer';
import LoadingSpinner from './common/LoadingSpinner';

const CodeSplittingDemo: React.FC = () => {
  const [stats] = useState(getBundleStats());
  const [loadingComponents, setLoadingComponents] = useState<string[]>([]);
  const [loadedComponents, setLoadedComponents] = useState<string[]>(['main', 'vendor', 'router']);

  const optimization = calculateOptimization(stats);
  const suggestions = getOptimizationSuggestions(stats);

  // 模拟组件加载
  const simulateComponentLoad = (componentName: string) => {
    if (loadedComponents.includes(componentName) || loadingComponents.includes(componentName)) {
      return;
    }

    setLoadingComponents(prev => [...prev, componentName]);

    setTimeout(() => {
      setLoadingComponents(prev => prev.filter(name => name !== componentName));
      setLoadedComponents(prev => [...prev, componentName]);
    }, 1000 + Math.random() * 2000);
  };

  const getChunkStatus = (chunkName: string) => {
    if (loadedComponents.includes(chunkName)) return 'loaded';
    if (loadingComponents.includes(chunkName)) return 'loading';
    return 'unloaded';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'loaded': return 'bg-green-100 text-green-800';
      case 'loading': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'loaded': return '已加载';
      case 'loading': return '加载中';
      default: return '未加载';
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">代码分割演示</h1>
        <p className="text-gray-600">
          展示React.lazy和动态导入如何优化应用的加载性能
        </p>
      </div>

      {/* 优化统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总包大小</p>
              <p className="text-2xl font-semibold text-gray-900">{formatSize(stats.totalSize)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Download className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">初始包大小</p>
              <p className="text-2xl font-semibold text-gray-900">{formatSize(stats.initialSize)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Zap className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">优化比例</p>
              <p className="text-2xl font-semibold text-gray-900">{optimization.optimizationRatio}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <BarChart className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">代码块数量</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.chunkCount}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 代码块状态 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">代码块加载状态</h2>
            <p className="text-sm text-gray-600">点击按钮模拟组件懒加载</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.chunks.map((chunk) => {
                const status = getChunkStatus(chunk.name);
                return (
                  <div key={chunk.name} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
                        {getStatusText(status)}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{chunk.name}</p>
                        <p className="text-sm text-gray-500">
                          {formatSize(chunk.size)} • {chunk.type === 'initial' ? '初始包' : '异步包'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {status === 'loading' && (
                        <LoadingSpinner size="sm" text="" />
                      )}
                      {status === 'unloaded' && chunk.type === 'async' && (
                        <button
                          onClick={() => simulateComponentLoad(chunk.name)}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          加载
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 优化建议 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">优化建议</h2>
            <p className="text-sm text-gray-600">基于当前配置的性能优化建议</p>
          </div>
          <div className="p-6">
            {suggestions.length > 0 ? (
              <div className="space-y-3">
                {suggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                    <p className="text-sm text-gray-700">{suggestion}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-green-600 text-4xl mb-2">✅</div>
                <p className="text-gray-600">代码分割配置良好！</p>
              </div>
            )}

            <div className="mt-6 pt-4 border-t border-gray-200">
              <h3 className="font-medium text-gray-900 mb-2">性能指标</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">初始加载减少:</span>
                  <span className="font-medium">{optimization.initialLoadReduction}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">异步代码块:</span>
                  <span className="font-medium">{optimization.asyncChunks} 个</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">初始代码块:</span>
                  <span className="font-medium">{optimization.initialChunks} 个</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 技术说明 */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">技术实现</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-800 mb-2">React.lazy 懒加载</h3>
            <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
              <code>{`const HomePage = React.lazy(() => 
  import('./HomePage')
);`}</code>
            </pre>
          </div>
          <div>
            <h3 className="font-medium text-gray-800 mb-2">Suspense 边界</h3>
            <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
              <code>{`<Suspense fallback={<Loading />}>
  <HomePage />
</Suspense>`}</code>
            </pre>
          </div>
          <div>
            <h3 className="font-medium text-gray-800 mb-2">Vite 代码分割</h3>
            <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
              <code>{`manualChunks: {
  vendor: ['react', 'react-dom'],
  pages: ['./HomePage.tsx']
}`}</code>
            </pre>
          </div>
          <div>
            <h3 className="font-medium text-gray-800 mb-2">预加载策略</h3>
            <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
              <code>{`preloadOnIdle('components', 2000);
preloadOnHover(element, 'chat');`}</code>
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeSplittingDemo;