import React, { useState, useCallback, useEffect } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, FileText, Download, Trash2, Eye } from 'lucide-react'
import { DocumentService } from '../services/documentService'

interface Document {
  id: string
  name: string
  size: number
  type: string
  uploadDate: string
  status: 'uploading' | 'processed' | 'error'
  progress?: number
}

const DocumentsPage: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // 加载文档列表
  const loadDocuments = async () => {
    try {
      setIsLoading(true)
      const response = await DocumentService.getDocuments()
      const formattedDocs: Document[] = response.documents.map(doc => ({
        id: doc.id,
        name: doc.filename,
        size: doc.file_size || doc.file_size_bytes || 0,
        type: doc.file_type,
        uploadDate: doc.upload_time,
        status: doc.processing_status === 'completed' ? 'processed' :
                doc.processing_status === 'failed' ? 'error' : 'uploading'
      }))
      setDocuments(formattedDocs)
    } catch (error) {
      console.error('加载文档列表失败:', error)
      // 显示错误提示
      alert('加载文档列表失败，请检查网络连接')
    } finally {
      setIsLoading(false)
    }
  }

  // 组件挂载时加载文档列表
  useEffect(() => {
    loadDocuments()
  }, [])

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsUploading(true)

    for (const file of acceptedFiles) {
      // 验证文件类型和大小
      if (!DocumentService.isSupportedFileType(file)) {
        alert(`不支持的文件类型: ${file.name}`)
        continue
      }

      if (!DocumentService.validateFileSize(file)) {
        alert(`文件过大: ${file.name} (最大10MB)`)
        continue
      }

      const newDoc: Document = {
        id: Date.now().toString() + Math.random().toString(36),
        name: file.name,
        size: file.size,
        type: file.type,
        uploadDate: new Date().toISOString(),
        status: 'uploading',
        progress: 0
      }

      setDocuments(prev => [newDoc, ...prev])

      try {
        // 真实的文档上传
        const response = await DocumentService.uploadDocument(
          file,
          (progress) => {
            // 更新上传进度
            setDocuments(prev =>
              prev.map(doc =>
                doc.id === newDoc.id
                  ? { ...doc, progress }
                  : doc
              )
            )
          }
        )

        // 上传成功，更新文档状态
        setDocuments(prev =>
          prev.map(doc =>
            doc.id === newDoc.id
              ? {
                  ...doc,
                  id: response.document_id,
                  status: 'processed' as const,
                  progress: 100
                }
              : doc
          )
        )

        console.log('文档上传成功:', response)

      } catch (error) {
        console.error('文档上传失败:', error)

        // 上传失败，更新状态
        setDocuments(prev =>
          prev.map(doc =>
            doc.id === newDoc.id
              ? { ...doc, status: 'error' as const }
              : doc
          )
        )
      }
    }

    setIsUploading(false)
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
    },
    multiple: true,
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const handleDelete = async (id: string) => {
    try {
      await DocumentService.deleteDocument(id)
      setDocuments(prev => prev.filter(doc => doc.id !== id))
      console.log('文档删除成功')
    } catch (error) {
      console.error('文档删除失败:', error)
      alert('删除文档失败，请重试')
    }
  }

  const getStatusBadge = (status: Document['status'], progress?: number) => {
    switch (status) {
      case 'uploading':
        return (
          <div className="flex items-center space-x-2">
            <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
              上传中
            </span>
            {progress !== undefined && (
              <span className="text-xs text-gray-500">{progress}%</span>
            )}
          </div>
        )
      case 'processed':
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">已处理</span>
      case 'error':
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">错误</span>
      default:
        return null
    }
  }

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return '📄'
    if (type.includes('word') || type.includes('document')) return '📝'
    if (type.includes('text') || type.includes('markdown')) return '📋'
    return '📄'
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">文档管理</h1>
          <p className="mt-2 text-sm text-gray-700">
            上传和管理您的知识文档，支持 TXT、MD、PDF、DOC、DOCX 格式
          </p>
        </div>
      </div>

      {/* 上传区域 */}
      <div className="mt-8">
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors duration-200 ${
            isDragActive
              ? 'border-primary-400 bg-primary-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">
            {isUploading ? (
              <span className="text-primary-600">正在上传文件...</span>
            ) : isDragActive ? (
              '释放文件以上传'
            ) : (
              <>
                <span className="font-medium text-primary-600 hover:text-primary-500">
                  点击上传
                </span>
                {' '}或拖拽文件到此处
              </>
            )}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            支持 TXT, MD, PDF, DOC, DOCX 格式，最大 10MB
          </p>
        </div>
      </div>

      {/* 文档列表 */}
      <div className="mt-8">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  文档
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  大小
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  上传时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  状态
                </th>
                <th className="relative px-6 py-3">
                  <span className="sr-only">操作</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {documents.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center text-sm text-gray-500">
                    <FileText className="mx-auto h-8 w-8 text-gray-300 mb-2" />
                    暂无文档，开始上传您的第一个文档
                  </td>
                </tr>
              ) : (
                documents.map((doc) => (
                  <tr key={doc.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-lg mr-3">{getFileIcon(doc.type)}</span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                          <div className="text-sm text-gray-500">{doc.type}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatFileSize(doc.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(doc.uploadDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(doc.status, doc.progress)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          className="text-primary-600 hover:text-primary-900 p-1 rounded"
                          title="查看"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          className="text-gray-600 hover:text-gray-900 p-1 rounded"
                          title="下载"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(doc.id)}
                          className="text-red-600 hover:text-red-900 p-1 rounded"
                          title="删除"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 统计信息 */}
      {documents.length > 0 && (
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FileText className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      总文档数
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {documents.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Upload className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      已处理
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {documents.filter(doc => doc.status === 'processed').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FileText className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      总大小
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatFileSize(documents.reduce((total, doc) => total + doc.size, 0))}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DocumentsPage 