import React from 'react'
import { Link } from 'react-router-dom'
import { MessageCircle, FileText, Upload, Brain } from 'lucide-react'

const HomePage: React.FC = () => {
  const features = [
    {
      name: '智能对话',
      description: '与AI进行自然语言对话，获得智能回答和建议',
      icon: MessageCircle,
      href: '/chat',
      color: 'bg-blue-100 text-blue-600',
    },
    {
      name: '文档管理',
      description: '上传和管理知识文档，构建个人知识库',
      icon: FileText,
      href: '/documents',
      color: 'bg-green-100 text-green-600',
    },
    {
      name: '文档上传',
      description: '支持多种格式文档上传，自动解析和索引',
      icon: Upload,
      href: '/documents',
      color: 'bg-purple-100 text-purple-600',
    },
    {
      name: 'RAG检索',
      description: '基于文档内容的检索增强生成，提供准确答案',
      icon: Brain,
      href: '/chat',
      color: 'bg-orange-100 text-orange-600',
    },
  ]

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* 欢迎区域 */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
          AI Knowledge
        </h1>
        <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
          智能知识助手，让信息获取更简单
        </p>
        <div className="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
          <div className="rounded-md shadow">
            <Link
              to="/chat"
              className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"
            >
              开始对话
            </Link>
          </div>
          <div className="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">
            <Link
              to="/documents"
              className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
            >
              管理文档
            </Link>
          </div>
        </div>
      </div>

      {/* 功能特性 */}
      <div className="mt-16">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              主要功能
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
              探索AI知识助手的强大功能
            </p>
          </div>

          <div className="mt-12">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
              {features.map((feature) => {
                const Icon = feature.icon
                return (
                  <Link
                    key={feature.name}
                    to={feature.href}
                    className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
                  >
                    <div>
                      <span className={`rounded-lg inline-flex p-3 ring-4 ring-white ${feature.color}`}>
                        <Icon className="h-6 w-6" aria-hidden="true" />
                      </span>
                    </div>
                    <div className="mt-8">
                      <h3 className="text-lg font-medium">
                        <span className="absolute inset-0" aria-hidden="true" />
                        {feature.name}
                      </h3>
                      <p className="mt-2 text-sm text-gray-500">
                        {feature.description}
                      </p>
                    </div>
                    <span
                      className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400"
                      aria-hidden="true"
                    >
                      <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20 4h1a1 1 0 00-1-1v1zm-1 12a1 1 0 102 0h-2zM8 3a1 1 0 000 2V3zM3.293 19.293a1 1 0 101.414 1.414l-1.414-1.414zM19 4v12h2V4h-2zm1-1H8v2h12V3zm-.707.293l-16 16 1.414 1.414 16-16-1.414-1.414z" />
                      </svg>
                    </span>
                  </Link>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage 