import React from 'react'
import { Link, useLocation, Outlet } from 'react-router-dom'
import { MessageCircle, FileText, Home, Zap, Monitor, Database } from 'lucide-react'
import { clsx } from 'clsx'

const Layout: React.FC = () => {
  const location = useLocation()

  const navigation = [
    { name: '首页', href: '/', icon: Home },
    { name: '聊天', href: '/chat', icon: MessageCircle },
    { name: '文档', href: '/documents', icon: FileText },
    { name: '性能演示', href: '/performance', icon: Zap },
    { name: '缓存监控', href: '/cache-monitor', icon: Monitor },
    { name: '缓存演示', href: '/cache-demo', icon: Database },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              {/* Logo */}
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-bold text-gray-900">
                  AI Knowledge
                </h1>
              </div>
              
              {/* 导航链接 */}
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                {navigation.map((item) => {
                  const Icon = item.icon
                  const isActive = location.pathname === item.href || 
                    (item.href === '/chat' && location.pathname.startsWith('/chat'))
                  
                  return (
                    <Link
                      key={item.name}
                      to={item.href}
                      className={clsx(
                        'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium',
                        isActive
                          ? 'border-primary-500 text-primary-600'
                          : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                      )}
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      {item.name}
                    </Link>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <Outlet />
      </main>
    </div>
  )
}

export default Layout