import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>freshC<PERSON>,
  <PERSON>,
  <PERSON>rk<PERSON>,
  Smartphone,
  Check
} from 'lucide-react';
import { useTheme, ThemeMode, ThemePreset } from '../hooks/useTheme';
import { ThemeSettings } from '../components/common';

export const ThemeShowcase: React.FC = () => {
  const { theme, preset, config, setTheme, setPreset, toggleTheme, resetToDefault } = useTheme();
  const [showSettings, setShowSettings] = useState(false);

  const themeCards = [
    {
      mode: 'light' as ThemeMode,
      title: '亮色模式',
      description: '清新明亮的日间界面',
      icon: <Sun size={24} />,
      bgClass: 'bg-gradient-to-br from-blue-50 to-indigo-100',
      textClass: 'text-gray-900'
    },
    {
      mode: 'dark' as ThemeMode,
      title: '暗色模式',
      description: '护眼的深色夜间界面',
      icon: <Moon size={24} />,
      bgClass: 'bg-gradient-to-br from-gray-800 to-gray-900',
      textClass: 'text-white'
    },
    {
      mode: 'system' as ThemeMode,
      title: '跟随系统',
      description: '自动适应系统主题设置',
      icon: <Monitor size={24} />,
      bgClass: 'bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900',
      textClass: 'text-gray-900 dark:text-white'
    }
  ];

  const presetCards = [
    {
      preset: 'default' as ThemePreset,
      title: '默认蓝色',
      colors: ['bg-blue-500', 'bg-blue-600', 'bg-blue-700'],
      description: '专业稳重的蓝色主题'
    },
    {
      preset: 'purple' as ThemePreset,
      title: '优雅紫',
      colors: ['bg-purple-500', 'bg-violet-500', 'bg-indigo-500'],
      description: '创意优雅的紫色主题'
    },
    {
      preset: 'green' as ThemePreset,
      title: '自然绿',
      colors: ['bg-green-500', 'bg-emerald-500', 'bg-teal-500'],
      description: '清新自然的绿色主题'
    },
    {
      preset: 'orange' as ThemePreset,
      title: '活力橙',
      colors: ['bg-orange-500', 'bg-amber-500', 'bg-yellow-500'],
      description: '活力四射的橙色主题'
    }
  ];

  const features = [
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: '平滑动画',
      description: '主题切换时的流畅过渡效果',
      enabled: config.animated
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: '定时切换',
      description: '根据时间自动切换深色/浅色模式',
      enabled: config.autoSwitch
    },
    {
      icon: <Monitor className="h-6 w-6" />,
      title: '系统跟随',
      description: '自动检测并跟随系统主题设置',
      enabled: theme === 'system'
    },
    {
      icon: <Smartphone className="h-6 w-6" />,
      title: '响应式设计',
      description: '完美适配各种屏幕尺寸',
      enabled: true
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
              <Palette className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              主题系统展示
            </h1>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            体验我们全新的主题切换系统，支持多种模式、颜色预设和智能功能
          </p>
        </div>

        {/* 当前状态卡片 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
              当前主题状态
            </h2>
            <div className="flex items-center space-x-3">
              <button
                onClick={toggleTheme}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                快速切换
              </button>
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <Settings size={20} />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">当前模式</div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                {theme === 'light' ? '亮色模式' : theme === 'dark' ? '暗色模式' : '跟随系统'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">颜色主题</div>
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                {preset === 'default' ? '默认蓝色' : 
                 preset === 'purple' ? '优雅紫' :
                 preset === 'green' ? '自然绿' : '活力橙'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">切换动画</div>
              <div className={`text-lg font-semibold ${config.animated ? 'text-green-600' : 'text-gray-400'}`}>
                {config.animated ? '开启' : '关闭'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">自动切换</div>
              <div className={`text-lg font-semibold ${config.autoSwitch ? 'text-green-600' : 'text-gray-400'}`}>
                {config.autoSwitch ? '开启' : '关闭'}
              </div>
            </div>
          </div>
        </div>

        {/* 主题模式选择 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
            主题模式
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {themeCards.map((card) => (
              <button
                key={card.mode}
                onClick={() => setTheme(card.mode)}
                className={`relative p-6 rounded-xl border-2 transition-all hover:scale-105 ${
                  theme === card.mode
                    ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className={`absolute inset-0 rounded-xl ${card.bgClass} opacity-10`} />
                <div className="relative">
                  <div className="flex items-center justify-center mb-4">
                    <div className={`p-3 rounded-lg ${
                      theme === card.mode 
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      {card.icon}
                    </div>
                  </div>
                  <h3 className={`text-lg font-semibold mb-2 ${
                    theme === card.mode 
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-900 dark:text-white'
                  }`}>
                    {card.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {card.description}
                  </p>
                  {theme === card.mode && (
                    <div className="absolute top-3 right-3">
                      <Check size={20} className="text-blue-600 dark:text-blue-400" />
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 颜色预设选择 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
            颜色主题
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {presetCards.map((card) => (
              <button
                key={card.preset}
                onClick={() => setPreset(card.preset)}
                className={`p-4 rounded-xl border-2 transition-all hover:scale-105 ${
                  preset === card.preset
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className="flex space-x-1">
                    {card.colors.map((color, index) => (
                      <div
                        key={index}
                        className={`w-6 h-6 rounded-full ${color} ring-2 ring-white dark:ring-gray-800`}
                      />
                    ))}
                  </div>
                  {preset === card.preset && (
                    <Check size={16} className="text-blue-600 dark:text-blue-400" />
                  )}
                </div>
                <h3 className={`text-sm font-semibold mb-1 ${
                  preset === card.preset 
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {card.title}
                </h3>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {card.description}
                </p>
              </button>
            ))}
          </div>
        </div>

        {/* 功能特性 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
            智能功能
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className={`p-2 rounded-lg ${
                  feature.enabled 
                    ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-400'
                }`}>
                  {feature.icon}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {feature.description}
                  </p>
                  <div className={`text-xs mt-1 ${
                    feature.enabled ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {feature.enabled ? '✓ 已启用' : '○ 未启用'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => setShowSettings(true)}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            <Settings size={20} />
            <span>打开高级设置</span>
          </button>
          <button
            onClick={resetToDefault}
            className="flex items-center space-x-2 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg font-medium transition-colors"
          >
            <RefreshCw size={20} />
            <span>重置为默认</span>
          </button>
        </div>
      </div>

      {/* 主题设置弹窗 */}
      <ThemeSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </div>
  );
};

export default ThemeShowcase; 