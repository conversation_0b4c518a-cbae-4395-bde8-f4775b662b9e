import React, { useState, useRef, useEffect } from 'react';
import {
  Setting<PERSON>,
  Play,
  RefreshCw,
  Zap,
  Clock,
  Bar<PERSON><PERSON>,
  MessageSquare,
  Cpu,
  HardDrive,
  Timer,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { MessageList, VirtualizedMessageList, type VirtualListRef } from './chat';
import type { ChatMessageResponse } from '../types';
import { MessageRole } from '../types';

interface PerformanceMetrics {
  renderTime: number;
  renderCount: number;
  memoryUsage: number;
  scrollPerformance: number;
}

export const VirtualizationShowcase: React.FC = () => {
  const [messageCount, setMessageCount] = useState(100);
  const [useVirtualization, setUseVirtualization] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [messages, setMessages] = useState<ChatMessageResponse[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [scrollTests, setScrollTests] = useState({ running: false, score: 0 });
  
  const virtualListRef = useRef<VirtualListRef>(null);
  const performanceObserver = useRef<PerformanceObserver | null>(null);

  // 生成模拟消息数据
  const generateMessages = (count: number): ChatMessageResponse[] => {
    const messages: ChatMessageResponse[] = [];
    const sampleContents = [
      "这是一条用户消息，用于测试虚拟滚动性能。",
      "AI助手的回复通常会更长一些，包含更多的信息和解释。这样可以更好地测试不同高度的消息项渲染性能。我们会模拟各种长度的消息来测试虚拟化列表的适应性。",
      "短消息",
      "这是一条中等长度的消息，包含一些详细信息，但不会过于冗长。主要用于测试混合高度的消息列表渲染性能。",
      "非常长的消息内容，用于模拟真实场景中可能出现的详细解答或说明。这类消息可能包含多个段落，详细的解释，代码示例，或者其他复杂内容。通过这样的测试，我们可以验证虚拟化组件在处理不同高度内容时的性能表现。虚拟滚动的优势在于只渲染可见区域的内容，大大减少了DOM节点数量，提升了滚动性能。",
      "🎯 测试表情符号和特殊字符：✨🚀📊💡🔥",
      "包含代码的消息：```javascript\nconst example = 'Hello World';\nconsole.log(example);\n```",
    ];

    for (let i = 0; i < count; i++) {
      const isUser = i % 3 === 0;
      const content = sampleContents[i % sampleContents.length];
      
      messages.push({
        id: `msg-${i}`,
        content: `[${i + 1}] ${content}`,
        role: isUser ? MessageRole.USER : MessageRole.ASSISTANT,
        created_at: new Date(Date.now() - (count - i) * 60000).toISOString(),
        session_id: 'demo-session',
        sources: []
      });
    }
    
    return messages;
  };

  // 性能监测
  const measurePerformance = async (callback: () => void) => {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    // 开始性能监测
    let renderCount = 0;
    performanceObserver.current = new PerformanceObserver((list) => {
      renderCount += list.getEntries().length;
    });
    performanceObserver.current.observe({ entryTypes: ['measure'] });

    // 执行回调
    callback();

    // 等待渲染完成
    await new Promise(resolve => setTimeout(resolve, 100));

    const endTime = performance.now();
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    const metrics: PerformanceMetrics = {
      renderTime: endTime - startTime,
      renderCount,
      memoryUsage: Math.max(0, endMemory - startMemory),
      scrollPerformance: 0
    };

    setMetrics(metrics);
    performanceObserver.current?.disconnect();
  };

  // 生成测试消息
  const handleGenerateMessages = async () => {
    setIsGenerating(true);
    
    await measurePerformance(() => {
      const newMessages = generateMessages(messageCount);
      setMessages(newMessages);
    });
    
    setIsGenerating(false);
  };

  // 滚动性能测试
  const runScrollTest = async () => {
    if (!virtualListRef.current || messages.length === 0) return;
    
    setScrollTests({ running: true, score: 0 });
    
    const iterations = 10;
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      const randomIndex = Math.floor(Math.random() * messages.length);
      virtualListRef.current.scrollToItem(randomIndex);
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const score = Math.max(0, 100 - Math.floor(totalTime / 10));
    
    setScrollTests({ running: false, score });
  };

  // 组件清理
  useEffect(() => {
    return () => {
      performanceObserver.current?.disconnect();
    };
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const formatTime = (ms: number) => {
    return `${ms.toFixed(2)} ms`;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            🚀 虚拟化性能演示
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            对比传统消息列表与虚拟化消息列表的性能差异，展示虚拟滚动在大量数据场景下的优势
          </p>
        </div>

        {/* 控制面板 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* 配置区 */}
            <div className="flex-1">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                测试配置
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    消息数量: {messageCount}
                  </label>
                  <input
                    type="range"
                    min="50"
                    max="5000"
                    step="50"
                    value={messageCount}
                    onChange={(e) => setMessageCount(Number(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>50</span>
                    <span>5000</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    渲染模式
                  </label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="renderMode"
                        checked={useVirtualization}
                        onChange={() => setUseVirtualization(true)}
                        className="mr-2"
                      />
                      <Zap className="h-4 w-4 mr-1 text-green-500" />
                      虚拟化列表
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="renderMode"
                        checked={!useVirtualization}
                        onChange={() => setUseVirtualization(false)}
                        className="mr-2"
                      />
                      <MessageSquare className="h-4 w-4 mr-1 text-orange-500" />
                      传统列表
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* 操作区 */}
            <div className="lg:w-64">
              <h3 className="text-lg font-semibold mb-4">操作控制</h3>
              <div className="space-y-3">
                <button
                  onClick={handleGenerateMessages}
                  disabled={isGenerating}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {isGenerating ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  生成测试数据
                </button>

                <button
                  onClick={runScrollTest}
                  disabled={scrollTests.running || messages.length === 0 || !useVirtualization}
                  className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {scrollTests.running ? (
                    <Timer className="h-4 w-4 animate-pulse" />
                  ) : (
                    <BarChart className="h-4 w-4" />
                  )}
                  滚动性能测试
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 性能指标 */}
        {metrics && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <BarChart className="h-5 w-5" />
              性能指标
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">渲染时间</span>
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {formatTime(metrics.renderTime)}
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Cpu className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">渲染次数</span>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {metrics.renderCount}
                </div>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <HardDrive className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">内存使用</span>
                </div>
                <div className="text-2xl font-bold text-purple-600">
                  {formatBytes(metrics.memoryUsage)}
                </div>
              </div>

              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium">滚动评分</span>
                </div>
                <div className="text-2xl font-bold text-orange-600 flex items-center gap-2">
                  {scrollTests.score > 0 ? scrollTests.score : '--'}
                  {scrollTests.score > 80 && <CheckCircle className="h-5 w-5 text-green-500" />}
                  {scrollTests.score <= 60 && scrollTests.score > 0 && <AlertCircle className="h-5 w-5 text-red-500" />}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 消息列表演示 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                消息列表演示 ({messages.length} 条消息)
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">
                  当前模式:
                </span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  useVirtualization 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                    : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300'
                }`}>
                  {useVirtualization ? '虚拟化列表' : '传统列表'}
                </span>
              </div>
            </div>
          </div>

          <div className="h-96">
            {messages.length > 0 ? (
              useVirtualization ? (
                <VirtualizedMessageList
                  ref={virtualListRef}
                  messages={messages}
                  isLoading={false}
                  autoScroll={false}
                  showTypingIndicator={false}
                  emptyStateText="暂无消息"
                  emptyStateSubtext="点击生成测试数据开始演示"
                />
              ) : (
                <MessageList
                  messages={messages}
                  isLoading={false}
                  autoScroll={false}
                  showTypingIndicator={false}
                  emptyStateText="暂无消息"
                  emptyStateSubtext="点击生成测试数据开始演示"
                />
              )
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <MessageSquare className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">准备开始测试</h3>
                  <p className="text-sm">点击"生成测试数据"按钮开始性能演示</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 说明文档 */}
        <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100">
            💡 虚拟化技术说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800 dark:text-blue-200">
            <div>
              <h4 className="font-medium mb-2">虚拟化优势:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>只渲染可见区域的元素</li>
                <li>大幅减少DOM节点数量</li>
                <li>显著提升滚动性能</li>
                <li>降低内存使用</li>
                <li>支持无限滚动</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">适用场景:</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>长聊天记录（1000+ 消息）</li>
                <li>数据量大的列表展示</li>
                <li>移动端性能优化</li>
                <li>实时数据流展示</li>
                <li>内存敏感的应用</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 