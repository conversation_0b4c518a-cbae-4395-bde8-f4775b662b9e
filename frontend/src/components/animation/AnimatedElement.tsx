import React, { useEffect, useState, useRef, useCallback } from 'react';
import { AnimatedElementProps } from '../../types/animation';
import { useAnimation } from './AnimationProvider';

export const AnimatedElement: React.FC<AnimatedElementProps> = ({
  children,
  animation,
  trigger = 'visible',
  // direction = 'up',
  duration: baseDuration = 300,
  delay: baseDelay = 0,
  className = '',
  onAnimationComplete,
}) => {
  const { getDuration, shouldAnimate } = useAnimation();
  const [isTriggered, setIsTriggered] = useState(trigger === 'immediate');
  const [isMounted, setIsMounted] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const duration = getDuration(baseDuration);
  const delay = getDuration(baseDelay);

  // 处理可见性触发
  useEffect(() => {
    if (trigger !== 'visible' || !shouldAnimate()) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isTriggered) {
            setIsTriggered(true);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    observerRef.current = observer;

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [trigger, isTriggered, shouldAnimate]);

  // 处理动画完成回调
  useEffect(() => {
    if (isTriggered && onAnimationComplete) {
      const timer = setTimeout(() => {
        onAnimationComplete();
      }, duration + delay);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [isTriggered, duration, delay, onAnimationComplete]);

  // 组件挂载处理
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 事件处理函数
  const handleMouseEnter = useCallback(() => {
    if (trigger === 'hover') {
      setIsTriggered(true);
    }
  }, [trigger]);

  const handleMouseLeave = useCallback(() => {
    if (trigger === 'hover') {
      setIsTriggered(false);
    }
  }, [trigger]);

  const handleFocus = useCallback(() => {
    if (trigger === 'focus') {
      setIsTriggered(true);
    }
  }, [trigger]);

  const handleBlur = useCallback(() => {
    if (trigger === 'focus') {
      setIsTriggered(false);
    }
  }, [trigger]);

  const handleClick = useCallback(() => {
    if (trigger === 'click') {
      setIsTriggered(!isTriggered);
    }
  }, [trigger, isTriggered]);

  const getAnimationClasses = () => {
    if (!shouldAnimate() || !isMounted) {
      return 'opacity-100 transform-none';
    }

    const baseClasses = 'transition-all ease-out';
    
    let transformClasses = '';
    let opacityClasses = '';
    let animationClasses = '';

    switch (animation) {
      case 'fade':
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-up':
        transformClasses = isTriggered ? 'translate-y-0' : 'translate-y-8';
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-down':
        transformClasses = isTriggered ? 'translate-y-0' : '-translate-y-8';
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-left':
        transformClasses = isTriggered ? 'translate-x-0' : 'translate-x-8';
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-right':
        transformClasses = isTriggered ? 'translate-x-0' : '-translate-x-8';
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'scale':
        transformClasses = isTriggered ? 'scale-100' : 'scale-95';
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'rotate':
        transformClasses = isTriggered ? 'rotate-0' : 'rotate-12';
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
        break;

      case 'bounce':
        if (isTriggered) {
          animationClasses = 'animate-bounce-in';
          transformClasses = 'scale-100';
          opacityClasses = 'opacity-100';
        } else {
          transformClasses = 'scale-95';
          opacityClasses = 'opacity-0';
        }
        break;

      case 'elastic':
        if (isTriggered) {
          animationClasses = 'animate-elastic-in';
          transformClasses = 'scale-100';
          opacityClasses = 'opacity-100';
        } else {
          transformClasses = 'scale-95';
          opacityClasses = 'opacity-0';
        }
        break;

      case 'spring':
        if (isTriggered) {
          animationClasses = 'animate-spring-in';
          transformClasses = 'scale-100';
          opacityClasses = 'opacity-100';
        } else {
          transformClasses = 'scale-90';
          opacityClasses = 'opacity-0';
        }
        break;

      default:
        opacityClasses = isTriggered ? 'opacity-100' : 'opacity-0';
    }

    return `${baseClasses} ${transformClasses} ${opacityClasses} ${animationClasses}`.trim();
  };

  const eventHandlers = {
    ...(trigger === 'hover' && { onMouseEnter: handleMouseEnter, onMouseLeave: handleMouseLeave }),
    ...(trigger === 'focus' && { onFocus: handleFocus, onBlur: handleBlur }),
    ...(trigger === 'click' && { onClick: handleClick }),
  };

  return (
    <div
      ref={elementRef}
      className={`${getAnimationClasses()} ${className}`}
      style={{
        transitionDuration: duration > 0 ? `${duration}ms` : '0ms',
        transitionDelay: delay > 0 ? `${delay}ms` : '0ms',
      }}
      {...eventHandlers}
    >
      {children}
    </div>
  );
}; 