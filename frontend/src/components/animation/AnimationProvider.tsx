import React, { createContext, useContext, useState, useEffect } from 'react';
import { AnimationContextValue, AnimationPreferences } from '../../types/animation';

const AnimationContext = createContext<AnimationContextValue | undefined>(undefined);

interface AnimationProviderProps {
  children: React.ReactNode;
}

const DEFAULT_PREFERENCES: AnimationPreferences = {
  enableAnimations: true,
  reduceMotion: false,
  animationSpeed: 'normal',
};

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ children }) => {
  const [preferences, setPreferences] = useState<AnimationPreferences>(DEFAULT_PREFERENCES);

  // 检测系统是否偏好减少动画
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setPreferences(prev => ({
        ...prev,
        reduceMotion: e.matches,
        enableAnimations: !e.matches,
      }));
    };

    // 初始检查
    if (mediaQuery.matches) {
      setPreferences(prev => ({
        ...prev,
        reduceMotion: true,
        enableAnimations: false,
      }));
    }

    // 监听变化
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  // 从localStorage加载偏好设置
  useEffect(() => {
    const stored = localStorage.getItem('animation-preferences');
    if (stored) {
      try {
        const parsedPreferences = JSON.parse(stored);
        setPreferences(prev => ({ ...prev, ...parsedPreferences }));
      } catch (error) {
        console.warn('Failed to parse animation preferences from localStorage:', error);
      }
    }
  }, []);

  // 保存偏好设置到localStorage
  const updatePreferences = (newPreferences: Partial<AnimationPreferences>) => {
    setPreferences(prev => {
      const updated = { ...prev, ...newPreferences };
      localStorage.setItem('animation-preferences', JSON.stringify(updated));
      return updated;
    });
  };

  // 根据偏好设置获取动画持续时间
  const getDuration = (baseDuration: number): number => {
    if (!preferences.enableAnimations || preferences.reduceMotion) {
      return 0;
    }

    const speedMultiplier = {
      slow: 1.5,
      normal: 1,
      fast: 0.7,
    }[preferences.animationSpeed];

    return baseDuration * speedMultiplier;
  };

  // 检查是否应该启用动画
  const shouldAnimate = (): boolean => {
    return preferences.enableAnimations && !preferences.reduceMotion;
  };

  const contextValue: AnimationContextValue = {
    preferences,
    updatePreferences,
    getDuration,
    shouldAnimate,
  };

  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
    </AnimationContext.Provider>
  );
};

export const useAnimation = (): AnimationContextValue => {
  const context = useContext(AnimationContext);
  if (!context) {
    throw new Error('useAnimation must be used within an AnimationProvider');
  }
  return context;
}; 