import React, { useState } from 'react';
import { PageTransition } from './PageTransition';
import { AnimatedElement } from './AnimatedElement';
import { FadeTransition } from './FadeTransition';
import { SlideTransition } from './SlideTransition';
import { useAnimation } from './AnimationProvider';
import { useButtonInteraction, useCardInteraction } from '../../hooks/useMicroInteraction';
import { AnimationType, AnimationDirection } from '../../types/animation';

export const AnimationShowcase: React.FC = () => {
  const { preferences, updatePreferences } = useAnimation();
  const [selectedAnimation, setSelectedAnimation] = useState<AnimationType>('fade');
  const [selectedDirection, setSelectedDirection] = useState<AnimationDirection>('up');
  const [showDemo, setShowDemo] = useState(true);
  const [triggerCount, setTriggerCount] = useState(0);

  const buttonInteraction = useButtonInteraction();
  const cardInteraction = useCardInteraction();

  const animationTypes: AnimationType[] = [
    'fade', 'slide-up', 'slide-down', 'slide-left', 'slide-right',
    'scale', 'rotate', 'bounce', 'elastic', 'spring'
  ];

  const directions: AnimationDirection[] = ['up', 'down', 'left', 'right'];

  const triggerDemo = () => {
    setShowDemo(false);
    setTimeout(() => {
      setShowDemo(true);
      setTriggerCount(prev => prev + 1);
    }, 100);
  };

  return (
    <PageTransition type="fade" duration={400}>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
        <div className="max-w-6xl mx-auto">
          {/* 标题 */}
          <AnimatedElement
            animation="slide-up"
            trigger="visible"
            className="text-center mb-12"
          >
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              动画效果展示
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              体验流畅的界面动画和过渡效果
            </p>
          </AnimatedElement>

          {/* 动画偏好设置 */}
          <AnimatedElement
            animation="slide-up"
            trigger="visible"
            delay={200}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 mb-8 shadow-lg"
          >
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              动画偏好设置
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={preferences.enableAnimations}
                  onChange={(e) => updatePreferences({ enableAnimations: e.target.checked })}
                  className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <span className="text-gray-700 dark:text-gray-300">启用动画</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={preferences.reduceMotion}
                  onChange={(e) => updatePreferences({ reduceMotion: e.target.checked })}
                  className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <span className="text-gray-700 dark:text-gray-300">减少动画</span>
              </label>

              <div className="flex items-center space-x-2">
                <span className="text-gray-700 dark:text-gray-300">速度:</span>
                <select
                  value={preferences.animationSpeed}
                  onChange={(e) => updatePreferences({ 
                    animationSpeed: e.target.value as 'slow' | 'normal' | 'fast' 
                  })}
                  className="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="slow">慢速</option>
                  <option value="normal">正常</option>
                  <option value="fast">快速</option>
                </select>
              </div>
            </div>
          </AnimatedElement>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 动画类型演示 */}
            <AnimatedElement
              animation="slide-left"
              trigger="visible"
              delay={300}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
            >
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                动画类型演示
              </h2>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  选择动画类型:
                </label>
                <select
                  value={selectedAnimation}
                  onChange={(e) => setSelectedAnimation(e.target.value as AnimationType)}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {animationTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  选择方向:
                </label>
                <select
                  value={selectedDirection}
                  onChange={(e) => setSelectedDirection(e.target.value as AnimationDirection)}
                  className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {directions.map(dir => (
                    <option key={dir} value={dir}>{dir}</option>
                  ))}
                </select>
              </div>

              <button
                {...buttonInteraction.handlers}
                onClick={triggerDemo}
                className={buttonInteraction.getAnimationClasses(
                  'w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg mb-4'
                )}
              >
                触发动画演示
              </button>

              <div className="h-32 flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                {showDemo && (
                  <AnimatedElement
                    key={triggerCount}
                    animation={selectedAnimation}
                    direction={selectedDirection}
                    trigger="immediate"
                    className="bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 px-4 py-2 rounded-lg"
                  >
                    {selectedAnimation} - {selectedDirection}
                  </AnimatedElement>
                )}
              </div>
            </AnimatedElement>

            {/* 过渡动画演示 */}
            <AnimatedElement
              animation="slide-right"
              trigger="visible"
              delay={400}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
            >
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                过渡动画演示
              </h2>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    淡入淡出过渡
                  </h3>
                  <div className="h-20 border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <FadeTransition show={showDemo} duration={500}>
                      <div className="h-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium">
                        淡入淡出内容
                      </div>
                    </FadeTransition>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    滑动过渡
                  </h3>
                  <div className="h-20 border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <SlideTransition 
                      show={showDemo} 
                      direction="left" 
                      duration={400}
                    >
                      <div className="h-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center text-white font-medium">
                        滑动内容
                      </div>
                    </SlideTransition>
                  </div>
                </div>
              </div>
            </AnimatedElement>
          </div>

          {/* 微交互演示 */}
          <AnimatedElement
            animation="slide-up"
            trigger="visible"
            delay={500}
            className="mt-8 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg"
          >
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              微交互演示
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 按钮交互 */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  按钮交互
                </h3>
                <div className="space-y-2">
                  <button
                    {...buttonInteraction.handlers}
                    className={buttonInteraction.getAnimationClasses(
                      'w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg'
                    )}
                  >
                    主要按钮
                  </button>
                  <button
                    {...buttonInteraction.handlers}
                    className={buttonInteraction.getAnimationClasses(
                      'w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-lg'
                    )}
                  >
                    次要按钮
                  </button>
                </div>
              </div>

              {/* 卡片交互 */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  卡片交互
                </h3>
                <div
                  {...cardInteraction.handlers}
                  className={cardInteraction.getAnimationClasses(
                    'p-4 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-lg cursor-pointer'
                  )}
                >
                  <div className="text-sm font-medium text-purple-900 dark:text-purple-100">
                    悬停卡片
                  </div>
                  <div className="text-xs text-purple-600 dark:text-purple-300 mt-1">
                    鼠标悬停查看效果
                  </div>
                </div>
              </div>

              {/* 触发器演示 */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  触发器动画
                </h3>
                <div className="space-y-2">
                  <AnimatedElement
                    animation="scale"
                    trigger="hover"
                    className="p-3 bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100 rounded cursor-pointer text-center text-sm"
                  >
                    悬停触发
                  </AnimatedElement>
                  <AnimatedElement
                    animation="bounce"
                    trigger="click"
                    className="p-3 bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-100 rounded cursor-pointer text-center text-sm"
                  >
                    点击触发
                  </AnimatedElement>
                </div>
              </div>
            </div>
          </AnimatedElement>
        </div>
      </div>
    </PageTransition>
  );
}; 