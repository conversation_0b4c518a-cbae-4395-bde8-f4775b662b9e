import React, { useState, useEffect } from 'react';
import { useAnimation } from './AnimationProvider';

interface FadeTransitionProps {
  children: React.ReactNode;
  show: boolean;
  duration?: number;
  className?: string;
  unmountOnExit?: boolean;
  onEntered?: () => void;
  onExited?: () => void;
}

export const FadeTransition: React.FC<FadeTransitionProps> = ({
  children,
  show,
  duration: baseDuration = 300,
  className = '',
  unmountOnExit = false,
  onEntered,
  onExited,
}) => {
  const { getDuration, shouldAnimate } = useAnimation();
  const [mounted, setMounted] = useState(show);
  const [visible, setVisible] = useState(show);

  const duration = getDuration(baseDuration);

  useEffect(() => {
    if (show) {
      setMounted(true);
      // 小延迟确保元素已渲染
      const timer = setTimeout(() => {
        setVisible(true);
        if (onEntered) {
          setTimeout(onEntered, duration);
        }
      }, 10);
      return () => clearTimeout(timer);
    } else {
      setVisible(false);
      if (unmountOnExit) {
        const timer = setTimeout(() => {
          setMounted(false);
          onExited?.();
        }, duration);
        return () => clearTimeout(timer);
      } else {
        const timer = setTimeout(() => {
          onExited?.();
        }, duration);
        return () => clearTimeout(timer);
      }
    }
  }, [show, duration, unmountOnExit, onEntered, onExited]);

  if (!mounted) {
    return null;
  }

  const getTransitionClasses = () => {
    if (!shouldAnimate()) {
      return show ? 'opacity-100' : 'opacity-0';
    }

    return [
      'transition-opacity ease-in-out',
      visible ? 'opacity-100' : 'opacity-0',
    ].join(' ');
  };

  return (
    <div
      className={`${getTransitionClasses()} ${className}`}
      style={{
        transitionDuration: duration > 0 ? `${duration}ms` : '0ms',
      }}
    >
      {children}
    </div>
  );
}; 