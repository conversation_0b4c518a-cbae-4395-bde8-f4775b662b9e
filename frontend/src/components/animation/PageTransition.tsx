import React, { useEffect, useState } from 'react';
import { PageTransitionProps } from '../../types/animation';
import { useAnimation } from './AnimationProvider';

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  type = 'fade',
  // direction = 'right',
  duration: baseDuration = 300,
  className = '',
}) => {
  const { getDuration, shouldAnimate } = useAnimation();
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  const duration = getDuration(baseDuration);

  useEffect(() => {
    setIsMounted(true);
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 10); // 小延迟确保初始样式被应用

    return () => clearTimeout(timer);
  }, []);

  const getAnimationClasses = () => {
    if (!shouldAnimate()) {
      return 'opacity-100 transform-none';
    }

    const baseClasses = 'transition-all ease-out';
    const durationClass = duration > 0 ? `duration-${Math.min(duration, 1000)}` : 'duration-0';

    let transformClasses = '';
    let opacityClasses = '';

    switch (type) {
      case 'fade':
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-up':
        transformClasses = isVisible ? 'translate-y-0' : 'translate-y-8';
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-down':
        transformClasses = isVisible ? 'translate-y-0' : '-translate-y-8';
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-left':
        transformClasses = isVisible ? 'translate-x-0' : 'translate-x-8';
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
        break;

      case 'slide-right':
        transformClasses = isVisible ? 'translate-x-0' : '-translate-x-8';
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
        break;

      case 'scale':
        transformClasses = isVisible ? 'scale-100' : 'scale-95';
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
        break;

      case 'bounce':
        if (isVisible) {
          transformClasses = 'scale-100 animate-bounce-in';
        } else {
          transformClasses = 'scale-95';
          opacityClasses = 'opacity-0';
        }
        break;

      case 'elastic':
        if (isVisible) {
          transformClasses = 'scale-100 animate-elastic-in';
        } else {
          transformClasses = 'scale-95';
          opacityClasses = 'opacity-0';
        }
        break;

      case 'spring':
        if (isVisible) {
          transformClasses = 'scale-100 animate-spring-in';
        } else {
          transformClasses = 'scale-90';
          opacityClasses = 'opacity-0';
        }
        break;

      default:
        opacityClasses = isVisible ? 'opacity-100' : 'opacity-0';
    }

    return `${baseClasses} ${durationClass} ${transformClasses} ${opacityClasses}`;
  };

  if (!isMounted) {
    return null; // 避免服务端渲染问题
  }

  return (
    <div
      className={`${getAnimationClasses()} ${className}`}
      style={{
        transitionDuration: duration > 0 ? `${duration}ms` : '0ms',
      }}
    >
      {children}
    </div>
  );
}; 