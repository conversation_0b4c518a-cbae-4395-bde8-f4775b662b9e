import React, { useState, useEffect } from 'react';
import { useAnimation } from './AnimationProvider';
import { AnimationDirection } from '../../types/animation';

interface SlideTransitionProps {
  children: React.ReactNode;
  show: boolean;
  direction?: AnimationDirection;
  duration?: number;
  distance?: number;
  className?: string;
  unmountOnExit?: boolean;
  onEntered?: () => void;
  onExited?: () => void;
}

export const SlideTransition: React.FC<SlideTransitionProps> = ({
  children,
  show,
  direction = 'right',
  duration: baseDuration = 300,
  distance = 100,
  className = '',
  unmountOnExit = false,
  onEntered,
  onExited,
}) => {
  const { getDuration, shouldAnimate } = useAnimation();
  const [mounted, setMounted] = useState(show);
  const [visible, setVisible] = useState(show);

  const duration = getDuration(baseDuration);

  useEffect(() => {
    if (show) {
      setMounted(true);
      const timer = setTimeout(() => {
        setVisible(true);
        if (onEntered) {
          setTimeout(onEntered, duration);
        }
      }, 10);
      return () => clearTimeout(timer);
    } else {
      setVisible(false);
      if (unmountOnExit) {
        const timer = setTimeout(() => {
          setMounted(false);
          onExited?.();
        }, duration);
        return () => clearTimeout(timer);
      } else {
        const timer = setTimeout(() => {
          onExited?.();
        }, duration);
        return () => clearTimeout(timer);
      }
    }
  }, [show, duration, unmountOnExit, onEntered, onExited]);

  if (!mounted) {
    return null;
  }

  const getTransformClasses = () => {
    if (!shouldAnimate()) {
      return 'transform-none opacity-100';
    }

    const baseClasses = 'transition-all ease-out';
    let transformClass = '';
    const opacityClass = visible ? 'opacity-100' : 'opacity-0';

    if (!visible) {
      switch (direction) {
        case 'up':
          transformClass = `translate-y-${distance > 50 ? 'full' : '8'}`;
          break;
        case 'down':
          transformClass = `-translate-y-${distance > 50 ? 'full' : '8'}`;
          break;
        case 'left':
          transformClass = `translate-x-${distance > 50 ? 'full' : '8'}`;
          break;
        case 'right':
          transformClass = `-translate-x-${distance > 50 ? 'full' : '8'}`;
          break;
        default:
          transformClass = 'translate-x-8';
      }
    } else {
      transformClass = 'translate-x-0 translate-y-0';
    }

    return `${baseClasses} ${transformClass} ${opacityClass}`;
  };

  return (
    <div
      className={`${getTransformClasses()} ${className}`}
      style={{
        transitionDuration: duration > 0 ? `${duration}ms` : '0ms',
      }}
    >
      {children}
    </div>
  );
}; 