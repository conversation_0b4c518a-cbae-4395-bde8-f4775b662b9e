// Animation Provider
export { AnimationProvider, useAnimation } from './AnimationProvider';

// Transition Components
export { PageTransition } from './PageTransition';
export { FadeTransition } from './FadeTransition';
export { SlideTransition } from './SlideTransition';
export { AnimatedElement } from './AnimatedElement';

// Showcase Component
export { AnimationShowcase } from './AnimationShowcase';

// Types
export type {
  AnimationConfig,
  TransitionConfig,
  AnimationPreferences,
  MicroInteractionConfig,
  AnimationType,
  AnimationDirection,
  AnimationContextValue,
  PageTransitionProps,
  AnimatedElementProps,
} from '../../types/animation'; 