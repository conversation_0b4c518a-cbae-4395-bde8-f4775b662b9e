/**
 * 缓存优化演示组件
 * 展示React Query缓存策略的优化效果
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useCachePerformanceAnalyzer, usePrefetchStrategy, useOfflineCache } from '../../config/cache';

// 演示场景类型
type DemoScenario = 'baseline' | 'optimized' | 'prefetch' | 'offline';

// 演示统计数据
interface DemoStats {
  scenario: DemoScenario;
  loadTime: number;
  cacheHitRate: number;
  networkRequests: number;
  memoryUsage: number;
  userExperience: 'poor' | 'fair' | 'good' | 'excellent';
}

// 场景配置
const scenarioConfigs = {
  baseline: {
    title: '基准场景',
    description: '默认配置，无优化',
    color: 'bg-gray-100 text-gray-800',
    icon: '📊',
  },
  optimized: {
    title: '缓存优化',
    description: '优化的staleTime和cacheTime',
    color: 'bg-blue-100 text-blue-800',
    icon: '🚀',
  },
  prefetch: {
    title: '预取策略',
    description: '智能数据预取',
    color: 'bg-green-100 text-green-800',
    icon: '⚡',
  },
  offline: {
    title: '离线缓存',
    description: '离线数据访问',
    color: 'bg-purple-100 text-purple-800',
    icon: '📱',
  },
};

// 统计卡片组件
const StatCard: React.FC<{
  title: string;
  value: string;
  change?: number;
  color?: string;
}> = ({ title, value, change, color = 'text-blue-600' }) => {
  const changeColor = change && change > 0 ? 'text-green-600' : change && change < 0 ? 'text-red-600' : 'text-gray-600';
  const changeIcon = change && change > 0 ? '↗' : change && change < 0 ? '↘' : '→';
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
      <h3 className="text-sm font-medium text-gray-700 mb-1">{title}</h3>
      <div className="flex items-center justify-between">
        <p className={`text-xl font-bold ${color}`}>{value}</p>
        {change !== undefined && (
          <span className={`text-sm flex items-center ${changeColor}`}>
            {changeIcon} {Math.abs(change).toFixed(1)}%
          </span>
        )}
      </div>
    </div>
  );
};

// 对比表格组件
const ComparisonTable: React.FC<{
  stats: DemoStats[];
  baseline: DemoStats;
}> = ({ stats, baseline }) => {
  const calculateImprovement = (current: number, baseline: number): number => {
    return ((baseline - current) / baseline) * 100;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">性能对比</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                场景
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                加载时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                缓存命中率
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                网络请求
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                内存使用
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户体验
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {stats.map((stat) => {
              const config = scenarioConfigs[stat.scenario];
              const isBaseline = stat.scenario === 'baseline';
              
              return (
                <tr key={stat.scenario} className={isBaseline ? 'bg-gray-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-lg mr-2">{config.icon}</span>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{config.title}</div>
                        <div className="text-sm text-gray-500">{config.description}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{stat.loadTime}ms</div>
                    {!isBaseline && (
                      <div className="text-xs text-green-600">
                        -{calculateImprovement(stat.loadTime, baseline.loadTime).toFixed(1)}%
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{(stat.cacheHitRate * 100).toFixed(1)}%</div>
                    {!isBaseline && (
                      <div className="text-xs text-green-600">
                        +{((stat.cacheHitRate - baseline.cacheHitRate) * 100).toFixed(1)}%
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{stat.networkRequests}</div>
                    {!isBaseline && (
                      <div className="text-xs text-green-600">
                        -{calculateImprovement(stat.networkRequests, baseline.networkRequests).toFixed(1)}%
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{(stat.memoryUsage / 1024 / 1024).toFixed(1)}MB</div>
                    {!isBaseline && (
                      <div className="text-xs text-green-600">
                        -{calculateImprovement(stat.memoryUsage, baseline.memoryUsage).toFixed(1)}%
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      stat.userExperience === 'excellent' ? 'bg-green-100 text-green-800' :
                      stat.userExperience === 'good' ? 'bg-blue-100 text-blue-800' :
                      stat.userExperience === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {stat.userExperience}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// 实时监控组件
const RealTimeMonitor: React.FC<{
  currentScenario: DemoScenario;
  isRunning: boolean;
}> = ({ currentScenario, isRunning }) => {
  const queryClient = useQueryClient();
  const { collectMetrics, utils } = useCachePerformanceAnalyzer(queryClient);
  const [metrics, setMetrics] = useState<any>(null);

  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      const newMetrics = collectMetrics();
      setMetrics(newMetrics);
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, collectMetrics]);

  if (!metrics) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">实时监控</h3>
        <p className="text-gray-500">启动演示查看实时数据</p>
      </div>
    );
  }

  const config = scenarioConfigs[currentScenario];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">实时监控</h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
          {config.icon} {config.title}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <StatCard
          title="查询数量"
          value={metrics.totalQueries.toString()}
          color="text-blue-600"
        />
        <StatCard
          title="命中率"
          value={utils.formatPercentage(metrics.cacheHitRate)}
          color="text-green-600"
        />
        <StatCard
          title="响应时间"
          value={utils.formatTime(metrics.averageQueryTime)}
          color="text-purple-600"
        />
        <StatCard
          title="内存使用"
          value={utils.formatBytes(metrics.estimatedMemoryUsage)}
          color="text-orange-600"
        />
      </div>

      {/* 状态指示器 */}
      <div className="mt-4 flex items-center">
        <div className={`w-2 h-2 rounded-full mr-2 ${isRunning ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
        <span className="text-sm text-gray-600">
          {isRunning ? '监控中...' : '已停止'}
        </span>
      </div>
    </div>
  );
};

// 主组件
const CacheOptimizationDemo: React.FC = () => {
  const queryClient = useQueryClient();
  const prefetchStrategy = usePrefetchStrategy(queryClient);
  const offlineCache = useOfflineCache(queryClient);
  
  const [currentScenario, setCurrentScenario] = useState<DemoScenario>('baseline');
  const [isRunning, setIsRunning] = useState(false);
  const [demoStats, setDemoStats] = useState<DemoStats[]>([]);
  const [simulationProgress, setSimulationProgress] = useState(0);

  // 模拟不同场景的数据
  const generateScenarioStats = useCallback((scenario: DemoScenario): DemoStats => {
    const baseStats = {
      scenario,
      loadTime: 2000,
      cacheHitRate: 0.3,
      networkRequests: 20,
      memoryUsage: 10 * 1024 * 1024, // 10MB
      userExperience: 'poor' as const,
    };

    switch (scenario) {
      case 'baseline':
        return baseStats;
      
      case 'optimized':
        return {
          ...baseStats,
          loadTime: 800,
          cacheHitRate: 0.7,
          networkRequests: 8,
          memoryUsage: 8 * 1024 * 1024,
          userExperience: 'good',
        };
      
      case 'prefetch':
        return {
          ...baseStats,
          loadTime: 400,
          cacheHitRate: 0.85,
          networkRequests: 5,
          memoryUsage: 12 * 1024 * 1024,
          userExperience: 'excellent',
        };
      
      case 'offline':
        return {
          ...baseStats,
          loadTime: 200,
          cacheHitRate: 0.95,
          networkRequests: 2,
          memoryUsage: 15 * 1024 * 1024,
          userExperience: 'excellent',
        };
      
      default:
        return baseStats;
    }
  }, []);

  // 运行场景演示
  const runScenario = useCallback(async (scenario: DemoScenario) => {
    setCurrentScenario(scenario);
    setIsRunning(true);
    setSimulationProgress(0);

    // 模拟进度
    for (let i = 0; i <= 100; i += 10) {
      setSimulationProgress(i);
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // 生成统计数据
    const stats = generateScenarioStats(scenario);
    setDemoStats(prev => {
      const updated = prev.filter(s => s.scenario !== scenario);
      return [...updated, stats];
    });

    // 应用场景配置
    switch (scenario) {
      case 'prefetch':
        await prefetchStrategy.prefetchIntelligent({ route: '/demo' });
        break;
      case 'offline':
        // 模拟离线操作
        offlineCache.addOfflineOperation({
          type: 'CREATE',
          endpoint: '/api/demo',
          data: { demo: true },
          maxRetries: 3,
        });
        break;
    }

    setIsRunning(false);
  }, [generateScenarioStats, prefetchStrategy, offlineCache]);

  // 运行所有场景
  const runAllScenarios = useCallback(async () => {
    const scenarios: DemoScenario[] = ['baseline', 'optimized', 'prefetch', 'offline'];
    
    for (const scenario of scenarios) {
      await runScenario(scenario);
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }, [runScenario]);

  // 重置演示
  const resetDemo = useCallback(() => {
    setCurrentScenario('baseline');
    setIsRunning(false);
    setDemoStats([]);
    setSimulationProgress(0);
    queryClient.clear();
  }, [queryClient]);

  const baseline = demoStats.find(s => s.scenario === 'baseline');

  return (
    <div className="p-6 space-y-6">
      {/* 标题和控制面板 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800">缓存优化效果演示</h2>
        <div className="flex items-center space-x-3">
          <button
            onClick={runAllScenarios}
            disabled={isRunning}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
          >
            {isRunning ? '运行中...' : '运行全部场景'}
          </button>
          <button
            onClick={resetDemo}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            重置
          </button>
        </div>
      </div>

      {/* 场景选择 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {(Object.keys(scenarioConfigs) as DemoScenario[]).map((scenario) => {
          const config = scenarioConfigs[scenario];
          const isActive = currentScenario === scenario;
          const hasStats = demoStats.some(s => s.scenario === scenario);
          
          return (
            <button
              key={scenario}
              onClick={() => runScenario(scenario)}
              disabled={isRunning}
              className={`p-4 rounded-lg border-2 transition-all ${
                isActive
                  ? 'border-blue-500 bg-blue-50'
                  : hasStats
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-2xl">{config.icon}</span>
                {hasStats && <span className="text-green-600">✓</span>}
              </div>
              <h3 className="font-semibold text-gray-800">{config.title}</h3>
              <p className="text-sm text-gray-600">{config.description}</p>
            </button>
          );
        })}
      </div>

      {/* 进度条 */}
      {isRunning && (
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              正在运行 {scenarioConfigs[currentScenario].title}
            </span>
            <span className="text-sm text-gray-500">{simulationProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${simulationProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* 实时监控 */}
      <RealTimeMonitor currentScenario={currentScenario} isRunning={isRunning} />

      {/* 性能对比 */}
      {demoStats.length > 0 && baseline && (
        <ComparisonTable stats={demoStats} baseline={baseline} />
      )}

      {/* 关键洞察 */}
      {demoStats.length > 1 && (
        <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">关键洞察</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">🚀 缓存优化效果</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 加载时间减少 60-90%</li>
                <li>• 缓存命中率提升至 70%+</li>
                <li>• 网络请求减少 60%</li>
                <li>• 用户体验显著改善</li>
              </ul>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2">⚡ 预取策略优势</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 响应时间降至 400ms</li>
                <li>• 缓存命中率达到 85%</li>
                <li>• 智能预加载相关数据</li>
                <li>• 用户体验达到优秀级别</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheOptimizationDemo;