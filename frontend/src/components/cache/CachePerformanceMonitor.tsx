/**
 * 缓存性能监控组件
 * 实时显示React Query缓存性能指标
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useCachePerformanceAnalyzer } from '../../config/cache';

// 性能监控Props
interface CachePerformanceMonitorProps {
  className?: string;
  refreshInterval?: number;
  showDetails?: boolean;
}

// 指标卡片组件
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  change?: number;
  color?: string;
  suffix?: string;
}> = ({ title, value, change, color = 'text-blue-600', suffix = '' }) => {
  const changeColor = change && change > 0 ? 'text-green-600' : change && change < 0 ? 'text-red-600' : 'text-gray-600';
  
  return (
    <div className="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-700">{title}</h3>
          <p className={`text-2xl font-bold ${color}`}>
            {value}{suffix}
          </p>
        </div>
        {change !== undefined && (
          <div className={`text-sm ${changeColor}`}>
            {change > 0 ? '+' : ''}{change.toFixed(1)}%
          </div>
        )}
      </div>
    </div>
  );
};

// 健康状态组件
const HealthStatusCard: React.FC<{
  status: 'excellent' | 'good' | 'fair' | 'poor';
  score: number;
  issues: number;
  recommendations: number;
}> = ({ status, score, issues, recommendations }) => {
  const statusConfig = {
    excellent: { color: 'bg-green-100 text-green-800', icon: '✅' },
    good: { color: 'bg-blue-100 text-blue-800', icon: '👍' },
    fair: { color: 'bg-yellow-100 text-yellow-800', icon: '⚠️' },
    poor: { color: 'bg-red-100 text-red-800', icon: '❌' },
  };

  const config = statusConfig[status];

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-800">缓存健康状态</h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${config.color}`}>
          {config.icon} {status.toUpperCase()}
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-gray-600">健康分数</span>
          <span className="font-semibold">{score}/100</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">发现问题</span>
          <span className="font-semibold text-red-600">{issues}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">优化建议</span>
          <span className="font-semibold text-blue-600">{recommendations}</span>
        </div>
      </div>
      
      {/* 进度条 */}
      <div className="mt-3">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              score >= 90 ? 'bg-green-500' : 
              score >= 70 ? 'bg-blue-500' :
              score >= 50 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${score}%` }}
          />
        </div>
      </div>
    </div>
  );
};

// 查询详情表格组件
const QueryDetailsTable: React.FC<{
  queries: any[];
  onQueryClick?: (queryHash: string) => void;
}> = ({ queries, onQueryClick }) => {
  const [sortBy, setSortBy] = useState<string>('executionTime');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const sortedQueries = [...queries].sort((a, b) => {
    const aValue = a[sortBy] || 0;
    const bValue = b[sortBy] || 0;
    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
  });

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">查询性能详情</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('queryKey')}
              >
                查询键
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('executionTime')}
              >
                执行时间
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('dataSize')}
              >
                数据大小
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('isStale')}
              >
                状态
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('errorCount')}
              >
                错误次数
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedQueries.slice(0, 10).map((query, index) => (
              <tr 
                key={query.queryHash} 
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => onQueryClick?.(query.queryHash)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {JSON.stringify(query.queryKey).substring(0, 50)}...
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`text-sm ${query.executionTime > 2000 ? 'text-red-600' : 'text-gray-900'}`}>
                    {query.executionTime}ms
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {(query.dataSize / 1024).toFixed(1)}KB
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    query.isStale ? 'bg-yellow-100 text-yellow-800' : 
                    query.isFetching ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {query.isStale ? '过期' : query.isFetching ? '获取中' : '新鲜'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className={`text-sm ${query.errorCount > 0 ? 'text-red-600' : 'text-gray-900'}`}>
                    {query.errorCount}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// 主组件
const CachePerformanceMonitor: React.FC<CachePerformanceMonitorProps> = ({
  className = '',
  refreshInterval = 5000,
  showDetails = true,
}) => {
  const queryClient = useQueryClient();
  const { analyzer, collectMetrics, analyzeHealth, getQueryDetails, utils } = useCachePerformanceAnalyzer(queryClient);
  
  const [metrics, setMetrics] = useState<any>(null);
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [queryDetails, setQueryDetails] = useState<any[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [previousMetrics, setPreviousMetrics] = useState<any>(null);

  // 刷新性能指标
  const refreshMetrics = useCallback(() => {
    const newMetrics = collectMetrics();
    const newHealthStatus = analyzeHealth();
    const newQueryDetails = getQueryDetails();

    setPreviousMetrics(metrics);
    setMetrics(newMetrics);
    setHealthStatus(newHealthStatus);
    setQueryDetails(newQueryDetails);
  }, [collectMetrics, analyzeHealth, getQueryDetails, metrics]);

  // 设置定时刷新
  useEffect(() => {
    refreshMetrics();
    const interval = setInterval(refreshMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshMetrics, refreshInterval]);

  // 计算变化百分比
  const calculateChange = (current: number, previous: number): number => {
    if (!previous) return 0;
    return ((current - previous) / previous) * 100;
  };

  if (!metrics || !healthStatus) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 space-y-6 ${className}`}>
      {/* 标题和控制按钮 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800">缓存性能监控</h2>
        <div className="flex items-center space-x-3">
          <button
            onClick={refreshMetrics}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            刷新
          </button>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            {isExpanded ? '收缩' : '展开'}
          </button>
        </div>
      </div>

      {/* 关键指标网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="总查询数"
          value={metrics.totalQueries}
          change={previousMetrics ? calculateChange(metrics.totalQueries, previousMetrics.totalQueries) : undefined}
          color="text-blue-600"
        />
        <MetricCard
          title="缓存命中率"
          value={utils.formatPercentage(metrics.cacheHitRate)}
          change={previousMetrics ? calculateChange(metrics.cacheHitRate, previousMetrics.cacheHitRate) : undefined}
          color="text-green-600"
        />
        <MetricCard
          title="平均响应时间"
          value={utils.formatTime(metrics.averageQueryTime)}
          change={previousMetrics ? calculateChange(metrics.averageQueryTime, previousMetrics.averageQueryTime) : undefined}
          color="text-purple-600"
        />
        <MetricCard
          title="内存使用"
          value={utils.formatBytes(metrics.estimatedMemoryUsage)}
          change={previousMetrics ? calculateChange(metrics.estimatedMemoryUsage, previousMetrics.estimatedMemoryUsage) : undefined}
          color="text-orange-600"
        />
      </div>

      {/* 健康状态 */}
      <HealthStatusCard
        status={healthStatus.overall}
        score={healthStatus.score}
        issues={healthStatus.issues.length}
        recommendations={healthStatus.recommendations.length}
      />

      {/* 详细信息（可展开） */}
      {isExpanded && (
        <div className="space-y-6">
          {/* 网络请求统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <MetricCard
              title="网络请求次数"
              value={metrics.networkRequestCount}
              color="text-indigo-600"
            />
            <MetricCard
              title="网络错误次数"
              value={metrics.networkErrorCount}
              color="text-red-600"
            />
            <MetricCard
              title="过期查询数"
              value={metrics.staleQueries}
              color="text-yellow-600"
            />
          </div>

          {/* 问题和建议 */}
          {healthStatus.issues.length > 0 && (
            <div className="bg-red-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-red-800 mb-3">发现的问题</h3>
              <div className="space-y-2">
                {healthStatus.issues.map((issue: any, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className={`inline-block w-2 h-2 rounded-full mt-2 ${
                      issue.severity === 'critical' ? 'bg-red-600' :
                      issue.severity === 'high' ? 'bg-orange-500' :
                      issue.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                    }`}></span>
                    <div>
                      <p className="font-medium text-red-800">{issue.description}</p>
                      <p className="text-sm text-red-600">{issue.impact}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 优化建议 */}
          {healthStatus.recommendations.length > 0 && (
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-800 mb-3">优化建议</h3>
              <div className="space-y-2">
                {healthStatus.recommendations.map((recommendation: any, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <span className={`inline-block w-2 h-2 rounded-full mt-2 ${
                      recommendation.priority === 'high' ? 'bg-red-500' :
                      recommendation.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    }`}></span>
                    <div>
                      <p className="font-medium text-blue-800">{recommendation.description}</p>
                      <p className="text-sm text-blue-600">{recommendation.implementation}</p>
                      <p className="text-xs text-blue-500">预期改进: {recommendation.expectedImprovement}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 查询详情表格 */}
          {showDetails && queryDetails.length > 0 && (
            <QueryDetailsTable
              queries={queryDetails}
              onQueryClick={(queryHash) => {
                console.log('Query clicked:', queryHash);
                // 可以在这里添加查询详情弹窗
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default CachePerformanceMonitor;