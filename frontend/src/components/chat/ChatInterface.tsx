import React, { useState, useCallback, useEffect } from 'react';
import { Bot, MoreVertical, Trash2, Edit3, Share } from 'lucide-react';
import { clsx } from 'clsx';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import type { 
  ChatContainerProps, 
  ChatMessageResponse, 
  ChatSessionResponse,
} from '@/types';

interface MessageStatus {
  status: 'sending' | 'sent' | 'delivered' | 'error';
  error?: string;
}

interface ExtendedChatContainerProps extends ChatContainerProps {
  // Session management
  currentSession?: ChatSessionResponse;
  onSessionCreate?: () => Promise<void>;
  onSessionUpdate?: (sessionId: string, data: { title?: string }) => Promise<void>;
  onSessionDelete?: (sessionId: string) => Promise<void>;
  
  // Message management
  messages: ChatMessageResponse[];
  onMessageSend: (message: string, sessionId?: string) => Promise<void>;
  onMessageRetry?: (messageId: string) => Promise<void>;
  onLoadMoreMessages?: () => Promise<void>;
  hasMoreMessages?: boolean;
  isLoadingMessages?: boolean;
  
  // Streaming
  isStreaming?: boolean;
  streamingMessageId?: string;
  
  // File upload
  onFileUpload?: (files: File[]) => Promise<void>;
  canUploadFiles?: boolean;
  
  // Voice features
  onVoiceToggle?: (isRecording: boolean) => void;
  isRecording?: boolean;
  canUseVoice?: boolean;
  
  // UI preferences
  showSessionActions?: boolean;
  showVoiceInput?: boolean;
  showFileUpload?: boolean;
  compactMode?: boolean;
  
  // Error handling
  error?: string;
  onErrorDismiss?: () => void;
}

export const ChatInterface: React.FC<ExtendedChatContainerProps> = ({
  sessionId,
  height = '100%',
  showHeader = true,
  // showSidebar = false,
  // onSessionChange,
  currentSession,
  // onSessionCreate,
  onSessionUpdate,
  onSessionDelete,
  messages,
  onMessageSend,
  onMessageRetry,
  onLoadMoreMessages,
  hasMoreMessages = false,
  isLoadingMessages = false,
  isStreaming = false,
  streamingMessageId,
  onFileUpload,
  canUploadFiles = true,
  onVoiceToggle,
  isRecording = false,
  canUseVoice = false,
  showSessionActions = true,
  // showVoiceInput = false,
  showFileUpload = true,
  compactMode = false,
  error,
  onErrorDismiss,
  className
}) => {
  const [inputValue, setInputValue] = useState('');
  const [isInputLoading, setIsInputLoading] = useState(false);
  const [messageStatuses, setMessageStatuses] = useState<Record<string, MessageStatus>>({});
  const [showSessionMenu, setShowSessionMenu] = useState(false);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [editTitleValue, setEditTitleValue] = useState('');

  // 处理消息发送
  const handleMessageSend = useCallback(async (message: string) => {
    if (!message.trim() || isInputLoading) return;

    setIsInputLoading(true);
    setInputValue('');

    try {
      // 创建临时消息ID用于状态跟踪
      const tempMessageId = `temp_${Date.now()}`;
      
      // 设置发送状态
      setMessageStatuses(prev => ({
        ...prev,
        [tempMessageId]: { status: 'sending' }
      }));

      await onMessageSend(message, sessionId);
      
      // 清除临时状态
      setMessageStatuses(prev => {
        const newStatuses = { ...prev };
        delete newStatuses[tempMessageId];
        return newStatuses;
      });
      
    } catch (error) {
      console.error('发送消息失败:', error);
      
      // 设置错误状态
      setMessageStatuses(prev => ({
        ...prev,
        [`temp_${Date.now()}`]: { 
          status: 'error', 
          error: error instanceof Error ? error.message : '发送失败'
        }
      }));
    } finally {
      setIsInputLoading(false);
    }
  }, [isInputLoading, onMessageSend, sessionId]);

  // 处理消息重试
  const handleMessageRetry = useCallback(async (messageId: string) => {
    const messageStatus = messageStatuses[messageId];
    if (!messageStatus || messageStatus.status !== 'error') return;

    try {
      setMessageStatuses(prev => ({
        ...prev,
        [messageId]: { status: 'sending' }
      }));

      await onMessageRetry?.(messageId);
      
      setMessageStatuses(prev => ({
        ...prev,
        [messageId]: { status: 'delivered' }
      }));
    } catch (error) {
      setMessageStatuses(prev => ({
        ...prev,
        [messageId]: { 
          status: 'error',
          error: error instanceof Error ? error.message : '重试失败'
        }
      }));
    }
  }, [messageStatuses, onMessageRetry]);

  // 处理消息复制
  const handleMessageCopy = useCallback((messageId: string) => {
    // 消息复制逻辑可以在MessageItem中处理
    console.log('复制消息:', messageId);
  }, []);

  // 处理文件上传
  const handleFileUpload = useCallback(async (files: File[]) => {
    if (!onFileUpload) return;
    
    try {
      await onFileUpload(files);
    } catch (error) {
      console.error('文件上传失败:', error);
    }
  }, [onFileUpload]);

  // 处理会话标题编辑
  const handleTitleEdit = useCallback(() => {
    if (!currentSession) return;
    setEditTitleValue(currentSession.title || '');
    setIsEditingTitle(true);
  }, [currentSession]);

  const handleTitleSave = useCallback(async () => {
    if (!currentSession || !editTitleValue.trim()) {
      setIsEditingTitle(false);
      return;
    }

    try {
      await onSessionUpdate?.(currentSession.id, { title: editTitleValue.trim() });
      setIsEditingTitle(false);
    } catch (error) {
      console.error('更新会话标题失败:', error);
    }
  }, [currentSession, editTitleValue, onSessionUpdate]);

  const handleTitleCancel = useCallback(() => {
    setIsEditingTitle(false);
    setEditTitleValue('');
  }, []);

  // 处理会话删除
  const handleSessionDelete = useCallback(async () => {
    if (!currentSession || !onSessionDelete) return;
    
    if (window.confirm('确定要删除这个会话吗？此操作不可撤销。')) {
      try {
        await onSessionDelete(currentSession.id);
      } catch (error) {
        console.error('删除会话失败:', error);
      }
    }
  }, [currentSession, onSessionDelete]);

  // 清理状态
  useEffect(() => {
    if (!isStreaming && streamingMessageId) {
      // 流式消息完成后，清理状态
      setMessageStatuses(prev => {
        const newStatuses = { ...prev };
        delete newStatuses[streamingMessageId];
        return newStatuses;
      });
    }
  }, [isStreaming, streamingMessageId]);

  const renderHeader = () => {
    if (!showHeader) return null;

    return (
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Bot className="h-5 w-5 text-white" />
          </div>
          
          <div className="flex-1 min-w-0">
            {isEditingTitle ? (
              <div className="flex items-center space-x-2">
                <input
                  value={editTitleValue}
                  onChange={(e) => setEditTitleValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleTitleSave();
                    if (e.key === 'Escape') handleTitleCancel();
                  }}
                  onBlur={handleTitleSave}
                  className="text-lg font-semibold text-gray-900 bg-transparent border-b border-blue-500 focus:outline-none"
                  autoFocus
                />
              </div>
            ) : (
              <>
                <h1 className="text-lg font-semibold text-gray-900 truncate">
                  {currentSession?.title || 'AI智能助手'}
                </h1>
                <p className="text-sm text-gray-500 truncate">
                  {currentSession 
                    ? `会话 • ${new Date(currentSession.created_at || '').toLocaleDateString()}`
                    : '准备为您提供帮助'
                  }
                </p>
              </>
            )}
          </div>
        </div>

        {showSessionActions && currentSession && (
          <div className="relative">
            <button
              onClick={() => setShowSessionMenu(!showSessionMenu)}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
            >
              <MoreVertical className="h-4 w-4" />
            </button>

            {showSessionMenu && (
              <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10">
                <button
                  onClick={() => {
                    handleTitleEdit();
                    setShowSessionMenu(false);
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <Edit3 className="h-4 w-4" />
                  <span>重命名会话</span>
                </button>
                
                <button
                  onClick={() => setShowSessionMenu(false)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <Share className="h-4 w-4" />
                  <span>分享会话</span>
                </button>
                
                <hr className="my-1" />
                
                <button
                  onClick={() => {
                    handleSessionDelete();
                    setShowSessionMenu(false);
                  }}
                  className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                >
                  <Trash2 className="h-4 w-4" />
                  <span>删除会话</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderErrorBanner = () => {
    if (!error) return null;

    return (
      <div className="bg-red-50 border-l-4 border-red-400 p-4">
        <div className="flex items-center justify-between">
          <div className="flex">
            <div className="text-red-400">
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
          {onErrorDismiss && (
            <button
              onClick={onErrorDismiss}
              className="text-red-400 hover:text-red-600"
            >
              <span className="sr-only">关闭</span>
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div 
      className={clsx(
        'flex flex-col bg-white rounded-lg shadow-sm border border-gray-200',
        compactMode ? 'h-full' : 'h-full max-h-screen',
        className
      )}
      style={{ height }}
      onClick={() => setShowSessionMenu(false)} // 点击外部关闭菜单
    >
      {renderHeader()}
      {renderErrorBanner()}
      
      {/* 消息列表区域 */}
      <div className="flex-1 min-h-0">
        <MessageList
          messages={messages}
          isLoading={isLoadingMessages}
          onLoadMore={onLoadMoreMessages}
          hasMore={hasMoreMessages}
          streamingMessageId={streamingMessageId}
          messageStatuses={messageStatuses}
          onMessageRetry={handleMessageRetry}
          onMessageCopy={handleMessageCopy}
          autoScroll={true}
          showTypingIndicator={isStreaming}
          emptyStateText="开始新对话"
          emptyStateSubtext="输入您的问题，我来帮助您找到答案"
        />
      </div>

      {/* 输入区域 */}
      <div className="border-t border-gray-200 p-4">
        <MessageInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleMessageSend}
          disabled={isStreaming}
          loading={isInputLoading || isStreaming}
          placeholder={
            isStreaming 
              ? "AI正在回复中..." 
              : "输入您的问题..."
          }
          showFileUpload={showFileUpload && canUploadFiles}
          canUploadFiles={canUploadFiles}
          canUseVoice={canUseVoice}
          showEmojiPicker={false}
          onFileUpload={handleFileUpload}
          onVoiceToggle={onVoiceToggle || (() => {})}
          isRecording={isRecording}
          autoFocus={!compactMode}
          maxLength={4000}
          minRows={1}
          maxRows={5}
        />
      </div>
    </div>
  );
}; 