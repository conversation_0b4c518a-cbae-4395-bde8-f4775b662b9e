import React, { lazy, Suspense } from 'react';
import LoadingSpinner from '../common/LoadingSpinner';
import { createLazyComponent } from '../../utils/preload';
import { ChatSessionResponse, ChatMessageResponse } from '../../types/chat';

// 懒加载聊天界面组件
const ChatInterfaceComponent = createLazyComponent(
  'chatInterface',
  () => import('./ChatInterface')
);

interface LazyChatInterfaceProps {
  sessionId: string;
  currentSession?: ChatSessionResponse;
  messages: ChatMessageResponse[];
  isStreaming: boolean;
  onMessageSend: (message: string) => void;
  onSessionCreate: () => void;
  onSessionDelete: (sessionId: string) => void;
  className?: string;
}

export const LazyChatInterface: React.FC<LazyChatInterfaceProps> = (props) => {
  return (
    <Suspense
      fallback={
        <div className="h-full flex items-center justify-center">
          <LoadingSpinner size="md" text="聊天界面加载中..." />
        </div>
      }
    >
      <ChatInterfaceComponent {...props} />
    </Suspense>
  );
};

export default LazyChatInterface;