import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Square, 
  Mic, 
  MicOff,
  AlertCircle 
} from 'lucide-react';
import { clsx } from 'clsx';
import type { ChatInputBoxProps } from '@/types';

interface ExtendedChatInputProps extends ChatInputBoxProps {
  onFileUpload?: (files: File[]) => void;
  onVoiceToggle?: (isRecording: boolean) => void;
  isRecording?: boolean;
  canUploadFiles?: boolean;
  canUseVoice?: boolean;
  canUseEmoji?: boolean;
  acceptedFileTypes?: string;
  maxFileSize?: number; // in MB
  error?: string;
  hint?: string;
  rows?: number;
  minRows?: number;
  maxRows?: number;
}

export const MessageInput: React.FC<ExtendedChatInputProps> = ({
  value = '',
  onChange,
  onSend,
  placeholder = "输入您的问题...",
  disabled = false,
  loading = false,
  maxLength = 4000,
  showFileUpload = true,
  showEmojiPicker = false,
  autoFocus = false,
  onFileUpload,
  onVoiceToggle,
  isRecording = false,
  canUploadFiles = true,
  canUseVoice = false,
  canUseEmoji = false,
  acceptedFileTypes = "image/*,text/*,.pdf,.doc,.docx",
  maxFileSize = 10,
  error,
  hint = "按 Enter 发送消息，Shift + Enter 换行",
  rows = 1,
  minRows = 1,
  maxRows = 5,
  className
}) => {
  const [currentRows, setCurrentRows] = useState(rows);
  const [isDragOver, setIsDragOver] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 自动调整文本框高度
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';
    
    // 计算行数
    const lineHeight = 24; // 大约的行高
    const contentHeight = textarea.scrollHeight;
    const calculatedRows = Math.max(minRows, Math.min(maxRows, Math.ceil(contentHeight / lineHeight)));
    
    setCurrentRows(calculatedRows);
    textarea.style.height = `${calculatedRows * lineHeight}px`;
  }, [minRows, maxRows]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    
    // 检查长度限制
    if (maxLength && newValue.length > maxLength) {
      return;
    }
    
    onChange?.(newValue);
    adjustTextareaHeight();
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift + Enter 换行，不做任何处理
        return;
      } else {
        // Enter 发送消息
        e.preventDefault();
        handleSend();
      }
    }
  };

  // 发送消息
  const handleSend = () => {
    if (!value.trim() || disabled || loading) return;
    onSend(value.trim());
  };

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileUpload(files);
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  };

  // 处理文件上传
  const handleFileUpload = (files: File[]) => {
    if (!canUploadFiles) return;

    // 验证文件
    const validFiles = files.filter(file => {
      // 检查文件大小
      if (file.size > maxFileSize * 1024 * 1024) {
        console.warn(`文件 ${file.name} 超过最大大小限制 ${maxFileSize}MB`);
        return false;
      }
      return true;
    });

    if (validFiles.length > 0) {
      onFileUpload?.(validFiles);
    }
  };

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  // 语音输入切换
  const handleVoiceToggle = () => {
    if (!canUseVoice) return;
    onVoiceToggle?.(!isRecording);
  };

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  // 初始化高度
  useEffect(() => {
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  const canSend = value.trim().length > 0 && !disabled && !loading;

  return (
    <div className={clsx('relative', className)}>
      {/* 拖拽覆盖层 */}
      {isDragOver && canUploadFiles && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-blue-500 border-dashed rounded-lg flex items-center justify-center z-10">
          <div className="text-center">
            <Paperclip className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <p className="text-blue-600 font-medium">释放文件以上传</p>
          </div>
        </div>
      )}

      <div
        className={clsx(
          'border rounded-lg transition-all duration-200',
          error 
            ? 'border-red-300 focus-within:border-red-500 focus-within:ring-red-500' 
            : 'border-gray-300 focus-within:border-blue-500 focus-within:ring-blue-500',
          'focus-within:ring-2 focus-within:ring-opacity-20',
          isDragOver && 'border-blue-500 ring-2 ring-blue-500 ring-opacity-20'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex items-end space-x-2 p-3">
          {/* 工具按钮 - 左侧 */}
          <div className="flex items-end space-x-1">
            {/* 文件上传按钮 */}
            {showFileUpload && canUploadFiles && (
              <>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept={acceptedFileTypes}
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={disabled || loading}
                  className={clsx(
                    'p-2 rounded-md transition-colors',
                    disabled || loading
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  )}
                  title="上传文件"
                >
                  <Paperclip className="h-4 w-4" />
                </button>
              </>
            )}

            {/* 表情符号按钮 */}
            {showEmojiPicker && canUseEmoji && (
              <button
                type="button"
                disabled={disabled || loading}
                className={clsx(
                  'p-2 rounded-md transition-colors',
                  disabled || loading
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                )}
                title="表情符号"
              >
                <Smile className="h-4 w-4" />
              </button>
            )}

            {/* 语音输入按钮 */}
            {canUseVoice && (
              <button
                type="button"
                onClick={handleVoiceToggle}
                disabled={disabled || loading}
                className={clsx(
                  'p-2 rounded-md transition-colors',
                  isRecording
                    ? 'text-red-600 bg-red-50 hover:bg-red-100'
                    : disabled || loading
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                )}
                title={isRecording ? "停止录音" : "开始录音"}
              >
                {isRecording ? (
                  <MicOff className="h-4 w-4" />
                ) : (
                  <Mic className="h-4 w-4" />
                )}
              </button>
            )}
          </div>

          {/* 文本输入区域 */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled || loading}
              className={clsx(
                'w-full resize-none border-0 outline-none bg-transparent',
                'placeholder-gray-500 text-gray-900',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'text-sm leading-6'
              )}
              style={{
                height: `${currentRows * 24}px`,
                minHeight: `${minRows * 24}px`,
                maxHeight: `${maxRows * 24}px`
              }}
              rows={currentRows}
            />
            
            {/* 字符计数 */}
            {maxLength && (
              <div className="absolute bottom-1 right-1 text-xs text-gray-400">
                {value.length}/{maxLength}
              </div>
            )}
          </div>

          {/* 发送按钮 */}
          <div className="flex items-end">
            {loading ? (
              <button
                type="button"
                disabled
                className="p-2 bg-gray-400 text-white rounded-md cursor-not-allowed"
                title="发送中..."
              >
                <Square className="h-4 w-4" />
              </button>
            ) : (
              <button
                type="button"
                onClick={handleSend}
                disabled={!canSend}
                className={clsx(
                  'p-2 rounded-md transition-colors',
                  canSend
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                )}
                title="发送消息"
              >
                <Send className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 底部信息 */}
      <div className="flex items-center justify-between mt-2 px-1">
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          {hint && (
            <span>{hint}</span>
          )}
          
          {isRecording && (
            <div className="flex items-center space-x-1 text-red-600">
              <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
              <span>正在录音...</span>
            </div>
          )}
        </div>

        {error && (
          <div className="flex items-center space-x-1 text-red-600 text-xs">
            <AlertCircle className="h-3 w-3" />
            <span>{error}</span>
          </div>
        )}
      </div>
    </div>
  );
}; 