import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { 
  Bo<PERSON>, 
  User, 
  Clock, 
  Check, 
  <PERSON><PERSON><PERSON><PERSON>, 
  AlertCircle, 
  Copy,
  RotateCcw
} from 'lucide-react';
import { clsx } from 'clsx';
import type { MessageBubbleProps } from '@/types';

interface MessageStatus {
  status: 'sending' | 'sent' | 'delivered' | 'error';
  error?: string;
}

interface ExtendedMessageBubbleProps extends MessageBubbleProps {
  messageStatus?: MessageStatus;
  showActions?: boolean;
}

export const MessageItem: React.FC<ExtendedMessageBubbleProps> = ({
  message,
  showAvatar = true,
  showTimestamp = true,
  isStreaming = false,
  messageStatus = { status: 'delivered' },
  showActions = true,
  onRetry,
  onCopy,
  // onEdit,
  // onDelete,
  className
}) => {
  const isUser = message.role === 'user';
  // const isAssistant = message.role === 'assistant';

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      onCopy?.();
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderStatusIcon = () => {
    if (!isUser) return null;

    switch (messageStatus.status) {
      case 'sending':
        return <Clock className="h-3 w-3 text-gray-400 animate-pulse" />;
      case 'sent':
        return <Check className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-blue-400" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-400" />;
      default:
        return null;
    }
  };

  const renderAvatar = () => {
    if (!showAvatar) return null;

    return (
      <div className="flex-shrink-0">
        {isUser ? (
          <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-blue-600" />
          </div>
        ) : (
          <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
            <Bot className="h-4 w-4 text-green-600" />
          </div>
        )}
      </div>
    );
  };

  const renderMarkdownContent = () => {
    return (
      <ReactMarkdown
        className="prose prose-sm max-w-none text-inherit"
        components={{
          code({ inline, className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <SyntaxHighlighter
                style={oneDark as any}
                language={match[1]}
                PreTag="div"
                className="rounded-md !mt-2 !mb-2"
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code 
                className={clsx(
                  'px-1 py-0.5 rounded text-xs font-mono',
                  isUser 
                    ? 'bg-blue-800 bg-opacity-30' 
                    : 'bg-gray-200 text-gray-800'
                )} 
                {...props}
              >
                {children}
              </code>
            );
          },
          p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
          blockquote: ({ children }) => (
            <blockquote className={clsx(
              'border-l-4 pl-4 italic mb-2',
              isUser ? 'border-blue-300' : 'border-gray-300'
            )}>
              {children}
            </blockquote>
          ),
          h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
        }}
      >
        {message.content}
      </ReactMarkdown>
    );
  };

  const renderSources = () => {
    if (!message.sources || message.sources.length === 0) return null;

    return (
      <div className="mt-3 pt-2 border-t border-opacity-20 border-gray-300">
        <p className="text-xs opacity-75 mb-1">参考文档:</p>
        <div className="flex flex-wrap gap-1">
          {message.sources.map((source: any, index: number) => (
            <span
              key={index}
              className={clsx(
                'inline-block px-2 py-1 rounded text-xs',
                isUser 
                  ? 'bg-blue-800 bg-opacity-30' 
                  : 'bg-gray-200 text-gray-700'
              )}
            >
              {source.filename || `文档 ${index + 1}`}
            </span>
          ))}
        </div>
      </div>
    );
  };

  const renderActions = () => {
    if (!showActions) return null;

    return (
      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 absolute top-2 right-2">
        <div className="flex space-x-1">
          <button
            onClick={handleCopy}
            className="p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
            title="复制消息"
          >
            <Copy className="h-3 w-3" />
          </button>
          
          {messageStatus.status === 'error' && onRetry && (
            <button
              onClick={onRetry}
              className="p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
              title="重试发送"
            >
              <RotateCcw className="h-3 w-3" />
            </button>
          )}
        </div>
      </div>
    );
  };

  const renderStreamingIndicator = () => {
    if (!isStreaming) return null;

    return (
      <div className="flex items-center space-x-1 mt-2">
        <div className="flex space-x-1">
          <div className="w-1 h-1 bg-current rounded-full animate-bounce"></div>
          <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <span className="text-xs opacity-60">正在输入...</span>
      </div>
    );
  };

  return (
    <div
      className={clsx(
        'flex group',
        isUser ? 'justify-end' : 'justify-start',
        className
      )}
    >
      <div
        className={clsx(
          'flex max-w-[80%] lg:max-w-[70%]',
          isUser ? 'flex-row-reverse space-x-reverse space-x-3' : 'flex-row space-x-3'
        )}
      >
        {renderAvatar()}
        
        <div className="flex flex-col min-w-0">
          <div
            className={clsx(
              'relative px-4 py-3 rounded-2xl break-words',
              isUser
                ? 'bg-blue-600 text-white rounded-br-md'
                : 'bg-gray-100 text-gray-900 rounded-bl-md',
              messageStatus.status === 'error' && 'border-2 border-red-300'
            )}
          >
            {renderActions()}
            
            <div className="text-sm">
              {renderMarkdownContent()}
              {renderStreamingIndicator()}
            </div>
            
            {renderSources()}
            
            {messageStatus.status === 'error' && messageStatus.error && (
              <div className="mt-2 pt-2 border-t border-red-300 border-opacity-30">
                <p className="text-xs text-red-200 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {messageStatus.error}
                </p>
              </div>
            )}
          </div>
          
          {(showTimestamp || messageStatus.status !== 'delivered') && (
            <div className={clsx(
              'flex items-center space-x-1 mt-1 px-1',
              isUser ? 'justify-end' : 'justify-start'
            )}>
              {showTimestamp && (
                <span className="text-xs text-gray-500">
                  {formatTimestamp(message.created_at)}
                </span>
              )}
              {renderStatusIcon()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 