import React, { useEffect, useRef, useCallback } from 'react';
import { Bot, MessageCircle } from 'lucide-react';
import { clsx } from 'clsx';
import { MessageItem } from './MessageItem';
import type { ChatHistoryProps } from '@/types';

interface MessageStatus {
  status: 'sending' | 'sent' | 'delivered' | 'error';
  error?: string;
}

interface ExtendedChatHistoryProps extends ChatHistoryProps {
  streamingMessageId?: string | undefined;
  messageStatuses?: Record<string, MessageStatus>;
  onMessageRetry?: (messageId: string) => void;
  onMessageCopy?: (messageId: string) => void;
  onMessageEdit?: (messageId: string) => void;
  onMessageDelete?: (messageId: string) => void;
  autoScroll?: boolean;
  showTypingIndicator?: boolean;
  emptyStateText?: string;
  emptyStateSubtext?: string;
  className?: string;
}

export const MessageList: React.FC<ExtendedChatHistoryProps> = ({
  messages,
  isLoading = false,
  onLoadMore,
  hasMore = false,
  streamingMessageId,
  messageStatuses = {},
  onMessageRetry,
  onMessageCopy,
  onMessageEdit,
  onMessageDelete,
  autoScroll = true,
  showTypingIndicator = true,
  emptyStateText = "开始新对话",
  emptyStateSubtext = "输入您的问题，我来帮助您找到答案",
  className
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isUserScrollingRef = useRef(false);
  const lastScrollTopRef = useRef(0);

  // 自动滚动到底部
  const scrollToBottom = useCallback((force = false) => {
    if (!autoScroll && !force) return;
    
    const container = containerRef.current;
    if (!container) return;

    // 如果用户正在手动滚动，不要自动滚动
    if (isUserScrollingRef.current && !force) return;

    // 使用 requestAnimationFrame 确保 DOM 更新后再滚动
    requestAnimationFrame(() => {
      messagesEndRef.current?.scrollIntoView({ 
        behavior: force ? 'auto' : 'smooth',
        block: 'end'
      });
    });
  }, [autoScroll]);

  // 检测用户是否在手动滚动
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 10;
    
    // 如果滚动位置发生变化且不在底部，说明用户在手动滚动
    if (scrollTop !== lastScrollTopRef.current && !isAtBottom) {
      isUserScrollingRef.current = true;
      
      // 如果用户滚动到接近底部，重置标志
      if (Math.abs(scrollHeight - scrollTop - clientHeight) < 100) {
        isUserScrollingRef.current = false;
      }
    } else if (isAtBottom) {
      isUserScrollingRef.current = false;
    }

    lastScrollTopRef.current = scrollTop;

    // 加载更多消息的逻辑
    if (scrollTop === 0 && hasMore && onLoadMore && !isLoading) {
      onLoadMore();
    }
  }, [hasMore, onLoadMore, isLoading]);

  // 监听消息变化，自动滚动
  useEffect(() => {
    if (messages.length > 0) {
      // 新消息到达时滚动到底部
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && (lastMessage.role === 'user' || !isUserScrollingRef.current)) {
        scrollToBottom();
      }
    }
  }, [messages.length, scrollToBottom]);

  // 监听流式消息，持续滚动
  useEffect(() => {
    if (streamingMessageId) {
      scrollToBottom();
    }
  }, [streamingMessageId, scrollToBottom]);

  const renderEmptyState = () => (
    <div className="flex-1 flex items-center justify-center">
      <div className="text-center text-gray-500 max-w-md mx-auto px-4">
        <Bot className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <h3 className="text-lg font-medium mb-2">{emptyStateText}</h3>
        <p className="text-sm text-gray-400">{emptyStateSubtext}</p>
        <div className="mt-6 space-y-2">
          <div className="text-xs text-gray-400">您可以尝试问我：</div>
          <div className="space-y-1 text-xs">
            <div className="bg-gray-50 rounded-lg px-3 py-2">🤔 "帮我总结一下这些文档的要点"</div>
            <div className="bg-gray-50 rounded-lg px-3 py-2">📚 "这个概念在文档中是如何解释的？"</div>
            <div className="bg-gray-50 rounded-lg px-3 py-2">💡 "基于已有资料，你有什么建议？"</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLoadMoreIndicator = () => {
    if (!hasMore) return null;
    
    return (
      <div className="flex justify-center py-4">
        {isLoading ? (
          <div className="flex items-center space-x-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
            <span className="text-sm">加载更多消息...</span>
          </div>
        ) : (
          <button
            onClick={onLoadMore}
            className="text-sm text-blue-600 hover:text-blue-800 hover:underline transition-colors"
          >
            点击加载更多消息
          </button>
        )}
      </div>
    );
  };

  const renderTypingIndicator = () => {
    if (!showTypingIndicator || !isLoading || messages.length === 0) return null;

    return (
      <div className="flex justify-start mb-4">
        <div className="flex space-x-3">
          <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
            <Bot className="h-4 w-4 text-green-600" />
          </div>
          <div className="bg-gray-100 text-gray-900 px-4 py-3 rounded-2xl rounded-bl-md">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleMessageAction = (messageId: string, action: string) => {
    switch (action) {
      case 'retry':
        onMessageRetry?.(messageId);
        break;
      case 'copy':
        onMessageCopy?.(messageId);
        break;
      case 'edit':
        onMessageEdit?.(messageId);
        break;
      case 'delete':
        onMessageDelete?.(messageId);
        break;
    }
  };

  if (messages.length === 0 && !isLoading) {
    return (
      <div className={clsx('flex flex-col h-full', className)}>
        {renderEmptyState()}
      </div>
    );
  }

  return (
    <div className={clsx('flex flex-col h-full', className)}>
      <div 
        ref={containerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto px-4 py-4 scroll-smooth"
        style={{ 
          scrollBehavior: 'smooth',
          overscrollBehavior: 'contain'
        }}
      >
        {renderLoadMoreIndicator()}
        
        <div className="space-y-6">
          {messages.map((message) => (
            <MessageItem
              key={message.id}
              message={message}
              isStreaming={streamingMessageId === message.id}
              messageStatus={messageStatuses[message.id] || { status: 'delivered' }}
              onRetry={() => handleMessageAction(message.id, 'retry')}
              onCopy={() => handleMessageAction(message.id, 'copy')}
              onEdit={() => handleMessageAction(message.id, 'edit')}
              onDelete={() => handleMessageAction(message.id, 'delete')}
              showActions={true}
              showAvatar={true}
              showTimestamp={true}
            />
          ))}
        </div>

        {renderTypingIndicator()}
        
        {/* 滚动锚点 */}
        <div ref={messagesEndRef} className="h-0" />
      </div>

      {/* 滚动到底部按钮 */}
      {isUserScrollingRef.current && (
        <div className="absolute bottom-4 right-4">
          <button
            onClick={() => scrollToBottom(true)}
            className="bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
            title="滚动到底部"
          >
            <MessageCircle className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
}; 