/**
 * 流式聊天集成示例
 * 演示如何在聊天界面中使用StreamingMessage组件
 */

import React, { useState, useCallback } from 'react';
import { Loader2 } from 'lucide-react';
import { StreamingMessage } from './StreamingMessage';
import { MessageItem } from './MessageItem';
import { MessageInput } from './MessageInput';
import { ChatRequest, ChatMessageResponse, MessageRole } from '../../types';

interface ChatMessage extends ChatMessageResponse {
  isStreaming?: boolean;
  streamingRequest?: ChatRequest | undefined;
}

export const StreamingChatExample: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentSession] = useState<string>('');

  // 发送普通消息
  const handleSendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: MessageRole.USER,
      content: message,
      session_id: currentSession,
      created_at: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);

    // 添加AI流式响应占位符
    const aiMessageRequest: ChatRequest = {
      message,
      session_id: currentSession,
      stream: true,
    };

    const aiMessage: ChatMessage = {
      id: `ai-${Date.now()}`,
      role: MessageRole.ASSISTANT,
      content: '', // 流式内容会通过StreamingMessage组件显示
      session_id: currentSession,
      created_at: new Date().toISOString(),
      isStreaming: true,
      streamingRequest: aiMessageRequest,
    };

    setMessages(prev => [...prev, aiMessage]);
  }, [currentSession]);

  // 处理流式消息完成
  const handleStreamComplete = useCallback((messageId: string, finalContent: string, responseMessageId?: string) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        return {
          ...msg,
          id: responseMessageId || msg.id,
          content: finalContent,
          isStreaming: false,
          streamingRequest: undefined,
        };
      }
      return msg;
    }));
  }, []);

  // 处理流式消息错误
  const handleStreamError = useCallback((messageId: string, error: string) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        return {
          ...msg,
          content: `错误: ${error}`,
          isStreaming: false,
          streamingRequest: undefined,
        };
      }
      return msg;
    }));
  }, []);

  return (
    <div className="streaming-chat-example h-full flex flex-col bg-gray-50">
      {/* 聊天标题 */}
      <div className="bg-white border-b px-4 py-3">
        <h2 className="text-lg font-semibold text-gray-800">
          流式聊天演示
        </h2>
        <p className="text-sm text-gray-600">
          使用 StreamingMessage 组件的实时流式聊天
        </p>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-medium mb-2">开始对话</h3>
              <p className="text-sm">发送消息体验流式响应效果</p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="flex flex-col space-y-2">
              {message.isStreaming && message.streamingRequest ? (
                // 流式消息组件
                <div className="flex justify-start">
                  <div className="max-w-3xl">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        AI
                      </div>
                      <span className="text-sm text-gray-600">AI助手</span>
                      <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                    </div>
                    <div className="bg-white rounded-lg p-4 shadow-sm border">
                      <StreamingMessage
                        request={message.streamingRequest}
                        onComplete={(content, responseId) => 
                          handleStreamComplete(message.id, content, responseId)
                        }
                        onError={(error) => handleStreamError(message.id, error)}
                        typewriterSpeed={30} // 调整打字速度
                        showConnectionStatus={true}
                        streamingOptions={{
                          maxRetries: 3,
                          retryDelay: 2000,
                        }}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                // 普通消息
                <MessageItem
                  message={message}
                  onCopy={() => navigator.clipboard.writeText(message.content)}
                  onEdit={() => {}} // 实际应用中实现编辑功能
                  onDelete={() => {
                    setMessages(prev => prev.filter(m => m.id !== message.id));
                  }}
                  onRetry={() => {
                    if (message.role === MessageRole.ASSISTANT) {
                      // 重新生成AI响应
                      setMessages(prev => prev.filter(m => m.id !== message.id));
                      const lastUserMessage = messages.slice().reverse().find(m => m.role === MessageRole.USER);
                      if (lastUserMessage) {
                        handleSendMessage(lastUserMessage.content);
                      }
                    }
                  }}
                  showTimestamp={true}
                  showActions={true}
                />
              )}
            </div>
          ))
        )}
      </div>

      {/* 输入区域 */}
      <div className="bg-white border-t p-4">
        <MessageInput
          onSend={handleSendMessage}
          placeholder="输入消息体验流式响应..."
          maxLength={2000}
          disabled={messages.some(m => m.isStreaming)}
          canUploadFiles={true}
          canUseVoice={false} // 在流式演示中禁用语音
          autoFocus={true}
          className="streaming-chat-input"
        />
        
        {/* 状态提示 */}
        {messages.some(m => m.isStreaming) && (
          <div className="mt-2 text-sm text-gray-500 flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>AI正在回复中...</span>
          </div>
        )}
      </div>

      {/* 开发提示 */}
      <div className="bg-blue-50 border-t p-3">
        <div className="text-xs text-blue-700">
          <strong>开发提示:</strong> 这是流式响应的集成示例。
          StreamingMessage组件支持自动重连、错误处理、打字机效果等功能。
        </div>
      </div>
    </div>
  );
};

export default StreamingChatExample; 