/**
 * 流式消息组件
 * 支持打字机效果、实时显示、markdown渲染
 */

import React, { useState, useEffect, useRef } from 'react';
import { Loader2, Wifi, WifiOff, AlertCircle, RotateCcw } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useStreaming, StreamingOptions } from '../../hooks/useStreaming';
import { ChatRequest, StreamChatResponse } from '../../types';
import { clsx } from 'clsx';

export interface StreamingMessageProps {
  request: ChatRequest;
  onComplete?: (content: string, messageId?: string) => void;
  onError?: (error: string) => void;
  className?: string;
  autoStart?: boolean;
  showConnectionStatus?: boolean;
  typewriterSpeed?: number; // 打字机速度（字符/秒）
  streamingOptions?: Partial<StreamingOptions>;
}

export const StreamingMessage: React.FC<StreamingMessageProps> = ({
  request,
  onComplete,
  onError,
  className,
  autoStart = true,
  showConnectionStatus = true,
  typewriterSpeed = 50, // 默认50字符/秒
  streamingOptions = {},
}) => {
  const [displayedContent, setDisplayedContent] = useState<string>('');
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [currentMessageId, setCurrentMessageId] = useState<string | undefined>();
  
  const typewriterIntervalRef = useRef<NodeJS.Timeout>();
  const fullContentRef = useRef<string>('');
  const typewriterIndexRef = useRef<number>(0);

  // 流式响应Hook配置
  const streamingOpts: StreamingOptions = {
    maxRetries: 3,
    retryDelay: 1000,
    autoReconnect: true,
    ...streamingOptions,
    onChunk: (chunk: StreamChatResponse) => {
      // 更新完整内容
      fullContentRef.current = chunk.chunk || '';
      setCurrentMessageId(chunk.message_id);
      streamingOptions.onChunk?.(chunk);
    },
    onComplete: (finalContent: string) => {
      fullContentRef.current = finalContent;
      setIsTyping(false);
      onComplete?.(finalContent, currentMessageId);
      streamingOptions.onComplete?.(finalContent);
    },
    onError: (error) => {
      setIsTyping(false);
      onError?.(error.message);
      streamingOptions.onError?.(error);
    },
  };

  const { state, startStream, stopStream, retry, reset } = useStreaming(streamingOpts);

  // 打字机效果
  useEffect(() => {
    if (!state.content || state.content === displayedContent) {
      return;
    }

    setIsTyping(true);
    
    // 计算延迟间隔（毫秒）
    const intervalMs = 1000 / typewriterSpeed;
    
    typewriterIntervalRef.current = setInterval(() => {
      const currentIndex = typewriterIndexRef.current;
      const targetContent = fullContentRef.current;
      
      if (currentIndex >= targetContent.length) {
        setIsTyping(false);
        clearInterval(typewriterIntervalRef.current);
        return;
      }
      
      // 逐字符显示
      typewriterIndexRef.current += 1;
      setDisplayedContent(targetContent.slice(0, typewriterIndexRef.current));
    }, intervalMs);

    return () => {
      if (typewriterIntervalRef.current) {
        clearInterval(typewriterIntervalRef.current);
      }
    };
  }, [state.content, typewriterSpeed, displayedContent]);

  // 自动开始
  useEffect(() => {
    if (autoStart && state.connectionState === 'idle') {
      startStream(request);
    }
  }, [autoStart, request, startStream, state.connectionState]);

  // 重置打字机状态当开始新的流式请求时
  useEffect(() => {
    if (state.isStreaming && state.connectionState === 'connecting') {
      setDisplayedContent('');
      typewriterIndexRef.current = 0;
      fullContentRef.current = '';
    }
  }, [state.isStreaming, state.connectionState]);

  // 连接状态指示器
  const renderConnectionStatus = () => {
    if (!showConnectionStatus) return null;

    const getStatusIcon = () => {
      switch (state.connectionState) {
        case 'connecting':
          return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
        case 'connected':
          return <Wifi className="h-4 w-4 text-green-500" />;
        case 'disconnected':
          return <WifiOff className="h-4 w-4 text-gray-500" />;
        case 'error':
          return <AlertCircle className="h-4 w-4 text-red-500" />;
        default:
          return null;
      }
    };

    const getStatusText = () => {
      switch (state.connectionState) {
        case 'connecting':
          return '正在连接...';
        case 'connected':
          return state.isStreaming ? '正在接收消息...' : '已连接';
        case 'disconnected':
          return '已断开连接';
        case 'error':
          return `连接错误: ${state.error}`;
        default:
          return '';
      }
    };

    return (
      <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
        {getStatusIcon()}
        <span>{getStatusText()}</span>
        {state.retryCount > 0 && (
          <span className="text-xs">
            (重试 {state.retryCount}/{streamingOpts.maxRetries})
          </span>
        )}
        {state.connectionState === 'error' && (
          <button
            onClick={retry}
            className="ml-2 p-1 hover:bg-gray-100 rounded transition-colors"
            title="重试连接"
          >
            <RotateCcw className="h-3 w-3" />
          </button>
        )}
      </div>
    );
  };

  // Markdown渲染配置
  const markdownComponents = {
    code: ({ node, inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      
      return !inline && language ? (
        <SyntaxHighlighter
          style={oneDark}
          language={language}
          PreTag="div"
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className={clsx(
          'px-1 py-0.5 bg-gray-100 text-gray-800 rounded text-sm font-mono',
          className
        )} {...props}>
          {children}
        </code>
      );
    },
    pre: ({ children }: any) => (
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto my-4">
        {children}
      </pre>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-700 my-4">
        {children}
      </blockquote>
    ),
    table: ({ children }: any) => (
      <div className="overflow-x-auto my-4">
        <table className="min-w-full border-collapse border border-gray-300">
          {children}
        </table>
      </div>
    ),
    th: ({ children }: any) => (
      <th className="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left">
        {children}
      </th>
    ),
    td: ({ children }: any) => (
      <td className="border border-gray-300 px-4 py-2">
        {children}
      </td>
    ),
  };

  return (
    <div className={clsx('streaming-message', className)}>
      {renderConnectionStatus()}
      
      <div className="prose prose-sm max-w-none">
        <ReactMarkdown components={markdownComponents}>
          {displayedContent}
        </ReactMarkdown>
        
        {/* 打字机光标 */}
        {isTyping && (
          <span className="inline-block w-2 h-4 bg-gray-400 ml-1 animate-pulse" />
        )}
        
        {/* 加载指示器 */}
        {state.isStreaming && !displayedContent && (
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>AI正在思考...</span>
          </div>
        )}
      </div>

      {/* 控制按钮 */}
      <div className="flex gap-2 mt-4">
        {state.isStreaming ? (
          <button
            onClick={stopStream}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            停止
          </button>
        ) : (
          <button
            onClick={() => startStream(request)}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            disabled={state.connectionState === 'connecting'}
          >
            {state.connectionState === 'connecting' ? '连接中...' : '重新开始'}
          </button>
        )}
        
        <button
          onClick={reset}
          className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          重置
        </button>
      </div>
    </div>
  );
};

export default StreamingMessage; 