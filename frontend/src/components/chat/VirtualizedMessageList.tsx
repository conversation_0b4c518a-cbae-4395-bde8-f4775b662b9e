import React, { useEffect, useRef, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import { VariableSizeList as List, ListChildComponentProps } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import { Bot, MessageCircle } from 'lucide-react';
import { clsx } from 'clsx';
import { MessageItem } from './MessageItem';
import type { ChatHistoryProps, ChatMessageResponse } from '@/types';

interface MessageStatus {
  status: 'sending' | 'sent' | 'delivered' | 'error';
  error?: string;
}

interface VirtualizedMessageListProps extends ChatHistoryProps {
  streamingMessageId?: string;
  messageStatuses?: Record<string, MessageStatus>;
  onMessageRetry?: (messageId: string) => void;
  onMessageCopy?: (messageId: string) => void;
  onMessageEdit?: (messageId: string) => void;
  onMessageDelete?: (messageId: string) => void;
  autoScroll?: boolean;
  showTypingIndicator?: boolean;
  emptyStateText?: string;
  emptyStateSubtext?: string;
  className?: string;
  itemHeight?: number; // 默认消息高度估算
}

interface VirtualListRef {
  scrollToBottom: (force?: boolean) => void;
  scrollToItem: (index: number) => void;
}

// 消息项高度缓存
const messageHeightCache = new Map<string, number>();

// 默认高度配置
const DEFAULT_HEIGHTS = {
  USER_MESSAGE: 80,    // 用户消息通常较短
  BOT_MESSAGE: 120,    // 机器人消息通常较长
  LOADING: 60,         // 加载指示器
  EMPTY_STATE: 400,    // 空状态
} as const;

// 消息项组件  
const MessageItemRenderer = React.memo<ListChildComponentProps<{
  messages: ChatMessageResponse[];
  messageStatuses: Record<string, MessageStatus>;
  streamingMessageId: string | undefined;
  onMessageAction: (messageId: string, action: string) => void;
}>>(({ index, style, data }) => {
  const { messages, messageStatuses, streamingMessageId, onMessageAction } = data;
  const message = messages[index];
  
  if (!message) {
    return <div style={style} />;
  }

  return (
    <div style={style}>
      <div className="px-4 py-3">
        <MessageItem
          message={message}
          isStreaming={streamingMessageId === message.id}
          messageStatus={messageStatuses[message.id] || { status: 'delivered' }}
          onRetry={() => onMessageAction(message.id, 'retry')}
          onCopy={() => onMessageAction(message.id, 'copy')}
          onEdit={() => onMessageAction(message.id, 'edit')}
          onDelete={() => onMessageAction(message.id, 'delete')}
          showActions={true}
          showAvatar={true}
          showTimestamp={true}
        />
      </div>
    </div>
  );
});

MessageItemRenderer.displayName = 'MessageItemRenderer';

export const VirtualizedMessageList = forwardRef<VirtualListRef, VirtualizedMessageListProps>(({
  messages,
  isLoading = false,
  onLoadMore,
  hasMore = false,
  streamingMessageId,
  messageStatuses = {},
  onMessageRetry,
  onMessageCopy,
  onMessageEdit,
  onMessageDelete,
  autoScroll = true,
  showTypingIndicator = true,
  emptyStateText = "开始新对话",
  emptyStateSubtext = "输入您的问题，我来帮助您找到答案",
  className,
  itemHeight = DEFAULT_HEIGHTS.BOT_MESSAGE
}, ref) => {
  const listRef = useRef<List>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isUserScrollingRef = useRef(false);
  const lastMessageCountRef = useRef(messages.length);

  // 计算消息项高度
  const getItemSize = useCallback((index: number) => {
    const message = messages[index];
    if (!message) return itemHeight;

    // 从缓存中获取高度
    const cached = messageHeightCache.get(message.id);
    if (cached) return cached;

    // 基于消息类型和内容长度估算高度
    const baseHeight = message.role === 'user' 
      ? DEFAULT_HEIGHTS.USER_MESSAGE 
      : DEFAULT_HEIGHTS.BOT_MESSAGE;
    
    // 基于内容长度调整高度
    const contentLength = message.content.length;
    const estimatedHeight = baseHeight + Math.floor(contentLength / 50) * 20;
    
    // 缓存估算高度
    messageHeightCache.set(message.id, estimatedHeight);
    
    return estimatedHeight;
  }, [messages, itemHeight]);

  // 处理消息操作
  const handleMessageAction = useCallback((messageId: string, action: string) => {
    switch (action) {
      case 'retry':
        onMessageRetry?.(messageId);
        break;
      case 'copy':
        onMessageCopy?.(messageId);
        break;
      case 'edit':
        onMessageEdit?.(messageId);
        break;
      case 'delete':
        onMessageDelete?.(messageId);
        break;
    }
  }, [onMessageRetry, onMessageCopy, onMessageEdit, onMessageDelete]);

  // 滚动到底部
  const scrollToBottom = useCallback((force = false) => {
    if (!autoScroll && !force) return;
    if (isUserScrollingRef.current && !force) return;
    if (!listRef.current || messages.length === 0) return;

    // 滚动到最后一项
    listRef.current.scrollToItem(messages.length - 1, 'end');
  }, [autoScroll, messages.length]);

  // 滚动到指定项
  const scrollToItem = useCallback((index: number) => {
    if (!listRef.current) return;
    listRef.current.scrollToItem(index, 'center');
  }, []);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    scrollToBottom,
    scrollToItem,
  }), [scrollToBottom, scrollToItem]);

  // 监听消息变化，自动滚动
  useEffect(() => {
    const currentMessageCount = messages.length;
    const hadNewMessage = currentMessageCount > lastMessageCountRef.current;
    
    if (hadNewMessage && currentMessageCount > 0) {
      const lastMessage = messages[currentMessageCount - 1];
      // 如果是用户消息或者用户没有手动滚动，则自动滚动到底部
      if (lastMessage && (lastMessage.role === 'user' || !isUserScrollingRef.current)) {
        // 延迟一帧以确保列表已更新
        requestAnimationFrame(() => {
          scrollToBottom();
        });
      }
    }
    
    lastMessageCountRef.current = currentMessageCount;
  }, [messages.length, scrollToBottom]);

  // 监听流式消息
  useEffect(() => {
    if (streamingMessageId && !isUserScrollingRef.current) {
      scrollToBottom();
    }
  }, [streamingMessageId, scrollToBottom]);

  // 滚动事件处理
  const handleScroll = useCallback(({ scrollDirection, scrollOffset, scrollUpdateWasRequested }: {
    scrollDirection: 'forward' | 'backward';
    scrollOffset: number;
    scrollUpdateWasRequested: boolean;
  }) => {
    // 如果是用户主动滚动（非程序控制）
    if (!scrollUpdateWasRequested) {
      const container = containerRef.current;
      if (!container) return;

      const { scrollHeight, clientHeight } = container;
      const isAtBottom = Math.abs(scrollHeight - scrollOffset - clientHeight) < 50;
      
      // 用户手动滚动且不在底部
      if (scrollDirection === 'backward' || !isAtBottom) {
        isUserScrollingRef.current = true;
      } else if (isAtBottom) {
        isUserScrollingRef.current = false;
      }
    }
  }, []);

  // 检查是否需要加载更多
  const isItemLoaded = useCallback((index: number) => {
    return !!messages[index];
  }, [messages]);

  // 加载更多项目
  const loadMoreItems = useCallback(() => {
    if (hasMore && onLoadMore && !isLoading) {
      return onLoadMore();
    }
    return Promise.resolve();
  }, [hasMore, onLoadMore, isLoading]);

  // 列表数据
  const listData = useMemo(() => ({
    messages,
    messageStatuses,
    streamingMessageId,
    onMessageAction: handleMessageAction,
  }), [messages, messageStatuses, streamingMessageId, handleMessageAction]);

  // 空状态渲染
  const renderEmptyState = () => (
    <div className="flex-1 flex items-center justify-center h-full">
      <div className="text-center text-gray-500 max-w-md mx-auto px-4">
        <Bot className="h-16 w-16 mx-auto mb-4 text-gray-300" />
        <h3 className="text-lg font-medium mb-2">{emptyStateText}</h3>
        <p className="text-sm text-gray-400">{emptyStateSubtext}</p>
        <div className="mt-6 space-y-2">
          <div className="text-xs text-gray-400">您可以尝试问我：</div>
          <div className="space-y-1 text-xs">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-3 py-2">🤔 "帮我总结一下这些文档的要点"</div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-3 py-2">📚 "这个概念在文档中是如何解释的？"</div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-3 py-2">💡 "基于已有资料，你有什么建议？"</div>
          </div>
        </div>
      </div>
    </div>
  );

  // 如果没有消息且不在加载中，显示空状态
  if (messages.length === 0 && !isLoading) {
    return (
      <div className={clsx('flex flex-col h-full', className)}>
        {renderEmptyState()}
      </div>
    );
  }

  const itemCount = messages.length + (hasMore ? 1 : 0);

  return (
    <div className={clsx('flex flex-col h-full relative', className)}>
      <div ref={containerRef} className="flex-1">
        <InfiniteLoader
          isItemLoaded={isItemLoaded}
          itemCount={itemCount}
          loadMoreItems={loadMoreItems}
        >
          {({ onItemsRendered, ref: infiniteRef }) => (
            <List
              ref={(listElement) => {
                if (listRef.current !== listElement) {
                  (listRef as any).current = listElement;
                }
                infiniteRef(listElement);
              }}
              height={containerRef.current?.clientHeight || 400}
              width={containerRef.current?.clientWidth || '100%'}
              itemCount={messages.length}
              itemSize={getItemSize}
              itemData={listData}
              onScroll={handleScroll}
              overscanCount={5} // 预渲染5个项目以提供更好的滚动体验
              onItemsRendered={onItemsRendered}
            >
              {MessageItemRenderer}
            </List>
          )}
        </InfiniteLoader>
      </div>

      {/* 加载指示器 */}
      {showTypingIndicator && isLoading && messages.length > 0 && (
        <div className="flex justify-center py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
            <span className="text-sm">AI正在思考...</span>
          </div>
        </div>
      )}

      {/* 滚动到底部按钮 */}
      {isUserScrollingRef.current && messages.length > 0 && (
        <div className="absolute bottom-4 right-4">
          <button
            onClick={() => scrollToBottom(true)}
            className="bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-10"
            title="滚动到底部"
          >
            <MessageCircle className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
});

VirtualizedMessageList.displayName = 'VirtualizedMessageList';

export type { VirtualListRef }; 