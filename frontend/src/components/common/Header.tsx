import React, { useState } from 'react';
import { 
  Settings, 
  Plus, 
  Sun, 
  Moon, 
  Menu, 
  X,
  FileText,
  BookOpen,
  Monitor,
  Palette,
  ChevronDown
} from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';
import ThemeSettings from './ThemeSettings';

interface HeaderProps {
  onNewSession: () => void;
  onToggleDocuments: () => void;
  onToggleSources: () => void;
  isDocumentsOpen: boolean;
  isSourcesOpen: boolean;
  onToggleMobileMenu: () => void;
  isMobileMenuOpen: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  onNewSession,
  onToggleDocuments,
  onToggleSources,
  isDocumentsOpen,
  isSourcesOpen,
  onToggleMobileMenu,
  isMobileMenuOpen
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [showThemeMenu, setShowThemeMenu] = useState(false);
  const [showThemeSettings, setShowThemeSettings] = useState(false);
  const { theme, setTheme } = useTheme();

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun size={16} />;
      case 'dark':
        return <Moon size={16} />;
      case 'system':
        return <Monitor size={16} />;
      default:
        return <Monitor size={16} />;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light':
        return '亮色';
      case 'dark':
        return '暗色';
      case 'system':
        return '跟随系统';
      default:
        return '跟随系统';
    }
  };

  const quickThemeOptions = [
    { value: 'light', label: '亮色模式', icon: <Sun size={16} /> },
    { value: 'dark', label: '暗色模式', icon: <Moon size={16} /> },
    { value: 'system', label: '跟随系统', icon: <Monitor size={16} /> }
  ];

  return (
    <>
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* 左侧：Logo 和移动端菜单按钮 */}
            <div className="flex items-center space-x-4">
              {/* 移动端菜单按钮 */}
              <button
                onClick={onToggleMobileMenu}
                className="md:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700"
                aria-label="切换菜单"
              >
                {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
              </button>

              {/* Logo */}
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                <h1 className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                  AI Knowledge
                </h1>
              </div>
            </div>

            {/* 中间：桌面端导航按钮 */}
            <div className="hidden md:flex items-center space-x-2">
              <button
                onClick={onToggleDocuments}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isDocumentsOpen
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
                }`}
              >
                <FileText className="h-4 w-4 mr-2 inline" />
                文档管理
              </button>
              
              <button
                onClick={onToggleSources}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isSourcesOpen
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
                }`}
              >
                <BookOpen className="h-4 w-4 mr-2 inline" />
                引用源文档
              </button>
            </div>

            {/* 右侧：操作按钮 */}
            <div className="flex items-center space-x-2">
              {/* 新建会话按钮 */}
              <button
                onClick={onNewSession}
                className="hidden sm:flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                新建会话
              </button>

              {/* 主题切换菜单 */}
              <div className="relative">
                <button
                  onClick={() => setShowThemeMenu(!showThemeMenu)}
                  className="flex items-center space-x-2 p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700 transition-colors"
                  aria-label="主题设置"
                >
                  {getThemeIcon()}
                  <span className="hidden sm:inline text-sm font-medium">
                    {getThemeLabel()}
                  </span>
                  <ChevronDown size={14} className={`transform transition-transform ${showThemeMenu ? 'rotate-180' : ''}`} />
                </button>

                {/* 主题快捷菜单 */}
                {showThemeMenu && (
                  <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 px-3 py-2">
                        快速切换
                      </div>
                      {quickThemeOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => {
                            setTheme(option.value as any);
                            setShowThemeMenu(false);
                          }}
                          className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors ${
                            theme === option.value
                              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                          }`}
                        >
                          <span className={theme === option.value ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}>
                            {option.icon}
                          </span>
                          <span>{option.label}</span>
                          {theme === option.value && (
                            <div className="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full" />
                          )}
                        </button>
                      ))}
                      
                      <div className="border-t border-gray-200 dark:border-gray-700 my-2" />
                      
                      <button
                        onClick={() => {
                          setShowThemeSettings(true);
                          setShowThemeMenu(false);
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <Palette size={16} className="text-gray-500 dark:text-gray-400" />
                        <span>高级设置</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* 设置按钮 */}
              <div className="relative">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700"
                  aria-label="设置"
                >
                  <Settings size={20} />
                </button>

                {/* 设置下拉菜单 */}
                {showSettings && (
                  <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                    <div className="py-2">
                      <button className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        偏好设置
                      </button>
                      <button className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        快捷键
                      </button>
                      <button 
                        onClick={() => {
                          setShowThemeSettings(true);
                          setShowSettings(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        主题设置
                      </button>
                      <button className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        关于
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* 移动端新建会话按钮 */}
              <button
                onClick={onNewSession}
                className="sm:hidden p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                aria-label="新建会话"
              >
                <Plus size={20} />
              </button>
            </div>
          </div>

          {/* 移动端导航菜单 */}
          {isMobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
              <div className="space-y-2">
                <button
                  onClick={() => {
                    onToggleDocuments();
                    onToggleMobileMenu();
                  }}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium ${
                    isDocumentsOpen
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
                  }`}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  文档管理
                </button>
                
                <button
                  onClick={() => {
                    onToggleSources();
                    onToggleMobileMenu();
                  }}
                  className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium ${
                    isSourcesOpen
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
                  }`}
                >
                  <BookOpen className="h-4 w-4 mr-2" />
                  引用源文档
                </button>

                {/* 移动端主题切换 */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400 px-3 py-1">
                    主题设置
                  </div>
                  {quickThemeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => {
                        setTheme(option.value as any);
                        onToggleMobileMenu();
                      }}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm ${
                        theme === option.value
                          ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
                      }`}
                    >
                      {option.icon}
                      <span>{option.label}</span>
                      {theme === option.value && (
                        <div className="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 点击外部关闭菜单 */}
        {(showSettings || showThemeMenu) && (
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => {
              setShowSettings(false);
              setShowThemeMenu(false);
            }}
          />
        )}
      </header>

      {/* 主题设置弹窗 */}
      <ThemeSettings
        isOpen={showThemeSettings}
        onClose={() => setShowThemeSettings(false)}
      />
    </>
  );
};

export default Header; 