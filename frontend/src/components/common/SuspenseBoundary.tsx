import React, { Suspense, ReactNode } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface SuspenseBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
  loadingText?: string;
}

export const SuspenseBoundary: React.FC<SuspenseBoundaryProps> = ({
  children,
  fallback,
  className = 'min-h-screen flex items-center justify-center',
  loadingText = '页面加载中...'
}) => {
  const defaultFallback = (
    <div className={className}>
      <LoadingSpinner size="lg" text={loadingText} />
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

export default SuspenseBoundary;