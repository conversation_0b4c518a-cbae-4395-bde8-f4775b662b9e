import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  Monitor, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>,
  <PERSON>rkles,
  RotateCcw,
  Check,
  X
} from 'lucide-react';
import { useTheme, ThemeMode, ThemePreset } from '../../hooks/useTheme';

interface ThemeSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const THEME_MODES: Array<{ value: ThemeMode; label: string; icon: React.ReactNode; description: string }> = [
  {
    value: 'light',
    label: '亮色模式',
    icon: <Sun size={16} />,
    description: '始终使用亮色主题'
  },
  {
    value: 'dark',
    label: '暗色模式',
    icon: <Moon size={16} />,
    description: '始终使用暗色主题'
  },
  {
    value: 'system',
    label: '跟随系统',
    icon: <Monitor size={16} />,
    description: '自动跟随系统设置'
  }
];

const THEME_PRESETS: Array<{ value: ThemePreset; label: string; colors: string[]; description: string }> = [
  {
    value: 'default',
    label: '默认蓝色',
    colors: ['bg-blue-500', 'bg-blue-600', 'bg-blue-700'],
    description: '经典的蓝色主题'
  },
  {
    value: 'blue',
    label: '海洋蓝',
    colors: ['bg-blue-500', 'bg-sky-500', 'bg-cyan-500'],
    description: '清新的海洋蓝色调'
  },
  {
    value: 'purple',
    label: '优雅紫',
    colors: ['bg-purple-500', 'bg-violet-500', 'bg-indigo-500'],
    description: '优雅的紫色主题'
  },
  {
    value: 'green',
    label: '自然绿',
    colors: ['bg-green-500', 'bg-emerald-500', 'bg-teal-500'],
    description: '清新的绿色主题'
  },
  {
    value: 'orange',
    label: '活力橙',
    colors: ['bg-orange-500', 'bg-amber-500', 'bg-yellow-500'],
    description: '充满活力的橙色'
  }
];

export const ThemeSettings: React.FC<ThemeSettingsProps> = ({ isOpen, onClose }) => {
  const { theme, preset, config, setTheme, setPreset, updateConfig, resetToDefault } = useTheme();
  const [tempConfig, setTempConfig] = useState(config);

  if (!isOpen) return null;

  const handleSave = () => {
    updateConfig(tempConfig);
    onClose();
  };

  const handleReset = () => {
    resetToDefault();
    setTempConfig(config);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Palette className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  主题设置
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  自定义您的界面外观和行为
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X size={20} className="text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="space-y-8">
            {/* 主题模式选择 */}
            <div>
              <h3 className="text-base font-medium text-gray-900 dark:text-white mb-4">
                主题模式
              </h3>
              <div className="grid grid-cols-3 gap-3">
                {THEME_MODES.map((mode) => (
                  <button
                    key={mode.value}
                    onClick={() => setTheme(mode.value)}
                    className={`p-4 rounded-xl border-2 transition-all hover:scale-105 ${
                      theme === mode.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className="flex flex-col items-center space-y-2">
                      <div className={`p-2 rounded-lg ${
                        theme === mode.value
                          ? 'bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-400'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                      }`}>
                        {mode.icon}
                      </div>
                      <div className="text-center">
                        <div className={`text-sm font-medium ${
                          theme === mode.value
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-gray-900 dark:text-white'
                        }`}>
                          {mode.label}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {mode.description}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* 主题预设选择 */}
            <div>
              <h3 className="text-base font-medium text-gray-900 dark:text-white mb-4">
                颜色主题
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {THEME_PRESETS.map((presetItem) => (
                  <button
                    key={presetItem.value}
                    onClick={() => setPreset(presetItem.value)}
                    className={`p-4 rounded-xl border-2 transition-all hover:scale-[1.02] ${
                      preset === presetItem.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex space-x-1">
                        {presetItem.colors.map((color, index) => (
                          <div
                            key={index}
                            className={`w-6 h-6 rounded-full ${color} ring-2 ring-white dark:ring-gray-800`}
                          />
                        ))}
                      </div>
                      <div className="flex-1 text-left">
                        <div className={`text-sm font-medium ${
                          preset === presetItem.value
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-gray-900 dark:text-white'
                        }`}>
                          {presetItem.label}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {presetItem.description}
                        </div>
                      </div>
                      {preset === presetItem.value && (
                        <Check size={16} className="text-blue-600 dark:text-blue-400" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* 高级选项 */}
            <div>
              <h3 className="text-base font-medium text-gray-900 dark:text-white mb-4">
                高级选项
              </h3>
              <div className="space-y-4">
                {/* 动画效果 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Sparkles size={18} className="text-gray-500 dark:text-gray-400" />
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        切换动画
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        主题切换时显示平滑过渡效果
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => setTempConfig(prev => ({ ...prev, animated: !prev.animated }))}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      tempConfig.animated ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        tempConfig.animated ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* 自动切换 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Clock size={18} className="text-gray-500 dark:text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          定时切换
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          根据时间自动切换深色/浅色模式
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => setTempConfig(prev => ({ ...prev, autoSwitch: !prev.autoSwitch }))}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        tempConfig.autoSwitch ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          tempConfig.autoSwitch ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  {tempConfig.autoSwitch && (
                    <div className="ml-9 pl-3 border-l border-gray-200 dark:border-gray-700">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            浅色模式开始
                          </label>
                          <input
                            type="time"
                            value={tempConfig.autoSwitchTime?.light || '07:00'}
                            onChange={(e) => setTempConfig(prev => ({
                              ...prev,
                              autoSwitchTime: {
                                ...prev.autoSwitchTime,
                                light: e.target.value,
                                dark: prev.autoSwitchTime?.dark || '19:00'
                              }
                            }))}
                            className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            深色模式开始
                          </label>
                          <input
                            type="time"
                            value={tempConfig.autoSwitchTime?.dark || '19:00'}
                            onChange={(e) => setTempConfig(prev => ({
                              ...prev,
                              autoSwitchTime: {
                                ...prev.autoSwitchTime,
                                light: prev.autoSwitchTime?.light || '07:00',
                                dark: e.target.value
                              }
                            }))}
                            className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center justify-between">
            <button
              onClick={handleReset}
              className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <RotateCcw size={16} />
              <span>重置为默认</span>
            </button>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSave}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
              >
                保存设置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeSettings; 