/**
 * 文档列表组件
 * 支持搜索、过滤、分页、排序、批量操作
 */

import React, { useState, useMemo, useCallback } from 'react';
import {
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  FileText,
  Image,
  FileIcon,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  CheckCircle2,
  Clock,
  AlertCircle,
  Loader2,
  Calendar,
  HardDrive,
} from 'lucide-react';
import { DocumentService } from '../../services/documentService';
import {
  DocumentResponse,
  DocumentListFilters,
  ProcessingStatusEnum,
} from '../../types';
import { clsx } from 'clsx';

export interface DocumentListProps {
  documents: DocumentResponse[];
  loading?: boolean;
  error?: string | null;
  pagination?: {
    page: number;
    page_size: number;
    total: number;
    has_next: boolean;
  };
  onRefresh?: () => void;
  onLoadMore?: () => void;
  onDocumentSelect?: (documentId: string) => void;
  onDocumentView?: (documentId: string) => void;
  onDocumentDownload?: (documentId: string) => void;
  onDocumentDelete?: (documentId: string) => void;
  onBatchDelete?: (documentIds: string[]) => void;
  onFiltersChange?: (filters: DocumentListFilters) => void;
  showSearch?: boolean;
  showFilters?: boolean;
  showBatchActions?: boolean;
  selectable?: boolean;
  compact?: boolean;
  className?: string;
}

interface SortConfig {
  field: 'upload_time' | 'filename' | 'file_size';
  direction: 'asc' | 'desc';
}

export const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  loading = false,
  error = null,
  pagination,
  onRefresh,
  onLoadMore,
  onDocumentSelect,
  onDocumentView,
  onDocumentDownload,
  onDocumentDelete,
  onBatchDelete,
  onFiltersChange,
  showSearch = true,
  showFilters = true,
  showBatchActions = true,
  selectable = false,
  compact = false,
  className,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<DocumentListFilters>({});
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set());
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'upload_time',
    direction: 'desc',
  });
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  // 处理搜索
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    const newFilters = { ...filters, search: query, page: 1 };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [filters, onFiltersChange]);

  // 处理过滤器变化
  const handleFilterChange = useCallback((key: keyof DocumentListFilters, value: any) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [filters, onFiltersChange]);

  // 处理排序
  const handleSort = useCallback((field: SortConfig['field']) => {
    const direction: SortConfig['direction'] = sortConfig.field === field && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    const newSortConfig = { field, direction };
    setSortConfig(newSortConfig);
    
    const newFilters: DocumentListFilters = {
      ...filters,
      sort_by: field,
      sort_order: direction,
      page: 1,
    };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [sortConfig, filters, onFiltersChange]);

  // 处理文档选择
  const handleDocumentSelect = useCallback((documentId: string, selected: boolean) => {
    const newSelected = new Set(selectedDocuments);
    if (selected) {
      newSelected.add(documentId);
    } else {
      newSelected.delete(documentId);
    }
    setSelectedDocuments(newSelected);
    onDocumentSelect?.(documentId);
  }, [selectedDocuments, onDocumentSelect]);

  // 全选/取消全选
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      const allIds = new Set(documents.map(doc => doc.id));
      setSelectedDocuments(allIds);
    } else {
      setSelectedDocuments(new Set());
    }
  }, [documents]);

  // 批量删除
  const handleBatchDelete = useCallback(() => {
    if (selectedDocuments.size > 0) {
      onBatchDelete?.(Array.from(selectedDocuments));
      setSelectedDocuments(new Set());
    }
  }, [selectedDocuments, onBatchDelete]);

  // 获取文件图标
  const getFileIcon = useCallback((doc: DocumentResponse) => {
    if (doc.file_type.startsWith('image/')) {
      return <Image className="h-5 w-5 text-blue-500" />;
    } else if (doc.file_type === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else {
      return <FileIcon className="h-5 w-5 text-gray-500" />;
    }
  }, []);

  // 获取状态指示器
  const getStatusIndicator = useCallback((doc: DocumentResponse) => {
    switch (doc.processing_status) {
      case ProcessingStatusEnum.PENDING:
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case ProcessingStatusEnum.PROCESSING:
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case ProcessingStatusEnum.COMPLETED:
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case ProcessingStatusEnum.FAILED:
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  }, []);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number): string => {
    return DocumentService.formatFileSize(bytes);
  }, []);

  // 格式化日期
  const formatDate = useCallback((dateString?: string): string => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
  }, []);

  // 过滤选项
  const statusOptions = [
    { value: ProcessingStatusEnum.PENDING, label: '等待处理', color: 'text-yellow-600' },
    { value: ProcessingStatusEnum.PROCESSING, label: '处理中', color: 'text-blue-600' },
    { value: ProcessingStatusEnum.COMPLETED, label: '已完成', color: 'text-green-600' },
    { value: ProcessingStatusEnum.FAILED, label: '处理失败', color: 'text-red-600' },
  ];

  const fileTypeOptions = useMemo(() => {
    const types = new Set(documents.map(doc => doc.file_type));
    return Array.from(types).map(type => ({
      value: type,
      label: type === 'application/pdf' ? 'PDF' :
             type.startsWith('image/') ? '图片' :
             type.startsWith('text/') ? '文本' :
             type.includes('word') ? 'Word' : type,
    }));
  }, [documents]);

  const isAllSelected = documents.length > 0 && selectedDocuments.size === documents.length;
  const isPartialSelected = selectedDocuments.size > 0 && selectedDocuments.size < documents.length;

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600 mb-4">{error}</p>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={clsx('document-list', className)}>
      {/* 搜索和过滤器 */}
      {(showSearch || showFilters) && (
        <div className="mb-6 space-y-4">
          {/* 搜索栏 */}
          {showSearch && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文档..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          {/* 过滤器 */}
          {showFilters && (
            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                className={clsx(
                  'flex items-center gap-2 px-3 py-2 text-sm border rounded-md transition-colors',
                  showFiltersPanel
                    ? 'bg-blue-50 border-blue-200 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                )}
              >
                <Filter className="h-4 w-4" />
                过滤器
                {showFiltersPanel ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>

              {onRefresh && (
                <button
                  onClick={onRefresh}
                  disabled={loading}
                  className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  <RefreshCw className={clsx('h-4 w-4', loading && 'animate-spin')} />
                  刷新
                </button>
              )}

              {showBatchActions && selectedDocuments.size > 0 && (
                <div className="flex items-center gap-2 ml-auto">
                  <span className="text-sm text-gray-600">
                    已选择 {selectedDocuments.size} 个文档
                  </span>
                  <button
                    onClick={handleBatchDelete}
                    className="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                    批量删除
                  </button>
                </div>
              )}
            </div>
          )}

          {/* 过滤器面板 */}
          {showFiltersPanel && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* 状态过滤 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    处理状态
                  </label>
                  <select
                    value={filters.status?.[0] || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value ? [e.target.value] : undefined)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">全部状态</option>
                    {statusOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 文件类型过滤 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文件类型
                  </label>
                  <select
                    value={filters.file_type?.[0] || ''}
                    onChange={(e) => handleFilterChange('file_type', e.target.value ? [e.target.value] : undefined)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="">全部类型</option>
                    {fileTypeOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 排序 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    排序方式
                  </label>
                  <select
                    value={`${sortConfig.field}_${sortConfig.direction}`}
                    onChange={(e) => {
                      const [field] = e.target.value.split('_') as [SortConfig['field'], SortConfig['direction']];
                      handleSort(field);
                    }}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                  >
                    <option value="upload_time_desc">上传时间 (新→旧)</option>
                    <option value="upload_time_asc">上传时间 (旧→新)</option>
                    <option value="filename_asc">文件名 (A→Z)</option>
                    <option value="filename_desc">文件名 (Z→A)</option>
                    <option value="file_size_desc">文件大小 (大→小)</option>
                    <option value="file_size_asc">文件大小 (小→大)</option>
                  </select>
                </div>
              </div>

              {/* 清除过滤器 */}
              <div className="flex justify-end mt-4">
                <button
                  onClick={() => {
                    setFilters({});
                    setSearchQuery('');
                    onFiltersChange?.({});
                  }}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  清除所有过滤器
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 文档列表 */}
      {loading && documents.length === 0 ? (
        <div className="text-center py-8">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">加载中...</p>
        </div>
      ) : documents.length === 0 ? (
        <div className="text-center py-8">
          <FileIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">暂无文档</p>
        </div>
      ) : (
        <div className="space-y-4">
          {/* 批量操作头部 */}
          {selectable && (
            <div className="flex items-center gap-3 pb-2 border-b border-gray-200">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = isPartialSelected;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-600">
                  {isAllSelected ? '取消全选' : '全选'}
                </span>
              </label>
            </div>
          )}

          {/* 文档项 */}
          {documents.map((document) => (
            <DocumentListItem
              key={document.id}
              document={document}
              selected={selectedDocuments.has(document.id)}
              onSelect={selectable ? (selected) => handleDocumentSelect(document.id, selected) : undefined}
              onView={() => onDocumentView?.(document.id)}
              onDownload={() => onDocumentDownload?.(document.id)}
              onDelete={() => onDocumentDelete?.(document.id)}
              getFileIcon={getFileIcon}
              getStatusIndicator={getStatusIndicator}
              formatFileSize={formatFileSize}
              formatDate={formatDate}
              compact={compact}
            />
          ))}

          {/* 加载更多 */}
          {pagination?.has_next && onLoadMore && (
            <div className="text-center pt-4">
              <button
                onClick={onLoadMore}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    加载中...
                  </div>
                ) : (
                  '加载更多'
                )}
              </button>
            </div>
          )}

          {/* 分页信息 */}
          {pagination && (
            <div className="text-center text-sm text-gray-600 pt-4 border-t">
              显示 {((pagination.page - 1) * pagination.page_size) + 1} - {Math.min(pagination.page * pagination.page_size, pagination.total)} / 共 {pagination.total} 个文档
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// 文档列表项组件
interface DocumentListItemProps {
  document: DocumentResponse;
  selected?: boolean;
  onSelect?: ((selected: boolean) => void) | undefined;
  onView?: () => void;
  onDownload?: () => void;
  onDelete?: () => void;
  getFileIcon: (doc: DocumentResponse) => React.ReactNode;
  getStatusIndicator: (doc: DocumentResponse) => React.ReactNode;
  formatFileSize: (bytes: number) => string;
  formatDate: (date?: string) => string;
  compact?: boolean;
}

const DocumentListItem: React.FC<DocumentListItemProps> = ({
  document,
  selected,
  onSelect,
  onView,
  onDownload,
  onDelete,
  getFileIcon,
  getStatusIndicator,
  formatFileSize,
  formatDate,
  compact,
}) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <div
      className={clsx(
        'flex items-center gap-3 p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors',
        selected && 'ring-2 ring-blue-500 border-blue-500',
        compact && 'p-2'
      )}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* 选择框 */}
      {onSelect && (
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </label>
      )}

      {/* 文件图标 */}
      <div className="flex-shrink-0">
        {getFileIcon(document)}
      </div>

      {/* 文档信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <p className="text-sm font-medium text-gray-900 truncate">
            {document.filename}
          </p>
          {getStatusIndicator(document)}
        </div>
        
        <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
          <span className="flex items-center gap-1">
            <HardDrive className="h-3 w-3" />
            {formatFileSize(document.file_size)}
          </span>
          
          {document.chunks_count && (
            <span>{document.chunks_count} 个片段</span>
          )}
          
          <span className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {formatDate(document.upload_time)}
          </span>
        </div>

        {document.error_message && (
          <p className="text-xs text-red-500 mt-1 truncate">
            {document.error_message}
          </p>
        )}
      </div>

      {/* 操作按钮 */}
      <div className={clsx(
        'flex items-center gap-1 transition-opacity',
        showActions ? 'opacity-100' : 'opacity-0'
      )}>
        {onView && (
          <button
            onClick={onView}
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
            title="查看文档"
          >
            <Eye className="h-4 w-4" />
          </button>
        )}
        
        {onDownload && (
          <button
            onClick={onDownload}
            className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
            title="下载文档"
          >
            <Download className="h-4 w-4" />
          </button>
        )}
        
        {onDelete && (
          <button
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="删除文档"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default DocumentList; 