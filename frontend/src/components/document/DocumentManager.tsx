/**
 * 文档管理主组件
 * 集成上传和列表功能，提供统一的文档管理界面
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  RefreshCw, 
  BarChart3, 
  Upload,
  FileText,
  AlertCircle,
  CheckCircle2,
  Clock,
  Loader2,
  X,
} from 'lucide-react';
import { DocumentUpload } from './DocumentUpload';
import { DocumentList } from './DocumentList';
import { DocumentService } from '../../services/documentService';
import {
  DocumentResponse,
  DocumentUploadResponse,
  DocumentListResponse,
  DocumentListFilters,
  DocumentStatistics,
} from '../../types';
import { clsx } from 'clsx';

export interface DocumentManagerProps {
  className?: string;
  initialView?: 'list' | 'upload';
  showStats?: boolean;
  showUpload?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number; // in seconds
  onDocumentSelect?: (document: DocumentResponse) => void;
  onDocumentView?: (documentId: string) => void;
  maxUploadSize?: number;
  maxUploadFiles?: number;
}

type ViewMode = 'list' | 'upload' | 'stats';

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  className,
  initialView = 'list',
  showStats = true,
  showUpload = true,
  autoRefresh = true,
  refreshInterval = 30,
  onDocumentSelect,
  onDocumentView,
  maxUploadSize = 10,
  maxUploadFiles = 10,
}) => {
  const [currentView, setCurrentView] = useState<ViewMode>(initialView);
  const [documents, setDocuments] = useState<DocumentResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DocumentStatistics | null>(null);
  const [filters, setFilters] = useState<DocumentListFilters>({
    page: 1,
    page_size: 20,
    sort_by: 'upload_time',
    sort_order: 'desc',
  });
  const [pagination, setPagination] = useState({
    page: 1,
    page_size: 20,
    total: 0,
    has_next: false,
  });

  // 加载文档列表
  const loadDocuments = useCallback(async (newFilters?: DocumentListFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const currentFilters = newFilters || filters;
             const params: any = {
         page: currentFilters.page || 1,
         page_size: currentFilters.page_size || 20,
       };
       
       if (currentFilters.status?.[0]) {
         params.status = currentFilters.status[0];
       }
       
       if (currentFilters.search) {
         params.search = currentFilters.search;
       }
       
       const response: DocumentListResponse = await DocumentService.getDocuments(params);

      setDocuments(response.documents);
      setPagination({
        page: response.page,
        page_size: response.page_size,
        total: response.total,
        has_next: (response.page * response.page_size) < response.total,
      });
    } catch (err) {
      console.error('Failed to load documents:', err);
      setError(err instanceof Error ? err.message : '加载文档失败');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // 加载统计信息
  const loadStats = useCallback(async () => {
    try {
      const statsData = await DocumentService.getDocumentStats();
      // 转换字段名以匹配DocumentStatistics接口
      const formattedStats: DocumentStatistics = {
        total_documents: statsData.total,
        processed_documents: statsData.processed,
        failed_documents: statsData.failed,
        processing_documents: statsData.processing,
        total_size_mb: statsData.total_size_mb,
        avg_processing_time_seconds: 0, // API未返回此字段，使用默认值
        most_common_file_types: [], // API未返回此字段，使用默认值
      };
      setStats(formattedStats);
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  }, []);

  // 刷新数据
  const handleRefresh = useCallback(() => {
    loadDocuments();
    if (showStats) {
      loadStats();
    }
  }, [loadDocuments, loadStats, showStats]);

  // 处理过滤器变化
  const handleFiltersChange = useCallback((newFilters: DocumentListFilters) => {
    setFilters(newFilters);
    loadDocuments(newFilters);
  }, [loadDocuments]);

  // 处理上传成功
  const handleUploadSuccess = useCallback((uploadedDocuments: DocumentUploadResponse[]) => {
    console.log('Documents uploaded successfully:', uploadedDocuments);
    
    // 刷新文档列表
    handleRefresh();
    
    // 切换到列表视图
    setCurrentView('list');
    
    // 显示成功提示
    // 这里可以添加toast通知
  }, [handleRefresh]);

  // 处理上传错误
  const handleUploadError = useCallback((error: string, file?: File) => {
    console.error('Upload error:', error, file);
    setError(`上传失败: ${error}`);
  }, []);

  // 处理文档删除
  const handleDocumentDelete = useCallback(async (documentId: string) => {
    if (!confirm('确定要删除这个文档吗？此操作不可撤销。')) {
      return;
    }

    try {
      await DocumentService.deleteDocument(documentId);
      handleRefresh();
    } catch (err) {
      console.error('Failed to delete document:', err);
      setError(err instanceof Error ? err.message : '删除文档失败');
    }
  }, [handleRefresh]);

  // 处理批量删除
  const handleBatchDelete = useCallback(async (documentIds: string[]) => {
    if (!confirm(`确定要删除选中的 ${documentIds.length} 个文档吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await DocumentService.batchDeleteDocuments(documentIds);
      handleRefresh();
    } catch (err) {
      console.error('Failed to batch delete documents:', err);
      setError(err instanceof Error ? err.message : '批量删除失败');
    }
  }, [handleRefresh]);

  // 处理文档下载
  const handleDocumentDownload = useCallback(async (documentId: string) => {
    try {
      const blob = await DocumentService.downloadDocument(documentId);
      const foundDocument = documents.find(doc => doc.id === documentId);
      const filename = foundDocument?.filename || 'document';
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = filename;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to download document:', err);
      setError(err instanceof Error ? err.message : '下载文档失败');
    }
  }, [documents]);

  // 处理加载更多
  const handleLoadMore = useCallback(() => {
    if (pagination.has_next) {
      const newFilters = { ...filters, page: (filters.page || 1) + 1 };
      setFilters(newFilters);
      loadDocuments(newFilters);
    }
  }, [pagination.has_next, filters, loadDocuments]);

  // 初始化加载
  useEffect(() => {
    loadDocuments();
    if (showStats) {
      loadStats();
    }
  }, []);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(handleRefresh, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
    return undefined;
  }, [autoRefresh, refreshInterval, handleRefresh]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return (
    <div className={clsx('document-manager h-full flex flex-col', className)}>
      {/* 头部导航 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900">文档管理</h1>
            
            {/* 视图切换 */}
            <div className="flex rounded-lg border border-gray-300 overflow-hidden">
              <button
                onClick={() => setCurrentView('list')}
                className={clsx(
                  'px-4 py-2 text-sm font-medium transition-colors',
                  currentView === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                )}
              >
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  文档列表
                </div>
              </button>
              
              {showUpload && (
                <button
                  onClick={() => setCurrentView('upload')}
                  className={clsx(
                    'px-4 py-2 text-sm font-medium transition-colors border-l border-gray-300',
                    currentView === 'upload'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  )}
                >
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    上传文档
                  </div>
                </button>
              )}
              
              {showStats && (
                <button
                  onClick={() => setCurrentView('stats')}
                  className={clsx(
                    'px-4 py-2 text-sm font-medium transition-colors border-l border-gray-300',
                    currentView === 'stats'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  )}
                >
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    统计信息
                  </div>
                </button>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
            >
              <RefreshCw className={clsx('h-4 w-4', loading && 'animate-spin')} />
              刷新
            </button>
          </div>
        </div>

        {/* 统计概览 */}
        {stats && currentView !== 'stats' && (
          <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-xs text-gray-600">总文档数</div>
              <div className="text-lg font-semibold text-gray-900">{stats.total_documents}</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3">
              <div className="text-xs text-green-600">已处理</div>
              <div className="text-lg font-semibold text-green-900">{stats.processed_documents}</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-3">
              <div className="text-xs text-blue-600">处理中</div>
              <div className="text-lg font-semibold text-blue-900">{stats.processing_documents}</div>
            </div>
            <div className="bg-red-50 rounded-lg p-3">
              <div className="text-xs text-red-600">处理失败</div>
              <div className="text-lg font-semibold text-red-900">{stats.failed_documents}</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-3">
              <div className="text-xs text-purple-600">总大小</div>
              <div className="text-lg font-semibold text-purple-900">
                {stats.total_size_mb.toFixed(1)} MB
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="flex-shrink-0 bg-red-50 border-b border-red-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <span className="text-red-700">{error}</span>
            </div>
            <button
              onClick={clearError}
              className="text-red-500 hover:text-red-700"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* 主内容区域 */}
      <div className="flex-1 overflow-hidden">
        {currentView === 'list' && (
          <div className="h-full overflow-auto p-6">
            <DocumentList
              documents={documents}
              loading={loading}
              error={null} // 错误已在上方显示
              pagination={pagination}
              onRefresh={handleRefresh}
              onLoadMore={handleLoadMore}
                                            onDocumentSelect={(documentId: string) => {
                 if (onDocumentSelect) {
                   const document = documents.find(doc => doc.id === documentId);
                   if (document) onDocumentSelect(document);
                 }
               }}
               onDocumentView={onDocumentView || (() => {})}
               onDocumentDownload={handleDocumentDownload}
               onDocumentDelete={handleDocumentDelete}
               onBatchDelete={handleBatchDelete}
              onFiltersChange={handleFiltersChange}
              showSearch={true}
              showFilters={true}
              showBatchActions={true}
              selectable={true}
            />
          </div>
        )}

        {currentView === 'upload' && (
          <div className="h-full overflow-auto p-6">
            <div className="max-w-4xl mx-auto">
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-2">上传文档</h2>
                <p className="text-gray-600">
                  支持 PDF、Word、文本文件和图片格式，单个文件最大 {maxUploadSize}MB，最多 {maxUploadFiles} 个文件。
                </p>
              </div>
              
              <DocumentUpload
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
                maxSize={maxUploadSize}
                maxFiles={maxUploadFiles}
                autoUpload={true}
                showPreview={true}
              />
            </div>
          </div>
        )}

        {currentView === 'stats' && (
          <div className="h-full overflow-auto p-6">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">详细统计</h2>
              
              {stats ? (
                <div className="space-y-6">
                  {/* 总体统计 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center">
                        <FileText className="h-8 w-8 text-blue-500" />
                        <div className="ml-4">
                          <div className="text-2xl font-bold text-gray-900">{stats.total_documents}</div>
                          <div className="text-sm text-gray-600">总文档数</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center">
                        <CheckCircle2 className="h-8 w-8 text-green-500" />
                        <div className="ml-4">
                          <div className="text-2xl font-bold text-gray-900">{stats.processed_documents}</div>
                          <div className="text-sm text-gray-600">已处理</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center">
                        <Clock className="h-8 w-8 text-yellow-500" />
                        <div className="ml-4">
                          <div className="text-2xl font-bold text-gray-900">{stats.processing_documents}</div>
                          <div className="text-sm text-gray-600">处理中</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center">
                        <AlertCircle className="h-8 w-8 text-red-500" />
                        <div className="ml-4">
                          <div className="text-2xl font-bold text-gray-900">{stats.failed_documents}</div>
                          <div className="text-sm text-gray-600">处理失败</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 文件类型分布 */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">文件类型分布</h3>
                    <div className="space-y-3">
                      {stats.most_common_file_types?.map((type, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-4 h-4 bg-blue-500 rounded" style={{
                              backgroundColor: `hsl(${(index * 360) / stats.most_common_file_types.length}, 70%, 50%)`
                            }}></div>
                            <span className="text-sm text-gray-900">{type.type}</span>
                          </div>
                          <span className="text-sm font-medium text-gray-600">{type.count} 个文件</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 其他统计信息 */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">存储信息</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">总存储大小</span>
                          <span className="text-sm font-medium text-gray-900">{stats.total_size_mb.toFixed(2)} MB</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">平均文件大小</span>
                          <span className="text-sm font-medium text-gray-900">
                            {stats.total_documents > 0 ? (stats.total_size_mb / stats.total_documents).toFixed(2) : 0} MB
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">处理性能</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">平均处理时间</span>
                          <span className="text-sm font-medium text-gray-900">
                            {stats.avg_processing_time_seconds?.toFixed(1) || 0} 秒
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">处理成功率</span>
                          <span className="text-sm font-medium text-gray-900">
                            {stats.total_documents > 0 
                              ? ((stats.processed_documents / stats.total_documents) * 100).toFixed(1)
                              : 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
                  <p className="text-gray-600">加载统计信息中...</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentManager; 