/**
 * 文档上传组件
 * 支持拖拽上传、文件验证、进度显示、批量上传
 */

import React, { useState, useCallback, useRef } from 'react';
import { 
  Upload, 
  X, 
  AlertCircle, 
  CheckCircle2, 
  Loader2,
  FileText,
  Image,
  FileIcon,
  Trash2,
} from 'lucide-react';
import { DocumentService } from '../../services/documentService';
import { 
  DocumentUploadResponse, 
  FileUploadProgress, 
  DragDropFile, 
  DragDropState,
  FileValidationRule,
  FileValidationResult 
} from '../../types';
import { clsx } from 'clsx';

export interface DocumentUploadProps {
  onUploadSuccess?: (documents: DocumentUploadResponse[]) => void;
  onUploadError?: (error: string, file?: File) => void;
  onUploadProgress?: (fileIndex: number, progress: number) => void;
  accept?: string;
  maxSize?: number; // in MB
  maxFiles?: number;
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  autoUpload?: boolean;
  showPreview?: boolean;
}

// const DEFAULT_VALIDATION: FileValidationRule = {
//   maxSize: 10, // 10MB
//   maxFiles: 10,
//   allowedTypes: [
//     'application/pdf',
//     'application/msword',
//     'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
//     'text/plain',
//     'text/markdown',
//     'image/jpeg',
//     'image/png',
//     'image/gif'
//   ]
// };

export const DocumentUpload: React.FC<DocumentUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  onUploadProgress,
  accept = 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,text/markdown,image/*',
  maxSize = 10,
  maxFiles = 10,
  multiple = true,
  disabled = false,
  className,
  autoUpload = true,
  showPreview = true,
}) => {
  const [dragState, setDragState] = useState<DragDropState>({
    isDragOver: false,
    files: [],
    invalidFiles: [],
  });
  
  const [uploadProgress, setUploadProgress] = useState<Record<string, FileUploadProgress>>({});
  const [isUploading, setIsUploading] = useState(false);
  const [, setUploadResults] = useState<(DocumentUploadResponse | Error)[]>([]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragCounterRef = useRef(0);

  // 测试API连接
  const testApiConnection = async () => {
    console.log('🧪 Testing API connection...');
    try {
      const response = await fetch('/api/v1/health');
      console.log('✅ Health check response:', response.status);
      
      // 测试聊天API
      const chatResponse = await fetch('/api/v1/chat/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: 'test', stream: false })
      });
      console.log('✅ Chat API response:', chatResponse.status);
      
      // 测试文档上传API的可访问性
      const uploadResponse = await fetch('/api/v1/documents/upload', {
        method: 'POST',
        body: new FormData() // 空的FormData，会返回错误但证明端点可访问
      });
      console.log('✅ Upload API response:', uploadResponse.status);
      
    } catch (error) {
      console.error('❌ API test failed:', error);
    }
  };

  // 文件验证
  const validateFile = useCallback((file: File): FileValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查文件大小
    if (file.size > maxSize * 1024 * 1024) {
      errors.push(`文件大小超过限制 (${maxSize}MB)`);
    }

    // 检查文件类型
    const allowedTypes = accept.split(',').map(type => type.trim());
    const isValidType = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.replace('/*', '/'));
      }
      return file.type === type;
    });

    if (!isValidType) {
      errors.push('不支持的文件类型');
    }

    // 检查文件是否为空
    if (file.size === 0) {
      errors.push('文件不能为空');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }, [accept, maxSize]);

  // 创建文件对象
  const createDragDropFile = useCallback((file: File): DragDropFile => {
    const validation = validateFile(file);
    const id = `${file.name}_${file.size}_${Date.now()}`;
    
    const dragDropFile: DragDropFile = {
      file,
      id,
      isValid: validation.isValid,
      errors: validation.errors,
    };

    // 只在需要预览且是图片时设置preview
    if (showPreview && file.type.startsWith('image/')) {
      dragDropFile.preview = URL.createObjectURL(file);
    }
    
    return dragDropFile;
  }, [validateFile, showPreview]);

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList | File[]) => {
    console.log('📂 handleFileSelect called with files:', files);
    
    const fileArray = Array.from(files);
    console.log('📋 fileArray:', fileArray);
    
    const totalFiles = dragState.files.length + fileArray.length;

    if (totalFiles > maxFiles) {
      console.log('❌ Too many files:', totalFiles, 'max:', maxFiles);
      onUploadError?.(
        `最多只能选择 ${maxFiles} 个文件，当前选择了 ${totalFiles} 个文件`
      );
      return;
    }

    const newFiles = fileArray.map(createDragDropFile);
    const validFiles = newFiles.filter(f => f.isValid);
    const invalidFiles = newFiles.filter(f => !f.isValid);

    console.log('✅ Valid files:', validFiles);
    console.log('❌ Invalid files:', invalidFiles);

    setDragState(prev => ({
      ...prev,
      files: [...prev.files, ...validFiles],
      invalidFiles: [...prev.invalidFiles, ...invalidFiles],
    }));

    // 自动上传将通过useEffect处理
    if (autoUpload && validFiles.length > 0) {
      console.log('🚀 Will auto-upload files:', validFiles.map(f => f.file));
      // 触发上传的逻辑将在useEffect中处理
      setTimeout(() => {
        handleUpload(validFiles.map(f => f.file));
      }, 0);
    }
  }, [dragState.files.length, maxFiles, createDragDropFile, autoUpload, onUploadError]);

  // 拖拽事件处理
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current++;
    
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setDragState(prev => ({ ...prev, isDragOver: true }));
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current--;
    
    if (dragCounterRef.current === 0) {
      setDragState(prev => ({ ...prev, isDragOver: false }));
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current = 0;
    
    setDragState(prev => ({ ...prev, isDragOver: false }));
    
    if (disabled) return;
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  // 文件输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('📂 File input changed:', e.target.files);
    const files = e.target.files;
    if (files && files.length > 0) {
      console.log('✅ Files found, calling handleFileSelect');
      handleFileSelect(files);
    } else {
      console.log('❌ No files selected');
    }
    // 清空input value，允许重复选择同一文件
    e.target.value = '';
  }, [handleFileSelect]);

  // 上传文件
  const handleUpload = useCallback(async (files?: File[]) => {
    console.log('🚀 handleUpload called with files:', files);
    
    const filesToUpload = files || dragState.files.map(f => f.file);
    console.log('📁 filesToUpload:', filesToUpload);
    
    if (filesToUpload.length === 0) {
      console.log('❌ No files to upload');
      onUploadError?.('没有可上传的文件');
      return;
    }

    console.log('⏳ Starting upload process...');
    setIsUploading(true);
    setUploadResults([]);

    try {
      console.log('📤 Calling DocumentService.uploadDocuments...');
      const results = await DocumentService.uploadDocuments(
        filesToUpload,
        (fileIndex, progress) => {
          console.log(`📊 Upload progress for file ${fileIndex}:`, progress);
          const currentFile = filesToUpload[fileIndex];
          if (!currentFile) return;
          
          const fileId = dragState.files[fileIndex]?.id || `file_${fileIndex}`;
          setUploadProgress(prev => ({
            ...prev,
            [fileId]: {
              file: currentFile,
              progress,
              status: 'uploading',
            },
          }));
          onUploadProgress?.(fileIndex, progress);
        },
        (fileIndex, result) => {
          console.log(`✅ Upload complete for file ${fileIndex}:`, result);
          const currentFile = filesToUpload[fileIndex];
          if (!currentFile) return;
          
          const fileId = dragState.files[fileIndex]?.id || `file_${fileIndex}`;
          if (result instanceof Error) {
            setUploadProgress(prev => ({
              ...prev,
              [fileId]: {
                file: currentFile,
                progress: 0,
                status: 'error',
                error: result.message,
              },
            }));
            onUploadError?.(result.message, currentFile);
          } else {
            setUploadProgress(prev => ({
              ...prev,
              [fileId]: {
                file: currentFile,
                progress: 100,
                status: 'success',
                document_id: result.document_id,
              },
            }));
          }
        }
      );

      console.log('🎉 Upload results:', results);
      setUploadResults(results);
      const successResults = results.filter(r => !(r instanceof Error)) as DocumentUploadResponse[];
      
      if (successResults.length > 0) {
        console.log('✅ Upload successful:', successResults);
        onUploadSuccess?.(successResults);
        // 清空成功上传的文件
        setDragState(prev => ({
          ...prev,
          files: prev.files.filter((_, index) => results[index] instanceof Error),
        }));
      }
    } catch (error) {
      console.error('💥 Upload error:', error);
      onUploadError?.(error instanceof Error ? error.message : '上传失败');
    } finally {
      setIsUploading(false);
    }
  }, [dragState.files, onUploadSuccess, onUploadError, onUploadProgress]);

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    setDragState(prev => ({
      ...prev,
      files: prev.files.filter(f => f.id !== fileId),
      invalidFiles: prev.invalidFiles.filter(f => f.id !== fileId),
    }));
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[fileId];
      return newProgress;
    });
  }, []);

  // 清空所有文件
  const clearAllFiles = useCallback(() => {
    setDragState({
      isDragOver: false,
      files: [],
      invalidFiles: [],
    });
    setUploadProgress({});
    setUploadResults([]);
  }, []);

  // 获取文件图标
  const getFileIcon = useCallback((file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-5 w-5" />;
    } else if (file.type === 'application/pdf') {
      return <FileText className="h-5 w-5" />;
    } else {
      return <FileIcon className="h-5 w-5" />;
    }
  }, []);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number): string => {
    return DocumentService.formatFileSize(bytes);
  }, []);

  return (
    <div className={clsx('document-upload', className)}>
      {/* API连接测试按钮 */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm text-blue-700 mb-2">
          如果上传功能不工作，请先测试API连接：
        </p>
        <button
          onClick={testApiConnection}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
        >
          测试API连接
        </button>
      </div>

      {/* 拖拽上传区域 */}
      <div
        className={clsx(
          'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
          {
            'border-blue-300 bg-blue-50': dragState.isDragOver,
            'border-gray-300 bg-gray-50': !dragState.isDragOver && !disabled,
            'border-gray-200 bg-gray-100 cursor-not-allowed': disabled,
            'hover:border-gray-400 hover:bg-gray-100': !disabled && !dragState.isDragOver,
          }
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => {
          console.log('🖱️ Upload area clicked');
          if (!disabled) {
            console.log('📁 Triggering file input click');
            fileInputRef.current?.click();
          } else {
            console.log('❌ Upload is disabled');
          }
        }}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />
        
        <div className="flex flex-col items-center gap-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <Upload className="h-8 w-8 text-blue-600" />
          </div>
          
          <div>
            <p className="text-lg font-medium text-gray-900">
              拖拽文件到此处上传
            </p>
            <p className="text-sm text-gray-500 mt-1">
              或点击选择文件
            </p>
          </div>
          
          <div className="text-xs text-gray-400">
            <p>支持的格式: PDF, Word, 文本文件, 图片</p>
            <p>单文件最大: {maxSize}MB，最多 {maxFiles} 个文件</p>
          </div>
        </div>
      </div>

      {/* 文件列表 */}
      {(dragState.files.length > 0 || dragState.invalidFiles.length > 0) && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              已选择文件 ({dragState.files.length + dragState.invalidFiles.length})
            </h3>
            <div className="flex gap-2">
              {!autoUpload && dragState.files.length > 0 && (
                <button
                  onClick={() => handleUpload()}
                  disabled={isUploading}
                  className={clsx(
                    'px-4 py-2 text-sm font-medium rounded-md transition-colors',
                    isUploading
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  )}
                >
                  {isUploading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      上传中...
                    </div>
                  ) : (
                    '开始上传'
                  )}
                </button>
              )}
              
              <button
                onClick={clearAllFiles}
                className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
              >
                <div className="flex items-center gap-2">
                  <Trash2 className="h-4 w-4" />
                  清空
                </div>
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {/* 有效文件 */}
            {dragState.files.map((fileItem) => {
              const progress = uploadProgress[fileItem.id];
              return (
                <div
                  key={fileItem.id}
                  className="flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-lg"
                >
                  {fileItem.preview ? (
                    <img
                      src={fileItem.preview}
                      alt={fileItem.file.name}
                      className="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                      {getFileIcon(fileItem.file)}
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {fileItem.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(fileItem.file.size)}
                    </p>
                    
                    {/* 进度条 */}
                    {progress && (
                      <div className="mt-2">
                        <div className="flex items-center gap-2 text-xs">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className={clsx(
                                'h-2 rounded-full transition-all duration-300',
                                {
                                  'bg-blue-500': progress.status === 'uploading',
                                  'bg-green-500': progress.status === 'success',
                                  'bg-red-500': progress.status === 'error',
                                }
                              )}
                              style={{ width: `${progress.progress}%` }}
                            />
                          </div>
                          <span className="text-gray-500">
                            {progress.progress}%
                          </span>
                        </div>
                        
                        {progress.error && (
                          <p className="text-xs text-red-500 mt-1">
                            {progress.error}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {progress?.status === 'success' && (
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                    )}
                    {progress?.status === 'error' && (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    {progress?.status === 'uploading' && (
                      <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                    )}
                    
                    <button
                      onClick={() => removeFile(fileItem.id)}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                      title="移除文件"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              );
            })}

            {/* 无效文件 */}
            {dragState.invalidFiles.map((fileItem) => (
              <div
                key={fileItem.id}
                className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg"
              >
                <div className="w-10 h-10 bg-red-100 rounded flex items-center justify-center">
                  <AlertCircle className="h-5 w-5 text-red-500" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {fileItem.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(fileItem.file.size)}
                  </p>
                  <div className="mt-1">
                    {fileItem.errors.map((error, index) => (
                      <p key={index} className="text-xs text-red-500">
                        {error}
                      </p>
                    ))}
                  </div>
                </div>
                
                <button
                  onClick={() => removeFile(fileItem.id)}
                  className="p-1 text-red-400 hover:text-red-600 transition-colors"
                  title="移除文件"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentUpload; 