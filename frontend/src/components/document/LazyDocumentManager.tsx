import React, { Suspense } from 'react';
import LoadingSpinner from '../common/LoadingSpinner';
import { createLazyComponent } from '../../utils/preload';

// 懒加载文档管理器组件
const DocumentManagerComponent = createLazyComponent(
  'documentManager',
  () => import('./DocumentManager')
);

interface LazyDocumentManagerProps {
  className?: string;
  onDocumentSelect?: (doc: any) => void;
  onDocumentView?: (docId: string) => void;
}

export const LazyDocumentManager: React.FC<LazyDocumentManagerProps> = (props) => {
  return (
    <Suspense
      fallback={
        <div className="h-full flex items-center justify-center">
          <LoadingSpinner size="md" text="文档管理器加载中..." />
        </div>
      }
    >
      <DocumentManagerComponent {...props} />
    </Suspense>
  );
};

export default LazyDocumentManager;