/**
 * 组件库统一导出
 */

// 通用组件
export * from './common';

// 聊天组件
export * from './chat';

// 文档组件
export * from './document';

// 源引用组件
export * from './sources';

// 加载组件（排除LoadingSpinner以避免冲突）
export {
  Skeleton,
  SkeletonText,
  SkeletonAvatar,
  SkeletonButton,
  SkeletonCard,
  SkeletonList,
  SkeletonTable,
  LoadingButton,
  LoadingButtonPrimary,
  LoadingButtonSecondary,
  LoadingButtonOutline,
  LoadingButtonDanger,
  LoadingButtonSuccess,
  PageLoader,
  PageLoaderFullscreen,
  PageLoaderCard,
  PageLoaderProgress,
  PageLoaderSkeleton,
  LazyLoader,
  LazyImage,
  LazyComponent,
  LazyIntersectionLoader,
  type SkeletonProps,
  type LoadingButtonProps,
  type PageLoaderProps,
  type LazyLoaderProps,
} from './loading';

// 通知组件
export * from './toast';

// 动画组件
export * from './animation';

// 移动端组件
export * from './mobile';

// 页面组件
// export { default as Layout } from './Layout';
// export { default as HomePage } from './HomePage';
// export { default as ChatPage } from './ChatPage';
// export { default as DocumentsPage } from './DocumentsPage';