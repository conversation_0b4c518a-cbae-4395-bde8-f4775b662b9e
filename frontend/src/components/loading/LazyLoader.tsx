/**
 * LazyLoader 懒加载占位符组件
 * 用于懒加载内容的占位符和加载状态管理
 */

import React, { useState, useEffect, useRef } from 'react';
import { clsx } from 'clsx';
import { LoadingSpinner } from './LoadingSpinner';
import { Skeleton } from './Skeleton';

export interface LazyLoaderProps {
  /** 子元素 */
  children: React.ReactNode;
  /** 加载状态 */
  loading?: boolean;
  /** 错误状态 */
  error?: boolean;
  /** 占位符类型 */
  placeholder?: 'spinner' | 'skeleton' | 'custom';
  /** 占位符高度 */
  height?: string | number;
  /** 占位符宽度 */
  width?: string | number;
  /** 自定义占位符 */
  customPlaceholder?: React.ReactNode;
  /** 错误时显示的内容 */
  errorContent?: React.ReactNode;
  /** 加载文本 */
  loadingText?: string;
  /** 错误文本 */
  errorText?: string;
  /** 重试函数 */
  onRetry?: () => void;
  /** 自定义类名 */
  className?: string;
  /** 是否启用交集观察器 */
  enableIntersectionObserver?: boolean;
  /** 交集观察器选项 */
  intersectionOptions?: IntersectionObserverInit;
  /** 进入视口时触发 */
  onIntersect?: () => void;
  /** 是否只触发一次 */
  triggerOnce?: boolean;
  /** 根边距 */
  rootMargin?: string;
  /** 触发阈值 */
  threshold?: number;
}

export const LazyLoader: React.FC<LazyLoaderProps> = ({
  children,
  loading = false,
  error = false,
  placeholder = 'skeleton',
  height = 'auto',
  width = 'auto',
  customPlaceholder,
  errorContent,
  loadingText = '加载中...',
  errorText = '加载失败',
  onRetry,
  className,
  enableIntersectionObserver = false,
  intersectionOptions = {},
  onIntersect,
  triggerOnce = true,
  rootMargin = '100px',
  threshold = 0.1,
}) => {
  const [isIntersecting, setIsIntersecting] = useState(!enableIntersectionObserver);
  const [hasIntersected, setHasIntersected] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!enableIntersectionObserver || !containerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          setHasIntersected(true);
          onIntersect?.();
          
          if (triggerOnce) {
            observer.disconnect();
          }
        } else if (!triggerOnce) {
          setIsIntersecting(false);
        }
      },
      {
        rootMargin,
        threshold,
        ...intersectionOptions,
      }
    );

    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, [enableIntersectionObserver, rootMargin, threshold, triggerOnce, onIntersect, intersectionOptions]);

  const containerStyle: React.CSSProperties = {
    height: typeof height === 'number' ? `${height}px` : height,
    width: typeof width === 'number' ? `${width}px` : width,
  };

  const renderPlaceholder = () => {
    if (customPlaceholder) {
      return customPlaceholder;
    }

    switch (placeholder) {
      case 'spinner':
        return (
          <div className="flex items-center justify-center h-full">
            <LoadingSpinner size="md" showText text={loadingText} textPosition="bottom" />
          </div>
        );

      case 'skeleton':
        return (
          <div className="space-y-3">
            <Skeleton variant="rect" height="20px" />
            <Skeleton variant="rect" height="20px" width="80%" />
            <Skeleton variant="rect" height="20px" width="60%" />
          </div>
        );

      default:
        return null;
    }
  };

  const renderError = () => {
    if (errorContent) {
      return errorContent;
    }

    return (
      <div className="flex flex-col items-center justify-center h-full space-y-4 text-center">
        <div className="text-error-500 text-4xl">⚠️</div>
        <div className="text-gray-600 dark:text-gray-400">
          <p className="text-sm">{errorText}</p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-2 px-4 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              重试
            </button>
          )}
        </div>
      </div>
    );
  };

  const containerClasses = clsx(
    'lazy-loader',
    {
      'min-h-20': height === 'auto',
    },
    className
  );

  // 如果启用了交集观察器但还没有交集，显示占位符
  if (enableIntersectionObserver && !isIntersecting && !hasIntersected) {
    return (
      <div
        ref={containerRef}
        className={containerClasses}
        style={containerStyle}
      >
        {renderPlaceholder()}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={containerClasses}
      style={containerStyle}
    >
      {error ? renderError() : loading ? renderPlaceholder() : children}
    </div>
  );
};

// 预设的懒加载组件
export const LazyImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  width?: string | number;
  height?: string | number;
  onLoad?: () => void;
  onError?: () => void;
}> = ({ src, alt, className, width, height, onLoad, onError }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const handleLoad = () => {
    setLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setLoading(false);
    setError(true);
    onError?.();
  };

  const handleRetry = () => {
    setLoading(true);
    setError(false);
  };

  return (
    <LazyLoader
      loading={loading}
      error={error}
      placeholder="skeleton"
      width={width}
      height={height}
      onRetry={handleRetry}
      className={className}
    >
      <img
        src={src}
        alt={alt}
        className={className}
        onLoad={handleLoad}
        onError={handleError}
      />
    </LazyLoader>
  );
};

export const LazyComponent: React.FC<{
  children: React.ReactNode;
  delay?: number;
  className?: string;
}> = ({ children, delay = 0, className }) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <LazyLoader
      loading={loading}
      placeholder="skeleton"
      className={className}
    >
      {children}
    </LazyLoader>
  );
};

export const LazyIntersectionLoader: React.FC<{
  children: React.ReactNode;
  onIntersect: () => void;
  className?: string;
  height?: string | number;
  triggerOnce?: boolean;
}> = ({ children, onIntersect, className, height = 200, triggerOnce = true }) => {
  const [loaded, setLoaded] = useState(false);

  const handleIntersect = () => {
    setLoaded(true);
    onIntersect();
  };

  return (
    <LazyLoader
      loading={!loaded}
      enableIntersectionObserver
      onIntersect={handleIntersect}
      triggerOnce={triggerOnce}
      height={height}
      className={className}
    >
      {children}
    </LazyLoader>
  );
};