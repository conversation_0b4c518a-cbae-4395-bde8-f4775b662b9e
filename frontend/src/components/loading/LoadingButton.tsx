/**
 * LoadingButton 加载按钮组件
 * 支持加载状态的按钮组件
 */

import React from 'react';
import { clsx } from 'clsx';
import { LoadingSpinner } from './LoadingSpinner';

export interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 是否处于加载状态 */
  loading?: boolean;
  /** 加载时显示的文本 */
  loadingText?: string;
  /** 按钮尺寸 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** 按钮样式变体 */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  /** 图标 */
  icon?: React.ReactNode;
  /** 图标位置 */
  iconPosition?: 'left' | 'right';
  /** 是否为块级按钮 */
  block?: boolean;
  /** 是否为圆形按钮 */
  round?: boolean;
  /** 子元素 */
  children?: React.ReactNode;
}

const sizeClasses = {
  xs: 'btn-xs',
  sm: 'btn-sm',
  md: 'btn-md',
  lg: 'btn-lg',
  xl: 'btn-xl',
};

const variantClasses = {
  primary: 'btn-primary',
  secondary: 'btn-secondary',
  outline: 'btn-outline',
  ghost: 'btn-ghost',
  danger: 'btn-danger',
  success: 'btn-success',
};

const spinnerSizeMap = {
  xs: 'xs' as const,
  sm: 'xs' as const,
  md: 'sm' as const,
  lg: 'sm' as const,
  xl: 'md' as const,
};

const spinnerVariantMap = {
  primary: 'white' as const,
  secondary: 'primary' as const,
  outline: 'primary' as const,
  ghost: 'primary' as const,
  danger: 'white' as const,
  success: 'white' as const,
};

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading = false,
  loadingText,
  size = 'md',
  variant = 'primary',
  icon,
  iconPosition = 'left',
  block = false,
  round = false,
  disabled,
  className,
  children,
  ...props
}) => {
  const buttonClasses = clsx(
    'btn',
    sizeClasses[size],
    variantClasses[variant],
    {
      'w-full': block,
      'rounded-full': round,
      'cursor-not-allowed': loading || disabled,
    },
    className
  );

  const spinnerSize = spinnerSizeMap[size];
  const spinnerVariant = spinnerVariantMap[variant];

  const renderContent = () => {
    if (loading) {
      return (
        <>
          <LoadingSpinner size={spinnerSize} variant={spinnerVariant} />
          {loadingText && <span className="ml-2">{loadingText}</span>}
        </>
      );
    }

    return (
      <>
        {icon && iconPosition === 'left' && (
          <span className="mr-2">{icon}</span>
        )}
        {children}
        {icon && iconPosition === 'right' && (
          <span className="ml-2">{icon}</span>
        )}
      </>
    );
  };

  return (
    <button
      className={buttonClasses}
      disabled={loading || disabled}
      {...props}
    >
      {renderContent()}
    </button>
  );
};

// 预设的加载按钮组件
export const LoadingButtonPrimary: React.FC<LoadingButtonProps> = (props) => (
  <LoadingButton variant="primary" {...props} />
);

export const LoadingButtonSecondary: React.FC<LoadingButtonProps> = (props) => (
  <LoadingButton variant="secondary" {...props} />
);

export const LoadingButtonOutline: React.FC<LoadingButtonProps> = (props) => (
  <LoadingButton variant="outline" {...props} />
);

export const LoadingButtonDanger: React.FC<LoadingButtonProps> = (props) => (
  <LoadingButton variant="danger" {...props} />
);

export const LoadingButtonSuccess: React.FC<LoadingButtonProps> = (props) => (
  <LoadingButton variant="success" {...props} />
);