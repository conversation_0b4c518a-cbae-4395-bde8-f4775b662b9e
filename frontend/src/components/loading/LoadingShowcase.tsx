/**
 * LoadingShowcase 加载组件展示页面
 * 用于展示和测试所有加载状态组件
 */

import React, { useState, useEffect } from 'react';
import { clsx } from 'clsx';
import {
  LoadingSpinner,
  LoadingSpinnerSmall,
  LoadingSpinnerLarge,
  LoadingButton,
  Skeleton,
  SkeletonText,
  SkeletonAvatar,
  SkeletonCard,
  SkeletonList,
  PageLoader,
  PageLoaderProgress,
  LazyLoader,
  LazyImage,
  LazyComponent,
} from './index';

export const LoadingShowcase: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showPageLoader, setShowPageLoader] = useState(false);

  // 模拟进度条
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            setLoading(false);
            return 0;
          }
          return prev + 10;
        });
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);

  const handleLoadingTest = () => {
    setLoading(true);
    setProgress(0);
  };

  const handlePageLoaderTest = () => {
    setShowPageLoader(true);
    setTimeout(() => setShowPageLoader(false), 3000);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            加载组件展示
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            各种加载状态组件的展示和测试
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Loading Spinner */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Loading Spinner</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <LoadingSpinnerSmall />
                <span className="text-sm">小尺寸</span>
              </div>
              <div className="flex items-center space-x-4">
                <LoadingSpinner size="md" />
                <span className="text-sm">中等尺寸</span>
              </div>
              <div className="flex items-center space-x-4">
                <LoadingSpinnerLarge />
                <span className="text-sm">大尺寸</span>
              </div>
              <div className="flex items-center space-x-4">
                <LoadingSpinner variant="success" />
                <span className="text-sm">成功色</span>
              </div>
              <div className="flex items-center space-x-4">
                <LoadingSpinner variant="error" />
                <span className="text-sm">错误色</span>
              </div>
            </div>
          </div>

          {/* Loading Button */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Loading Button</h2>
            <div className="space-y-4">
              <LoadingButton
                loading={loading}
                loadingText="处理中"
                onClick={handleLoadingTest}
              >
                点击测试
              </LoadingButton>
              <LoadingButton
                variant="secondary"
                loading={loading}
                size="sm"
              >
                次要按钮
              </LoadingButton>
              <LoadingButton
                variant="outline"
                loading={loading}
                size="lg"
              >
                大按钮
              </LoadingButton>
            </div>
          </div>

          {/* Skeleton */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Skeleton 骨架屏</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">头像骨架</h3>
                <SkeletonAvatar size="lg" />
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">文本骨架</h3>
                <SkeletonText lines={3} />
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">基本形状</h3>
                <div className="flex space-x-4">
                  <Skeleton width="100px" height="20px" />
                  <Skeleton variant="circle" />
                  <Skeleton width="80px" height="32px" rounded="lg" />
                </div>
              </div>
            </div>
          </div>

          {/* Skeleton Components */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Skeleton 组件</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-2">卡片骨架</h3>
                <SkeletonCard />
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2">列表骨架</h3>
                <SkeletonList count={3} />
              </div>
            </div>
          </div>

          {/* Page Loader */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Page Loader</h2>
            <div className="space-y-4">
              <button
                onClick={handlePageLoaderTest}
                className="btn btn-primary"
              >
                测试页面加载器
              </button>
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 h-32 relative">
                <PageLoader
                  show={loading}
                  type="spinner"
                  text="加载中..."
                  backdrop={false}
                />
                {!loading && (
                  <div className="text-center text-gray-500 dark:text-gray-400">
                    内容区域
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Progress Loader */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Progress Loader</h2>
            <div className="space-y-4">
              <PageLoaderProgress
                progress={progress}
                text="上传中"
                description="正在处理您的文件..."
                show={loading}
              />
              {!loading && (
                <div className="text-center text-gray-500 dark:text-gray-400">
                  进度条示例
                </div>
              )}
            </div>
          </div>

          {/* Lazy Loader */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Lazy Loader</h2>
            <div className="space-y-4">
              <LazyComponent delay={1000}>
                <div className="p-4 bg-primary-50 dark:bg-primary-900 rounded-lg">
                  <p className="text-primary-700 dark:text-primary-300">
                    这是一个延迟加载的组件
                  </p>
                </div>
              </LazyComponent>
              <LazyLoader
                loading={false}
                error={false}
                placeholder="skeleton"
                height={100}
              >
                <div className="p-4 bg-success-50 dark:bg-success-900 rounded-lg">
                  <p className="text-success-700 dark:text-success-300">
                    加载完成的内容
                  </p>
                </div>
              </LazyLoader>
            </div>
          </div>

          {/* Error State */}
          <div className="card p-6">
            <h2 className="text-xl font-semibold mb-4">Error State</h2>
            <div className="space-y-4">
              <LazyLoader
                loading={false}
                error={true}
                errorText="加载失败"
                onRetry={() => console.log('重试')}
                height={100}
              >
                <div>这里不会显示</div>
              </LazyLoader>
            </div>
          </div>
        </div>
      </div>

      {/* 全屏页面加载器 */}
      {showPageLoader && (
        <PageLoader
          fullscreen
          type="spinner"
          text="加载中..."
          description="正在初始化应用..."
        />
      )}
    </div>
  );
};