/**
 * LoadingSpinner 组件
 * 可配置的加载动画组件，支持多种样式和尺寸
 */

import React from 'react';
import { clsx } from 'clsx';
import { Loader2 } from 'lucide-react';

export interface LoadingSpinnerProps {
  /** 尺寸大小 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** 颜色主题 */
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'white';
  /** 加载文本 */
  text?: string;
  /** 是否显示文本 */
  showText?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 文本位置 */
  textPosition?: 'bottom' | 'right';
  /** 动画类型 */
  animation?: 'spin' | 'pulse' | 'bounce';
  /** 是否居中显示 */
  centered?: boolean;
}

const sizeClasses = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12',
};

const variantClasses = {
  primary: 'text-primary-600 dark:text-primary-400',
  secondary: 'text-secondary-600 dark:text-secondary-400',
  success: 'text-success-600 dark:text-success-400',
  warning: 'text-warning-600 dark:text-warning-400',
  error: 'text-error-600 dark:text-error-400',
  white: 'text-white',
};

const textSizeClasses = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-sm',
  lg: 'text-base',
  xl: 'text-lg',
};

const animationClasses = {
  spin: 'animate-spin',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'primary',
  text = '加载中...',
  showText = false,
  className,
  textPosition = 'bottom',
  animation = 'spin',
  centered = false,
}) => {
  const spinnerClasses = clsx(
    sizeClasses[size],
    variantClasses[variant],
    animationClasses[animation],
    className
  );

  const textClasses = clsx(
    textSizeClasses[size],
    variantClasses[variant],
    'font-medium'
  );

  const containerClasses = clsx(
    'flex items-center',
    {
      'justify-center': centered,
      'flex-col gap-2': textPosition === 'bottom',
      'flex-row gap-2': textPosition === 'right',
    }
  );

  const content = (
    <div className={containerClasses}>
      <Loader2 className={spinnerClasses} />
      {showText && <span className={textClasses}>{text}</span>}
    </div>
  );

  if (centered) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        {content}
      </div>
    );
  }

  return content;
};

// 预设的加载组件
export const LoadingSpinnerSmall: React.FC<Pick<LoadingSpinnerProps, 'variant' | 'className'>> = (props) => (
  <LoadingSpinner size="sm" {...props} />
);

export const LoadingSpinnerLarge: React.FC<Pick<LoadingSpinnerProps, 'variant' | 'className'>> = (props) => (
  <LoadingSpinner size="lg" showText textPosition="bottom" {...props} />
);

export const LoadingSpinnerFullscreen: React.FC<Pick<LoadingSpinnerProps, 'text'>> = ({ text }) => (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-dark-900/80 backdrop-blur-sm">
    <LoadingSpinner size="xl" showText text={text} textPosition="bottom" />
  </div>
);

export const LoadingSpinnerOverlay: React.FC<Pick<LoadingSpinnerProps, 'text'>> = ({ text }) => (
  <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80 dark:bg-dark-900/80 backdrop-blur-sm">
    <LoadingSpinner size="lg" showText text={text} textPosition="bottom" />
  </div>
);