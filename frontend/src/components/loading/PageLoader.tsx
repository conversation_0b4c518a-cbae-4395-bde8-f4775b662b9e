/**
 * PageLoader 页面级加载组件
 * 用于显示全屏或页面区域的加载状态
 */

import React from 'react';
import { clsx } from 'clsx';
import { LoadingSpinner } from './LoadingSpinner';
import { Skeleton } from './Skeleton';

export interface PageLoaderProps {
  /** 是否显示 */
  show?: boolean;
  /** 加载文本 */
  text?: string;
  /** 加载类型 */
  type?: 'spinner' | 'skeleton' | 'progress' | 'dots';
  /** 尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 是否为全屏加载 */
  fullscreen?: boolean;
  /** 是否显示背景遮罩 */
  backdrop?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 进度值 (0-100) */
  progress?: number;
  /** 是否显示进度百分比 */
  showProgress?: boolean;
  /** 自定义logo */
  logo?: React.ReactNode;
  /** 描述文本 */
  description?: string;
}

export const PageLoader: React.FC<PageLoaderProps> = ({
  show = true,
  text = '加载中...',
  type = 'spinner',
  size = 'md',
  fullscreen = false,
  backdrop = true,
  className,
  progress = 0,
  showProgress = false,
  logo,
  description,
}) => {
  if (!show) return null;

  const containerClasses = clsx(
    'flex flex-col items-center justify-center',
    {
      'fixed inset-0 z-50': fullscreen,
      'absolute inset-0 z-10': !fullscreen,
      'bg-white/80 dark:bg-dark-900/80 backdrop-blur-sm': backdrop,
    },
    className
  );

  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return (
          <div className="flex flex-col items-center space-y-4">
            {logo && <div className="mb-2">{logo}</div>}
            <LoadingSpinner size={size} showText text={text} textPosition="bottom" />
            {description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 max-w-xs text-center">
                {description}
              </p>
            )}
          </div>
        );

      case 'skeleton':
        return (
          <div className="w-full max-w-md space-y-4">
            <div className="flex items-center space-x-4">
              <Skeleton variant="avatar" />
              <div className="flex-1 space-y-2">
                <Skeleton width="60%" height="16px" />
                <Skeleton width="40%" height="14px" />
              </div>
            </div>
            <Skeleton variant="card" />
            <div className="space-y-2">
              <Skeleton width="100%" height="12px" />
              <Skeleton width="80%" height="12px" />
              <Skeleton width="60%" height="12px" />
            </div>
          </div>
        );

      case 'progress':
        return (
          <div className="flex flex-col items-center space-y-4 w-full max-w-sm">
            {logo && <div className="mb-2">{logo}</div>}
            <div className="w-full">
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                <span>{text}</span>
                {showProgress && <span>{progress}%</span>}
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
            {description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                {description}
              </p>
            )}
          </div>
        );

      case 'dots':
        return (
          <div className="flex flex-col items-center space-y-4">
            {logo && <div className="mb-2">{logo}</div>}
            <div className="flex space-x-2">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="w-3 h-3 bg-primary-600 rounded-full animate-bounce"
                  style={{ animationDelay: `${i * 0.1}s` }}
                />
              ))}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">{text}</p>
            {description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                {description}
              </p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={containerClasses}>
      {renderLoader()}
    </div>
  );
};

// 预设的页面加载器组件
export const PageLoaderFullscreen: React.FC<Pick<PageLoaderProps, 'text' | 'type' | 'logo' | 'description'>> = (props) => (
  <PageLoader fullscreen backdrop {...props} />
);

export const PageLoaderCard: React.FC<Pick<PageLoaderProps, 'text' | 'type' | 'show'>> = (props) => (
  <PageLoader size="sm" backdrop={false} className="h-48" {...props} />
);

export const PageLoaderProgress: React.FC<Pick<PageLoaderProps, 'progress' | 'text' | 'description'>> = (props) => (
  <PageLoader type="progress" showProgress {...props} />
);

export const PageLoaderSkeleton: React.FC<Pick<PageLoaderProps, 'show' | 'className'>> = (props) => (
  <PageLoader type="skeleton" backdrop={false} {...props} />
);