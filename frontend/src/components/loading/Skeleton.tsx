/**
 * Skeleton 骨架屏组件
 * 用于显示内容加载时的占位符
 */

import React from 'react';
import { clsx } from 'clsx';

export interface SkeletonProps {
  /** 宽度 */
  width?: string | number;
  /** 高度 */
  height?: string | number;
  /** 自定义类名 */
  className?: string;
  /** 是否为圆形 */
  circle?: boolean;
  /** 预设样式 */
  variant?: 'text' | 'rect' | 'circle' | 'avatar' | 'button' | 'card';
  /** 动画类型 */
  animation?: 'pulse' | 'wave' | 'shimmer' | 'none';
  /** 圆角大小 */
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** 是否显示为行内元素 */
  inline?: boolean;
}

const variantClasses = {
  text: 'h-4 w-full',
  rect: 'h-12 w-full',
  circle: 'h-12 w-12 rounded-full',
  avatar: 'h-10 w-10 rounded-full',
  button: 'h-10 w-20 rounded-lg',
  card: 'h-32 w-full rounded-lg',
};

const roundedClasses = {
  none: 'rounded-none',
  sm: 'rounded-sm',
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  full: 'rounded-full',
};

const animationClasses = {
  pulse: 'animate-pulse',
  wave: 'animate-wave',
  shimmer: 'loading',
  none: '',
};

export const Skeleton: React.FC<SkeletonProps> = ({
  width,
  height,
  className,
  circle = false,
  variant = 'rect',
  animation = 'pulse',
  rounded = 'md',
  inline = false,
}) => {
  const style: React.CSSProperties = {};
  
  if (width) {
    style.width = typeof width === 'number' ? `${width}px` : width;
  }
  
  if (height) {
    style.height = typeof height === 'number' ? `${height}px` : height;
  }

  const skeletonClasses = clsx(
    'skeleton bg-gray-300 dark:bg-dark-700',
    {
      [variantClasses[variant]]: !width && !height,
      [roundedClasses[rounded]]: !circle && variant !== 'circle',
      'rounded-full': circle || variant === 'circle',
      'inline-block': inline,
      'block': !inline,
    },
    animationClasses[animation],
    className
  );

  return <div className={skeletonClasses} style={style} />;
};

// 预设的骨架屏组件
export const SkeletonText: React.FC<{
  lines?: number;
  width?: (string | number)[];
  className?: string;
}> = ({ lines = 1, width, className }) => {
  const lineArray = Array.from({ length: lines });
  
  return (
    <div className={clsx('space-y-2', className)}>
      {lineArray.map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          width={width?.[index] || (index === lines - 1 ? '60%' : '100%')}
        />
      ))}
    </div>
  );
};

export const SkeletonAvatar: React.FC<{
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}> = ({ size = 'md', className }) => {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  return (
    <Skeleton
      className={clsx(sizeClasses[size], className)}
      variant="circle"
    />
  );
};

export const SkeletonButton: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  width?: string | number;
  className?: string;
}> = ({ size = 'md', width, className }) => {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
  };

  return (
    <Skeleton
      className={clsx(sizeClasses[size], 'rounded-lg', className)}
      width={width || '80px'}
    />
  );
};

export const SkeletonCard: React.FC<{
  showAvatar?: boolean;
  showActions?: boolean;
  lines?: number;
  className?: string;
}> = ({ showAvatar = true, showActions = true, lines = 3, className }) => {
  return (
    <div className={clsx('p-4 space-y-4', className)}>
      {/* 头部 */}
      {showAvatar && (
        <div className="flex items-center space-x-3">
          <SkeletonAvatar size="md" />
          <div className="flex-1 space-y-2">
            <Skeleton width="120px" height="16px" />
            <Skeleton width="80px" height="14px" />
          </div>
        </div>
      )}
      
      {/* 内容 */}
      <SkeletonText lines={lines} />
      
      {/* 操作按钮 */}
      {showActions && (
        <div className="flex space-x-2">
          <SkeletonButton size="sm" width="60px" />
          <SkeletonButton size="sm" width="60px" />
        </div>
      )}
    </div>
  );
};

export const SkeletonList: React.FC<{
  count?: number;
  showAvatar?: boolean;
  className?: string;
}> = ({ count = 5, showAvatar = true, className }) => {
  const items = Array.from({ length: count });
  
  return (
    <div className={clsx('space-y-4', className)}>
      {items.map((_, index) => (
        <div key={index} className="flex items-center space-x-3">
          {showAvatar && <SkeletonAvatar size="md" />}
          <div className="flex-1 space-y-2">
            <Skeleton width="60%" height="16px" />
            <Skeleton width="40%" height="14px" />
          </div>
        </div>
      ))}
    </div>
  );
};

export const SkeletonTable: React.FC<{
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  className?: string;
}> = ({ rows = 5, columns = 4, showHeader = true, className }) => {
  const rowArray = Array.from({ length: rows });
  const columnArray = Array.from({ length: columns });
  
  return (
    <div className={clsx('space-y-3', className)}>
      {/* 表头 */}
      {showHeader && (
        <div className="flex space-x-4">
          {columnArray.map((_, index) => (
            <Skeleton key={index} width="100px" height="20px" className="flex-1" />
          ))}
        </div>
      )}
      
      {/* 表格行 */}
      {rowArray.map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {columnArray.map((_, colIndex) => (
            <Skeleton key={colIndex} height="16px" className="flex-1" />
          ))}
        </div>
      ))}
    </div>
  );
};