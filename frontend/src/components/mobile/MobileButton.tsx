import React, { forwardRef } from 'react';
import { useButtonInteraction } from '../../hooks/useMicroInteraction';
import { useMobile } from '../../hooks/useResponsive';

export interface MobileButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  touchOptimized?: boolean;
}

/**
 * 移动端优化的按钮组件
 * 提供触摸友好的设计和交互体验
 */
export const MobileButton = forwardRef<HTMLButtonElement, MobileButtonProps>(
  ({
    children,
    variant = 'primary',
    size = 'md',
    fullWidth = false,
    loading = false,
    leftIcon,
    rightIcon,
    touchOptimized = true,
    className = '',
    disabled,
    ...props
  }, ref) => {
    const { isMobile, isTouchDevice } = useMobile();
    const buttonInteraction = useButtonInteraction({
      enableTouch: isTouchDevice,
    });

    // 根据移动端优化调整尺寸
    const getOptimizedSize = () => {
      if (!touchOptimized || !isMobile) return size;
      
      // 移动端最小推荐触摸目标：44px (Apple HIG) / 48dp (Material Design)
      const sizeMap = {
        xs: 'sm', // 升级小尺寸
        sm: 'md', // 升级小尺寸
        md: 'md', // 保持中等
        lg: 'lg', // 保持大尺寸
        xl: 'xl', // 保持超大
      };
      return sizeMap[size] as typeof size;
    };

    const optimizedSize = getOptimizedSize();

    // 尺寸样式
    const sizeStyles = {
      xs: 'px-2 py-1 text-xs min-h-[32px]',
      sm: 'px-3 py-2 text-sm min-h-[36px]',
      md: 'px-4 py-2.5 text-sm min-h-[44px]', // 移动端友好高度
      lg: 'px-6 py-3 text-base min-h-[48px]', // 符合Material Design
      xl: 'px-8 py-4 text-lg min-h-[56px]',
    };

    // 变体样式
    const variantStyles = {
      primary: `
        bg-primary-600 hover:bg-primary-700 active:bg-primary-800
        text-white shadow-sm
        disabled:bg-primary-300 disabled:text-primary-100
      `,
      secondary: `
        bg-gray-600 hover:bg-gray-700 active:bg-gray-800
        text-white shadow-sm
        disabled:bg-gray-300 disabled:text-gray-100
      `,
      outline: `
        border-2 border-primary-600 hover:border-primary-700 active:border-primary-800
        text-primary-600 hover:text-primary-700 active:text-primary-800
        bg-transparent hover:bg-primary-50 active:bg-primary-100
        disabled:border-gray-300 disabled:text-gray-300 disabled:bg-transparent
      `,
      ghost: `
        text-primary-600 hover:text-primary-700 active:text-primary-800
        bg-transparent hover:bg-primary-50 active:bg-primary-100
        disabled:text-gray-300 disabled:bg-transparent
      `,
      danger: `
        bg-red-600 hover:bg-red-700 active:bg-red-800
        text-white shadow-sm
        disabled:bg-red-300 disabled:text-red-100
      `,
    };

    // 基础样式
    const baseStyles = `
      inline-flex items-center justify-center
      font-medium rounded-lg
      transition-all duration-200 ease-out
      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
      disabled:cursor-not-allowed disabled:opacity-60
      ${isTouchDevice ? 'select-none' : ''}
      ${fullWidth ? 'w-full' : ''}
    `;

    // 触摸优化样式
    const touchStyles = isTouchDevice ? `
      active:scale-95
      touch-manipulation
      -webkit-tap-highlight-color: transparent
    ` : '';

    // 获取最终的className
    const finalClassName = [
      baseStyles,
      sizeStyles[optimizedSize],
      variantStyles[variant],
      touchStyles,
      className,
    ].filter(Boolean).join(' ');

    return (
      <button
        ref={ref}
        className={buttonInteraction.getAnimationClasses(finalClassName)}
        disabled={disabled || loading}
        {...buttonInteraction.handlers}
        {...props}
      >
        {/* 加载状态 */}
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}

        {/* 左侧图标 */}
        {leftIcon && !loading && (
          <span className={`${children ? 'mr-2' : ''} flex-shrink-0`}>
            {leftIcon}
          </span>
        )}

        {/* 按钮文本 */}
        {children && (
          <span className={loading ? 'opacity-75' : ''}>
            {children}
          </span>
        )}

        {/* 右侧图标 */}
        {rightIcon && !loading && (
          <span className={`${children ? 'ml-2' : ''} flex-shrink-0`}>
            {rightIcon}
          </span>
        )}
      </button>
    );
  }
);

MobileButton.displayName = 'MobileButton'; 