import React, { useState } from 'react';
import { FadeTransition, SlideTransition } from '../animation';
import { MobileButton } from './MobileButton';
import { useMobile } from '../../hooks/useResponsive';
import { useSwipe } from '../../hooks/useGestures';

export interface NavItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  active?: boolean;
  badge?: string | number;
  disabled?: boolean;
}

export interface MobileNavProps {
  items: NavItem[];
  isOpen: boolean;
  onClose: () => void;
  onItemClick?: (item: NavItem) => void;
  title?: string;
  footer?: React.ReactNode;
}

/**
 * 移动端抽屉式导航菜单
 */
export const MobileDrawerNav: React.FC<MobileNavProps> = ({
  items,
  isOpen,
  onClose,
  onItemClick,
  title = '导航菜单',
  footer,
}) => {
  const { isMobile } = useMobile();
  
  // 支持右滑关闭手势
  const swipeGesture = useSwipe((direction) => {
    if (direction === 'right' && isOpen) {
      onClose();
    }
  }, {
    swipeThreshold: 50,
    velocityThreshold: 0.3,
  });

  const handleItemClick = (item: NavItem) => {
    if (item.disabled) return;
    
    item.onClick?.();
    onItemClick?.(item);
    onClose(); // 点击后自动关闭菜单
  };

  if (!isMobile) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <FadeTransition show={isOpen} duration={300}>
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        />
      </FadeTransition>

      {/* 抽屉内容 */}
      <SlideTransition
        show={isOpen}
        direction="right"
        duration={300}
        className="fixed top-0 left-0 bottom-0 w-80 max-w-[85vw] bg-white dark:bg-gray-800 shadow-xl z-50"
        unmountOnExit={true}
      >
        <div
          ref={swipeGesture.ref as React.RefObject<HTMLDivElement>}
          className="flex flex-col h-full"
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {title}
            </h2>
            <MobileButton
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </MobileButton>
          </div>

          {/* 导航项列表 */}
          <nav className="flex-1 px-2 py-4 space-y-2 overflow-y-auto">
            {items.map((item) => (
              <button
                key={item.id}
                onClick={() => handleItemClick(item)}
                disabled={item.disabled}
                className={`
                  w-full flex items-center px-3 py-3 text-left rounded-lg
                  transition-colors duration-200
                  ${item.active
                    ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-200'
                    : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                  ${item.disabled
                    ? 'opacity-50 cursor-not-allowed'
                    : 'active:bg-gray-200 dark:active:bg-gray-600'
                  }
                `}
              >
                {/* 图标 */}
                {item.icon && (
                  <span className="mr-3 flex-shrink-0 w-6 h-6 flex items-center justify-center">
                    {item.icon}
                  </span>
                )}

                {/* 标签 */}
                <span className="flex-1 font-medium">
                  {item.label}
                </span>

                {/* 徽章 */}
                {item.badge && (
                  <span className="ml-2 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 text-xs font-medium px-2 py-1 rounded-full">
                    {item.badge}
                  </span>
                )}
              </button>
            ))}
          </nav>

          {/* 底部 */}
          {footer && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              {footer}
            </div>
          )}
        </div>
      </SlideTransition>
    </>
  );
};

/**
 * 移动端底部导航栏
 */
export interface MobileBottomNavProps {
  items: NavItem[];
  onItemClick?: (item: NavItem) => void;
  className?: string;
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({
  items,
  onItemClick,
  className = '',
}) => {
  const { isMobile } = useMobile();

  const handleItemClick = (item: NavItem) => {
    if (item.disabled) return;
    
    item.onClick?.();
    onItemClick?.(item);
  };

  if (!isMobile) return null;

  return (
    <nav className={`
      fixed bottom-0 left-0 right-0 z-30
      bg-white dark:bg-gray-800 
      border-t border-gray-200 dark:border-gray-700
      safe-area-inset-bottom
      ${className}
    `}>
      <div className="flex items-center justify-around px-2 py-2">
        {items.map((item) => (
          <button
            key={item.id}
            onClick={() => handleItemClick(item)}
            disabled={item.disabled}
            className={`
              relative flex flex-col items-center justify-center
              px-3 py-2 min-w-0 flex-1
              transition-colors duration-200
              ${item.active
                ? 'text-primary-600 dark:text-primary-400'
                : 'text-gray-500 dark:text-gray-400'
              }
              ${item.disabled
                ? 'opacity-50 cursor-not-allowed'
                : 'active:text-primary-700 dark:active:text-primary-300'
              }
            `}
          >
            {/* 图标 */}
            {item.icon && (
              <div className="relative mb-1">
                <span className="w-6 h-6 flex items-center justify-center">
                  {item.icon}
                </span>
                {/* 徽章 */}
                {item.badge && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-[16px] h-4 flex items-center justify-center px-1">
                    {item.badge}
                  </span>
                )}
              </div>
            )}

            {/* 标签 */}
            <span className="text-xs font-medium truncate max-w-full">
              {item.label}
            </span>

            {/* 活动指示器 */}
            {item.active && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 dark:bg-primary-400 rounded-full" />
            )}
          </button>
        ))}
      </div>
    </nav>
  );
};

/**
 * 移动端浮动操作按钮
 */
export interface MobileFABProps {
  icon: React.ReactNode;
  onClick: () => void;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'danger';
  className?: string;
  disabled?: boolean;
}

export const MobileFAB: React.FC<MobileFABProps> = ({
  icon,
  onClick,
  position = 'bottom-right',
  size = 'md',
  variant = 'primary',
  className = '',
  disabled = false,
}) => {
  const { isMobile } = useMobile();

  if (!isMobile) return null;

  const positionStyles = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'bottom-center': 'bottom-6 left-1/2 transform -translate-x-1/2',
  };

  const sizeStyles = {
    sm: 'w-12 h-12',
    md: 'w-14 h-14',
    lg: 'w-16 h-16',
  };

  const variantStyles = {
    primary: 'bg-primary-600 hover:bg-primary-700 text-white shadow-lg',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white shadow-lg',
    danger: 'bg-red-600 hover:bg-red-700 text-white shadow-lg',
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        fixed z-40 rounded-full
        flex items-center justify-center
        transition-all duration-200 ease-out
        active:scale-95
        disabled:opacity-50 disabled:cursor-not-allowed
        ${positionStyles[position]}
        ${sizeStyles[size]}
        ${variantStyles[variant]}
        ${className}
      `}
    >
      {icon}
    </button>
  );
}; 