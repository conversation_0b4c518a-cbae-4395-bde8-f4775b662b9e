import React, { useState } from 'react';
import { PageTransition, AnimatedElement } from '../animation';
import { MobileButton } from './MobileButton';
import { MobileDrawerNav, MobileBottomNav, MobileFAB, NavItem } from './MobileNav';
import { useMobile, useGestures, useMobilePerformance, useLazyImage } from '../../hooks';

export const MobileShowcase: React.FC = () => {
  const { isMobile, isTouchDevice, isPortrait } = useMobile();
  const { metrics, recommendations } = useMobilePerformance();
  
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('components');
  const [gestureLog, setGestureLog] = useState<string[]>([]);

  // 手势演示
  const gestureDemo = useGestures({
    onSwipeLeft: () => addGestureLog('👈 向左滑动'),
    onSwipeRight: () => addGestureLog('👉 向右滑动'),
    onSwipeUp: () => addGestureLog('👆 向上滑动'),
    onSwipeDown: () => addGestureLog('👇 向下滑动'),
    onTap: () => addGestureLog('👆 单击'),
    onDoubleTap: () => addGestureLog('👆👆 双击'),
    onLongPress: () => addGestureLog('👆⏰ 长按'),
  });

  const addGestureLog = (message: string) => {
    setGestureLog(prev => [
      `${new Date().toLocaleTimeString()}: ${message}`,
      ...prev.slice(0, 4)
    ]);
  };

  // 懒加载图片演示
  const lazyImage = useLazyImage(
    'https://picsum.photos/400/300?random=1',
    {
      fallbackSrc: 'https://via.placeholder.com/400x300?text=Fallback',
      onLoad: () => addGestureLog('🖼️ 图片加载完成'),
    }
  );

  // 导航项
  const navItems: NavItem[] = [
    {
      id: 'components',
      label: '组件展示',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14-7v2M5 4v2M19 18h-3m-4 0H5m7-4v2M9 8v2" />
        </svg>
      ),
      active: activeTab === 'components',
      onClick: () => setActiveTab('components'),
    },
    {
      id: 'gestures',
      label: '手势识别',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
        </svg>
      ),
      active: activeTab === 'gestures',
      onClick: () => setActiveTab('gestures'),
    },
    {
      id: 'performance',
      label: '性能监控',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      active: activeTab === 'performance',
      onClick: () => setActiveTab('performance'),
    },
    {
      id: 'responsive',
      label: '响应式',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      active: activeTab === 'responsive',
      onClick: () => setActiveTab('responsive'),
    },
  ];

  const drawerItems: NavItem[] = [
    { id: '1', label: '首页', icon: '🏠' },
    { id: '2', label: '聊天', icon: '💬', badge: '3' },
    { id: '3', label: '文档', icon: '📄' },
    { id: '4', label: '设置', icon: '⚙️' },
    { id: '5', label: '帮助', icon: '❓' },
  ];

  if (!isMobile) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
        <div className="text-center p-8">
          <div className="text-6xl mb-4">📱</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            移动端展示页面
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            请在移动设备或使用浏览器的移动端模拟器查看此页面
          </p>
        </div>
      </div>
    );
  }

  return (
    <PageTransition type="fade" duration={400}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20">
        {/* 头部 */}
        <header className="bg-white dark:bg-gray-800 shadow-sm px-4 py-3 flex items-center justify-between">
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
            移动端展示
          </h1>
          <MobileButton
            variant="ghost"
            size="sm"
            onClick={() => setIsDrawerOpen(true)}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </MobileButton>
        </header>

        {/* 主要内容 */}
        <main className="px-4 py-6 space-y-6">
          {activeTab === 'components' && (
            <AnimatedElement animation="slide-up" trigger="visible">
              <div className="space-y-6">
                {/* 按钮展示 */}
                <section className="bg-white dark:bg-gray-800 rounded-lg p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    移动端按钮
                  </h2>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <MobileButton variant="primary" size="md">
                        主要按钮
                      </MobileButton>
                      <MobileButton variant="secondary" size="md">
                        次要按钮
                      </MobileButton>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <MobileButton variant="outline" size="md">
                        轮廓按钮
                      </MobileButton>
                      <MobileButton variant="ghost" size="md">
                        幽灵按钮
                      </MobileButton>
                    </div>
                    <MobileButton 
                      variant="danger" 
                      size="lg" 
                      fullWidth
                      leftIcon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      }
                    >
                      删除操作
                    </MobileButton>
                    <MobileButton 
                      variant="primary" 
                      size="md" 
                      loading
                      fullWidth
                    >
                      加载中...
                    </MobileButton>
                  </div>
                </section>

                {/* 懒加载图片 */}
                <section className="bg-white dark:bg-gray-800 rounded-lg p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    懒加载图片
                  </h2>
                  <div className="space-y-4">
                    <div
                      ref={lazyImage.ref}
                      className="w-full h-48 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden"
                    >
                      {lazyImage.src ? (
                        <img 
                          src={lazyImage.src} 
                          alt="懒加载演示"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-500">
                          <div className="text-center">
                            <div className="text-2xl mb-2">🖼️</div>
                            <div>图片懒加载中...</div>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      状态: {lazyImage.isInView ? '可见' : '不可见'} | 
                      已加载: {lazyImage.isLoaded ? '是' : '否'}
                    </div>
                  </div>
                </section>
              </div>
            </AnimatedElement>
          )}

          {activeTab === 'gestures' && (
            <AnimatedElement animation="slide-up" trigger="visible">
              <div className="space-y-6">
                <section className="bg-white dark:bg-gray-800 rounded-lg p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    手势识别区域
                  </h2>
                  <div
                    ref={gestureDemo.ref as React.RefObject<HTMLDivElement>}
                    className="h-48 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg flex items-center justify-center text-center p-4"
                  >
                    <div>
                      <div className="text-3xl mb-2">👋</div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        在此区域尝试滑动、点击、双击、长按等手势
                      </p>
                    </div>
                  </div>
                </section>

                <section className="bg-white dark:bg-gray-800 rounded-lg p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    手势日志
                  </h2>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {gestureLog.length > 0 ? (
                      gestureLog.map((log, index) => (
                        <div
                          key={index}
                          className="text-sm bg-gray-50 dark:bg-gray-700 rounded p-2"
                        >
                          {log}
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                        暂无手势记录
                      </div>
                    )}
                  </div>
                </section>
              </div>
            </AnimatedElement>
          )}

          {activeTab === 'performance' && (
            <AnimatedElement animation="slide-up" trigger="visible">
              <div className="space-y-6">
                <section className="bg-white dark:bg-gray-800 rounded-lg p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    设备信息
                  </h2>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">移动设备:</span>
                      <span className={isMobile ? 'text-green-600' : 'text-red-600'}>
                        {isMobile ? '是' : '否'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">触摸设备:</span>
                      <span className={isTouchDevice ? 'text-green-600' : 'text-red-600'}>
                        {isTouchDevice ? '是' : '否'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">方向:</span>
                      <span>{isPortrait ? '竖屏' : '横屏'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">网络:</span>
                      <span className={metrics.isSlowConnection ? 'text-yellow-600' : 'text-green-600'}>
                        {metrics.connectionType || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">内存:</span>
                      <span className={metrics.isLowMemory ? 'text-red-600' : 'text-green-600'}>
                        {metrics.isLowMemory ? '内存不足' : '正常'}
                      </span>
                    </div>
                  </div>
                </section>

                <section className="bg-white dark:bg-gray-800 rounded-lg p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    优化建议
                  </h2>
                  <div className="space-y-2">
                    {recommendations.length > 0 ? (
                      recommendations.map((rec, index) => (
                        <div key={index} className="flex items-start space-x-2 text-sm">
                          <span className="text-blue-500 mt-0.5">•</span>
                          <span className="text-gray-600 dark:text-gray-300">{rec}</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                        当前无需优化
                      </div>
                    )}
                  </div>
                </section>
              </div>
            </AnimatedElement>
          )}
        </main>

        {/* 抽屉导航 */}
        <MobileDrawerNav
          items={drawerItems}
          isOpen={isDrawerOpen}
          onClose={() => setIsDrawerOpen(false)}
          title="导航菜单"
          footer={
            <MobileButton variant="outline" size="sm" fullWidth>
              退出登录
            </MobileButton>
          }
        />

        {/* 底部导航 */}
        <MobileBottomNav
          items={navItems}
          onItemClick={(item) => setActiveTab(item.id)}
        />

        {/* 浮动操作按钮 */}
        <MobileFAB
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          }
          onClick={() => addGestureLog('✨ FAB 按钮点击')}
          position="bottom-right"
        />
      </div>
    </PageTransition>
  );
}; 