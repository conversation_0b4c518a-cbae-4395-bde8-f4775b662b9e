import React from 'react';

interface HighlightTextProps {
  text: string;
  searchTerms?: string[];
  className?: string;
  highlightClassName?: string;
  caseSensitive?: boolean;
}

/**
 * 高亮文本组件
 * 用于在源文档内容中突出显示搜索关键词
 */
export const HighlightText: React.FC<HighlightTextProps> = ({
  text,
  searchTerms = [],
  className = '',
  highlightClassName = 'bg-yellow-200 text-yellow-800 px-1 rounded',
  caseSensitive = false
}) => {
  if (!searchTerms.length || !text) {
    return <span className={className}>{text}</span>;
  }

  // 过滤掉空的搜索词，并按长度降序排序（优先匹配长词）
  const validTerms = searchTerms
    .filter(term => term && term.trim().length > 0)
    .map(term => term.trim())
    .sort((a, b) => b.length - a.length);

  if (!validTerms.length) {
    return <span className={className}>{text}</span>;
  }

  // 创建正则表达式，转义特殊字符
  const escapeRegex = (str: string) => {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  };

  // 构建匹配模式
  const pattern = validTerms.map(escapeRegex).join('|');
  const regex = new RegExp(`(${pattern})`, caseSensitive ? 'g' : 'gi');

  // 分割文本并高亮匹配项
  const parts = text.split(regex);
  
  return (
    <span className={className}>
      {parts.map((part, index) => {
        if (!part) return null;
        
        // 检查是否是匹配的词
        const isMatch = validTerms.some(term => 
          caseSensitive ? part === term : part.toLowerCase() === term.toLowerCase()
        );
        
        if (isMatch) {
          return (
            <mark key={index} className={highlightClassName}>
              {part}
            </mark>
          );
        }
        
        return <span key={index}>{part}</span>;
      })}
    </span>
  );
};

/**
 * Hook：从查询字符串中提取搜索关键词
 */
export const useSearchTerms = (query?: string): string[] => {
  if (!query) return [];
  
  // 简单的词汇提取：按空格、标点分割，过滤短词
  return query
    .toLowerCase()
    .split(/[\s,，。！？；：""''（）【】\[\]<>《》]+/)
    .filter(term => term.length >= 2) // 过滤太短的词
    .filter((term, index, arr) => arr.indexOf(term) === index); // 去重
};

/**
 * 智能高亮组件
 * 自动从查询中提取关键词进行高亮
 */
interface SmartHighlightProps {
  text: string;
  query?: string | undefined;
  className?: string;
  highlightClassName?: string;
}

export const SmartHighlight: React.FC<SmartHighlightProps> = ({
  text,
  query,
  className = '',
  highlightClassName = 'bg-yellow-200 text-yellow-800 px-1 rounded'
}) => {
  const searchTerms = useSearchTerms(query);
  
  return (
    <HighlightText
      text={text}
      searchTerms={searchTerms}
      className={className}
      highlightClassName={highlightClassName}
    />
  );
};

export default HighlightText; 