# 源文档引用组件使用指南

本目录包含用于显示AI回复源文档引用的组件，支持相似度展示、内容高亮、展开折叠等功能。

## 组件列表

### 1. `SourceReferences` - 基础引用列表组件
显示源文档引用的基础组件，支持内容截断、展开/折叠、相似度展示。

```tsx
import { SourceReferences } from './components/sources';

// 基础使用
<SourceReferences
  sources={chatResponse.sources}
  maxInitialVisible={3}
  showSimilarityScores={true}
  searchQuery={userQuery} // 支持关键词高亮
  onSourceClick={(source) => {
    // 处理源文档点击，例如跳转到文档页面
    navigateToDocument(source.document_id);
  }}
/>
```

### 2. `SourceSidebar` - 响应式侧边栏组件
支持桌面端侧边栏和移动端底部抽屉模式的响应式组件。

```tsx
import { SourceSidebar, SourceFloatingButton } from './components/sources';

function ChatPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <div>
      {/* 聊天内容 */}
      <div className="chat-content">
        {messages.map(message => (
          <div key={message.id}>
            {message.content}
            {/* 如果消息有源文档，显示浮动按钮 */}
            {message.sources?.length > 0 && (
              <SourceFloatingButton
                sourcesCount={message.sources.length}
                onClick={() => setSidebarOpen(true)}
              />
            )}
          </div>
        ))}
      </div>
      
      {/* 源文档侧边栏 */}
      <SourceSidebar
        sources={currentMessageSources}
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
        onSourceClick={handleSourceClick}
      />
    </div>
  );
}
```

### 3. `HighlightText` & `SmartHighlight` - 高亮组件
用于在文档内容中高亮显示搜索关键词。

```tsx
import { HighlightText, SmartHighlight } from './components/sources';

// 手动指定高亮词汇
<HighlightText
  text="这是一段包含关键词的文本内容"
  searchTerms={['关键词', '文本']}
  highlightClassName="bg-yellow-200 text-yellow-900"
/>

// 自动从查询中提取关键词
<SmartHighlight
  text={source.content}
  query={userQuery}
/>
```

## 数据类型

源文档引用的数据结构：

```tsx
interface SourceReference {
  content: string;           // 文档内容片段
  similarity: number;        // 相似度分数 (0-1)
  document_id?: string;      // 文档ID
  document_name?: string;    // 文档名称
  chunk_id?: string;         // 文档块ID
  page?: number;            // 页码
  section?: string;         // 章节名称
}
```

## 集成到聊天消息组件

```tsx
// ChatMessage.tsx
import { SourceReferences } from '../sources';

interface ChatMessageProps {
  message: ChatMessageResponse;
  userQuery?: string;  // 用户的原始查询，用于高亮
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  userQuery 
}) => {
  return (
    <div className="chat-message">
      {/* 消息内容 */}
      <div className="message-content">
        {message.content}
      </div>
      
      {/* 源文档引用 */}
      {message.sources && message.sources.length > 0 && (
        <div className="mt-4">
          <SourceReferences
            sources={message.sources}
            searchQuery={userQuery}
            maxInitialVisible={2}
            onSourceClick={(source) => {
              // 处理源文档点击
              if (source.document_id) {
                openDocumentModal(source.document_id, source.chunk_id);
              }
            }}
          />
        </div>
      )}
    </div>
  );
};
```

## 样式定制

所有组件都使用Tailwind CSS，可以通过以下方式自定义样式：

1. **通过className属性**：
```tsx
<SourceReferences 
  className="custom-sources-style"
  sources={sources}
/>
```

2. **通过高亮类名**：
```tsx
<HighlightText
  text={content}
  searchTerms={terms}
  highlightClassName="bg-blue-200 text-blue-900 font-semibold"
/>
```

3. **响应式设计**：
组件已内置响应式设计，在不同屏幕尺寸下会自动适配：
- 桌面端：右侧侧边栏
- 移动端：底部抽屉式面板

## 最佳实践

1. **性能优化**：
   - 使用`maxInitialVisible`限制初始显示的引用数量
   - 长内容自动截断，用户可选择展开

2. **用户体验**：
   - 提供清晰的相似度分数指示
   - 支持关键词高亮，便于用户快速定位相关内容
   - 响应式设计适配不同设备

3. **交互设计**：
   - 点击源文档可跳转到完整文档
   - 浮动按钮提供快速访问入口
   - 展开/折叠功能避免界面过于拥挤

## 未来扩展

- 支持PDF页面预览
- 添加文档片段的上下文展示
- 集成文档搜索和过滤功能
- 支持批量操作源文档 