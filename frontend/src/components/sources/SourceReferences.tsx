import React, { useState, useMemo } from 'react';
import { 
  SourceReferencesProps, 
  SourceReferenceItemProps, 
  SourceReference 
} from '../../types/chat';
import { SmartHighlight } from './HighlightText';

/**
 * 单个源文档引用项组件
 */
const SourceReferenceItem: React.FC<SourceReferenceItemProps & { searchQuery?: string | undefined }> = ({
  source,
  index,
  isExpanded = false,
  onToggle,
  onSourceClick,
  showSimilarityScore = true,
  searchQuery
}) => {
  const similarityPercentage = Math.round(source.similarity * 100);
  const truncatedContent = source.content.length > 150 
    ? source.content.slice(0, 150) + '...' 
    : source.content;

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.8) return 'text-green-600 bg-green-50';
    if (similarity >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-gray-600 bg-gray-50';
  };

  const handleContentClick = () => {
    if (onSourceClick) {
      onSourceClick(source);
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-3 mb-2 bg-white hover:shadow-sm transition-shadow">
      {/* 头部：标题和相似度 */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">
            引用 {index + 1}
          </span>
          {source.document_name && (
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {source.document_name}
            </span>
          )}
        </div>
        {showSimilarityScore && (
          <span className={`text-xs px-2 py-1 rounded-full ${getSimilarityColor(source.similarity)}`}>
            {similarityPercentage}% 匹配
          </span>
        )}
      </div>

      {/* 内容 - 支持高亮显示 */}
      <div className="text-sm text-gray-700 leading-relaxed">
        <div 
          className={`${onSourceClick ? 'cursor-pointer hover:text-blue-600' : ''}`}
          onClick={handleContentClick}
        >
          <SmartHighlight
            text={isExpanded ? source.content : truncatedContent}
            query={searchQuery}
            highlightClassName="bg-yellow-200 text-yellow-900 px-0.5 rounded"
          />
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mt-2">
        <div className="flex items-center space-x-2">
          {source.page && (
            <span className="text-xs text-gray-500">第 {source.page} 页</span>
          )}
          {source.section && (
            <span className="text-xs text-gray-500">{source.section}</span>
          )}
        </div>
        {source.content.length > 150 && (
          <button
            onClick={onToggle}
            className="text-xs text-blue-600 hover:text-blue-800 font-medium"
          >
            {isExpanded ? '收起' : '展开'}
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * 源文档引用组件主体
 */
export const SourceReferences: React.FC<SourceReferencesProps & { searchQuery?: string | undefined }> = ({
  sources,
  className = '',
  maxInitialVisible = 3,
  showSimilarityScores = true,
  onSourceClick,
  searchQuery
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [showAll, setShowAll] = useState(false);

  // 排序：按相似度降序排列
  const sortedSources = useMemo(() => {
    return [...sources].sort((a, b) => b.similarity - a.similarity);
  }, [sources]);

  // 显示的源文档
  const visibleSources = showAll 
    ? sortedSources 
    : sortedSources.slice(0, maxInitialVisible);

  const toggleItemExpansion = (index: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  if (!sources || sources.length === 0) {
    return null;
  }

  return (
    <div className={`source-references ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-semibold text-gray-800 flex items-center">
          📚 相关文档 
          <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
            {sources.length}
          </span>
        </h4>
      </div>

      {/* 源文档列表 */}
      <div className="space-y-2">
        {visibleSources.map((source, index) => (
          <SourceReferenceItem
            key={index}
            source={source}
            index={index}
            isExpanded={expandedItems.has(index)}
            onToggle={() => toggleItemExpansion(index)}
            onSourceClick={onSourceClick}
            showSimilarityScore={showSimilarityScores}
            searchQuery={searchQuery}
          />
        ))}
      </div>

      {/* 显示更多按钮 */}
      {sources.length > maxInitialVisible && (
        <div className="mt-3 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium px-3 py-1 rounded border border-blue-200 hover:bg-blue-50"
          >
            {showAll 
              ? `收起 (显示 ${maxInitialVisible} 个)` 
              : `显示全部 ${sources.length} 个引用`
            }
          </button>
        </div>
      )}
    </div>
  );
};

export default SourceReferences; 