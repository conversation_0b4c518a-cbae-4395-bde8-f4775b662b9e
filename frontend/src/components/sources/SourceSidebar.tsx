import React, { useState, useEffect } from 'react';
import { SourceReference } from '../../types/chat';
import SourceReferences from './SourceReferences';

interface SourceSidebarProps {
  sources: SourceReference[];
  isOpen: boolean;
  onToggle: () => void;
  onSourceClick?: (source: SourceReference) => void;
  className?: string;
}

/**
 * 响应式源文档引用侧边栏组件
 * 支持桌面端侧边栏和移动端底部抽屉模式
 */
export const SourceSidebar: React.FC<SourceSidebarProps> = ({
  sources,
  isOpen,
  onToggle,
  onSourceClick,
  className = ''
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  if (!sources || sources.length === 0) {
    return null;
  }

  // 移动端底部抽屉样式
  if (isMobile) {
    return (
      <>
        {/* 背景遮罩 */}
        {isOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={onToggle}
          />
        )}
        
        {/* 底部抽屉 */}
        <div 
          className={`
            fixed bottom-0 left-0 right-0 bg-white z-50 transition-transform duration-300
            ${isOpen ? 'translate-y-0' : 'translate-y-full'}
            max-h-[70vh] overflow-hidden flex flex-col
            ${className}
          `}
        >
          {/* 抽屉头部 */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <h3 className="text-lg font-semibold text-gray-800">相关文档</h3>
            <button
              onClick={onToggle}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
              aria-label="关闭"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* 抽屉内容 */}
          <div className="flex-1 overflow-y-auto p-4">
            <SourceReferences
              sources={sources}
              onSourceClick={onSourceClick}
              showSimilarityScores={true}
              maxInitialVisible={10}
            />
          </div>
        </div>
      </>
    );
  }

  // 桌面端侧边栏样式
  return (
    <div 
      className={`
        fixed right-0 top-0 bottom-0 bg-white border-l border-gray-200 z-30
        transition-transform duration-300 
        ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        w-96 overflow-hidden flex flex-col
        ${className}
      `}
    >
      {/* 侧边栏头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-800">相关文档</h3>
        <button
          onClick={onToggle}
          className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
          aria-label="关闭"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {/* 侧边栏内容 */}
      <div className="flex-1 overflow-y-auto p-4">
        <SourceReferences
          sources={sources}
          onSourceClick={onSourceClick}
          showSimilarityScores={true}
          maxInitialVisible={10}
        />
      </div>
    </div>
  );
};

/**
 * 源文档引用浮动按钮
 * 用于触发侧边栏的显示/隐藏
 */
interface SourceFloatingButtonProps {
  sourcesCount: number;
  onClick: () => void;
  className?: string;
}

export const SourceFloatingButton: React.FC<SourceFloatingButtonProps> = ({
  sourcesCount,
  onClick,
  className = ''
}) => {
  if (sourcesCount === 0) {
    return null;
  }

  return (
    <button
      onClick={onClick}
      className={`
        fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white 
        rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200
        flex items-center space-x-2 z-20
        ${className}
      `}
      aria-label={`查看 ${sourcesCount} 个相关文档`}
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <span className="text-sm font-medium">{sourcesCount}</span>
    </button>
  );
};

export default SourceSidebar; 