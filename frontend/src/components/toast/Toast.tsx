/**
 * Toast 通知组件
 * 支持多种类型的通知消息
 */

import React, { useEffect, useState } from 'react';
import { clsx } from 'clsx';
import {
  X,
  CheckCircle2,
  AlertCircle,
  AlertTriangle,
  Info,
  Loader2,
} from 'lucide-react';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface ToastProps {
  /** 唯一ID */
  id: string;
  /** 标题 */
  title?: string;
  /** 消息内容 */
  message: string;
  /** 类型 */
  type?: ToastType;
  /** 持续时间（毫秒），0表示不自动关闭 */
  duration?: number;
  /** 是否可关闭 */
  closable?: boolean;
  /** 是否可点击 */
  clickable?: boolean;
  /** 位置 */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  /** 自定义图标 */
  icon?: React.ReactNode;
  /** 操作按钮 */
  action?: {
    label: string;
    onClick: () => void;
  };
  /** 关闭回调 */
  onClose: () => void;
  /** 点击回调 */
  onClick?: () => void;
  /** 自定义类名 */
  className?: string;
  /** 是否显示动画 */
  animated?: boolean;
}

const typeConfig = {
  success: {
    icon: CheckCircle2,
    className: 'border-success-200 bg-success-50 text-success-800 dark:border-success-800 dark:bg-success-900/20 dark:text-success-200',
    iconClassName: 'text-success-500 dark:text-success-400',
  },
  error: {
    icon: AlertCircle,
    className: 'border-error-200 bg-error-50 text-error-800 dark:border-error-800 dark:bg-error-900/20 dark:text-error-200',
    iconClassName: 'text-error-500 dark:text-error-400',
  },
  warning: {
    icon: AlertTriangle,
    className: 'border-warning-200 bg-warning-50 text-warning-800 dark:border-warning-800 dark:bg-warning-900/20 dark:text-warning-200',
    iconClassName: 'text-warning-500 dark:text-warning-400',
  },
  info: {
    icon: Info,
    className: 'border-info-200 bg-info-50 text-info-800 dark:border-info-800 dark:bg-info-900/20 dark:text-info-200',
    iconClassName: 'text-info-500 dark:text-info-400',
  },
  loading: {
    icon: Loader2,
    className: 'border-primary-200 bg-primary-50 text-primary-800 dark:border-primary-800 dark:bg-primary-900/20 dark:text-primary-200',
    iconClassName: 'text-primary-500 dark:text-primary-400 animate-spin',
  },
};

export const Toast: React.FC<ToastProps> = ({
  id,
  title,
  message,
  type = 'info',
  duration = 5000,
  closable = true,
  clickable = false,
  icon,
  action,
  onClose,
  onClick,
  className,
  animated = true,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  const config = typeConfig[type];
  const IconComponent = icon || config.icon;

  useEffect(() => {
    // 进入动画
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleClose = () => {
    if (animated) {
      setIsLeaving(true);
      setTimeout(() => {
        onClose();
      }, 300);
    } else {
      onClose();
    }
  };

  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  const toastClasses = clsx(
    'relative flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm',
    'max-w-sm w-full pointer-events-auto',
    {
      // 基础样式
      [config.className]: true,
      // 动画
      'transform transition-all duration-300 ease-in-out': animated,
      'translate-x-0 opacity-100': isVisible && !isLeaving,
      'translate-x-full opacity-0': (!isVisible || isLeaving) && animated,
      // 交互
      'cursor-pointer hover:shadow-xl': clickable,
    },
    className
  );

  return (
    <div className={toastClasses} onClick={handleClick}>
      {/* 图标 */}
      <div className="flex-shrink-0 mt-0.5">
        <IconComponent
          className={clsx('h-5 w-5', config.iconClassName)}
          aria-hidden="true"
        />
      </div>

      {/* 内容 */}
      <div className="flex-1 min-w-0">
        {title && (
          <div className="text-sm font-medium leading-5 mb-1">
            {title}
          </div>
        )}
        <div className="text-sm leading-5">
          {message}
        </div>
        {action && (
          <div className="mt-3">
            <button
              onClick={(e) => {
                e.stopPropagation();
                action.onClick();
              }}
              className="text-sm font-medium underline hover:no-underline focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded"
            >
              {action.label}
            </button>
          </div>
        )}
      </div>

      {/* 关闭按钮 */}
      {closable && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleClose();
          }}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 dark:hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current transition-colors"
          aria-label="关闭"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};