/**
 * ToastContainer 通知容器组件
 * 管理所有Toast的显示和布局
 */

import React from 'react';
import { clsx } from 'clsx';
import { Toast, ToastProps } from './Toast';

export type ToastPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';

export interface ToastContainerProps {
  /** Toast列表 */
  toasts: Array<Omit<ToastProps, 'onClose'>>;
  /** 位置 */
  position?: ToastPosition;
  /** 最大显示数量 */
  maxToasts?: number;
  /** 间距 */
  spacing?: number;
  /** 自定义类名 */
  className?: string;
  /** 关闭Toast回调 */
  onClose: (id: string) => void;
  /** 点击Toast回调 */
  onClick?: (id: string) => void;
  /** z-index */
  zIndex?: number;
}

const positionClasses = {
  'top-right': 'top-4 right-4 items-end',
  'top-left': 'top-4 left-4 items-start',
  'bottom-right': 'bottom-4 right-4 items-end',
  'bottom-left': 'bottom-4 left-4 items-start',
  'top-center': 'top-4 left-1/2 transform -translate-x-1/2 items-center',
  'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2 items-center',
};

export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  position = 'top-right',
  maxToasts = 5,
  spacing = 12,
  className,
  onClose,
  onClick,
  zIndex = 9999,
}) => {
  // 限制显示数量
  const displayedToasts = toasts.slice(0, maxToasts);
  
  // 如果没有toast，不渲染容器
  if (displayedToasts.length === 0) {
    return null;
  }

  const containerClasses = clsx(
    'fixed pointer-events-none flex flex-col',
    positionClasses[position],
    className
  );

  const containerStyle: React.CSSProperties = {
    zIndex,
    gap: `${spacing}px`,
  };

  return (
    <div className={containerClasses} style={containerStyle}>
      {displayedToasts.map((toast) => (
        <Toast
          key={toast.id}
          {...toast}
          onClose={() => onClose(toast.id)}
          onClick={onClick ? () => onClick(toast.id) : undefined}
        />
      ))}
    </div>
  );
};