/**
 * ToastProvider 通知上下文提供者
 * 管理全局Toast状态和方法
 */

import React, { useState, useCallback, useRef } from 'react';
import { ToastContainer } from './ToastContainer';
import { ToastData, ToastOptions, ToastContextValue, ToastProviderProps } from './types';
import { ToastType } from './Toast';
import { ToastContext } from './useToast';

export { useToast } from './useToast';

let toastIdCounter = 0;

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  position = 'top-right',
  maxToasts = 5,
  defaultDuration = 5000,
  spacing = 12,
  className,
  zIndex = 9999,
}) => {
  const [toasts, setToasts] = useState<ToastData[]>([]);
  const toastTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const generateId = useCallback(() => {
    return `toast-${++toastIdCounter}-${Date.now()}`;
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
    
    // 清除定时器
    const timeout = toastTimeouts.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      toastTimeouts.current.delete(id);
    }
  }, []);

  const addToast = useCallback((
    message: string,
    type: ToastType = 'info',
    options: ToastOptions = {}
  ): string => {
    const id = generateId();
    const duration = options.duration ?? defaultDuration;
    
    const toast: ToastData = {
      id,
      message,
      type,
      ...(options.title && { title: options.title }),
      duration,
      closable: options.closable ?? true,
      clickable: options.clickable ?? false,
      ...(options.icon && { icon: options.icon }),
      ...(options.action && { action: options.action }),
      ...(options.onClick && { onClick: options.onClick }),
      position: options.position ?? position,
      createdAt: Date.now(),
    };

    setToasts(prev => {
      // 如果超过最大数量，移除最旧的
      const newToasts = prev.length >= maxToasts 
        ? prev.slice(-(maxToasts - 1))
        : prev;
      
      return [...newToasts, toast];
    });

    // 设置自动关闭定时器
    if (duration > 0) {
      const timeout = setTimeout(() => {
        removeToast(id);
      }, duration);
      toastTimeouts.current.set(id, timeout);
    }

    return id;
  }, [generateId, defaultDuration, position, maxToasts, removeToast]);

  const clearToasts = useCallback(() => {
    setToasts([]);
    
    // 清除所有定时器
    toastTimeouts.current.forEach(timeout => clearTimeout(timeout));
    toastTimeouts.current.clear();
  }, []);

  const updateToast = useCallback((id: string, updates: Partial<ToastData>) => {
    setToasts(prev => 
      prev.map(toast => 
        toast.id === id 
          ? { ...toast, ...updates }
          : toast
      )
    );
  }, []);

  // 快捷方法
  const success = useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'success', options);
  }, [addToast]);

  const error = useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'error', options);
  }, [addToast]);

  const warning = useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'warning', options);
  }, [addToast]);

  const info = useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'info', options);
  }, [addToast]);

  const loading = useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'loading', { ...options, duration: 0 });
  }, [addToast]);

  const contextValue: ToastContextValue = {
    toasts,
    addToast,
    removeToast,
    clearToasts,
    updateToast,
    success,
    error,
    warning,
    info,
    loading,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <ToastContainer
        toasts={toasts}
        position={position}
        maxToasts={maxToasts}
        spacing={spacing}
        {...(className && { className })}
        zIndex={zIndex}
        onClose={removeToast}
        onClick={(id) => {
          const toast = toasts.find(t => t.id === id);
          if (toast?.onClick) {
            toast.onClick();
          }
        }}
      />
    </ToastContext.Provider>
  );
};