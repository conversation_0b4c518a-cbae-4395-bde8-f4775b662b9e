/**
 * ToastShowcase 通知系统展示页面
 * 用于展示和测试Toast通知功能
 */

import React, { useState } from 'react';
import { useToast } from './useToast';
import { ToastProvider } from './ToastProvider';
import { Heart, Download, Settings } from 'lucide-react';

const ToastShowcaseContent: React.FC = () => {
  const toast = useToast();
  const [count, setCount] = useState(0);

  const handleSuccess = () => {
    toast.success('操作成功！', {
      title: '成功',
      duration: 3000,
    });
  };

  const handleError = () => {
    toast.error('操作失败，请重试', {
      title: '错误',
      duration: 5000,
    });
  };

  const handleWarning = () => {
    toast.warning('请注意，这是一个警告消息', {
      title: '警告',
      duration: 4000,
    });
  };

  const handleInfo = () => {
    toast.info('这是一条信息提示', {
      title: '提示',
      duration: 3000,
    });
  };

  const handleLoading = () => {
    const id = toast.loading('正在处理中...', {
      title: '加载中',
    });
    
    // 模拟处理完成
    setTimeout(() => {
      toast.updateToast(id, {
        type: 'success',
        message: '处理完成！',
        duration: 3000,
      });
    }, 3000);
  };

  const handleCustomIcon = () => {
    toast.success('自定义图标的通知', {
      icon: <Heart className="h-5 w-5" />,
      duration: 3000,
    });
  };

  const handleWithAction = () => {
    toast.info('文件已上传到服务器', {
      title: '上传完成',
      action: {
        label: '查看',
        onClick: () => {
          alert('查看文件');
        },
      },
      duration: 0, // 不自动关闭
    });
  };

  const handleMultiple = () => {
    const messages = [
      '第一条消息',
      '第二条消息',
      '第三条消息',
      '第四条消息',
      '第五条消息',
    ];
    
    messages.forEach((message, index) => {
      setTimeout(() => {
        toast.info(message, {
          title: `消息 ${index + 1}`,
          duration: 3000,
        });
      }, index * 500);
    });
  };

  const handleClickable = () => {
    toast.info('点击此通知查看详情', {
      title: '可点击通知',
      clickable: true,
      onClick: () => {
        alert('通知被点击了！');
      },
      duration: 0,
    });
  };

  const handleCounter = () => {
    const newCount = count + 1;
    setCount(newCount);
    toast.success(`计数器: ${newCount}`, {
      title: '计数更新',
      duration: 2000,
    });
  };

  const handleClearAll = () => {
    toast.clearToasts();
    toast.info('所有通知已清除', {
      duration: 2000,
    });
  };

  const handlePosition = (position: any) => {
    toast.info(`位置: ${position}`, {
      title: '位置测试',
      position,
      duration: 3000,
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Toast 通知系统展示
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            各种通知类型和功能的演示
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 基本类型 */}
          <div className="card p-6">
            <h2 className="text-lg font-semibold mb-4">基本类型</h2>
            <div className="space-y-3">
              <button onClick={handleSuccess} className="btn btn-success btn-sm w-full">
                成功通知
              </button>
              <button onClick={handleError} className="btn btn-danger btn-sm w-full">
                错误通知
              </button>
              <button onClick={handleWarning} className="btn bg-warning-600 hover:bg-warning-700 text-white btn-sm w-full">
                警告通知
              </button>
              <button onClick={handleInfo} className="btn bg-info-600 hover:bg-info-700 text-white btn-sm w-full">
                信息通知
              </button>
              <button onClick={handleLoading} className="btn btn-primary btn-sm w-full">
                加载通知
              </button>
            </div>
          </div>

          {/* 自定义功能 */}
          <div className="card p-6">
            <h2 className="text-lg font-semibold mb-4">自定义功能</h2>
            <div className="space-y-3">
              <button onClick={handleCustomIcon} className="btn btn-outline btn-sm w-full">
                自定义图标
              </button>
              <button onClick={handleWithAction} className="btn btn-outline btn-sm w-full">
                带操作按钮
              </button>
              <button onClick={handleClickable} className="btn btn-outline btn-sm w-full">
                可点击通知
              </button>
              <button onClick={handleCounter} className="btn btn-outline btn-sm w-full">
                计数器 ({count})
              </button>
            </div>
          </div>

          {/* 批量操作 */}
          <div className="card p-6">
            <h2 className="text-lg font-semibold mb-4">批量操作</h2>
            <div className="space-y-3">
              <button onClick={handleMultiple} className="btn btn-secondary btn-sm w-full">
                批量通知
              </button>
              <button onClick={handleClearAll} className="btn btn-outline btn-sm w-full">
                清除所有
              </button>
            </div>
          </div>

          {/* 位置测试 */}
          <div className="card p-6 md:col-span-2 lg:col-span-3">
            <h2 className="text-lg font-semibold mb-4">位置测试</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              <button 
                onClick={() => handlePosition('top-left')}
                className="btn btn-ghost btn-sm"
              >
                左上角
              </button>
              <button 
                onClick={() => handlePosition('top-center')}
                className="btn btn-ghost btn-sm"
              >
                上方中央
              </button>
              <button 
                onClick={() => handlePosition('top-right')}
                className="btn btn-ghost btn-sm"
              >
                右上角
              </button>
              <button 
                onClick={() => handlePosition('bottom-left')}
                className="btn btn-ghost btn-sm"
              >
                左下角
              </button>
              <button 
                onClick={() => handlePosition('bottom-center')}
                className="btn btn-ghost btn-sm"
              >
                下方中央
              </button>
              <button 
                onClick={() => handlePosition('bottom-right')}
                className="btn btn-ghost btn-sm"
              >
                右下角
              </button>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 card p-6">
          <h2 className="text-lg font-semibold mb-4">使用说明</h2>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <p>• 通知会在指定时间后自动关闭（loading类型除外）</p>
            <p>• 可以通过点击关闭按钮手动关闭通知</p>
            <p>• 支持自定义图标、操作按钮和点击事件</p>
            <p>• 通知数量超过最大值时会自动移除旧的通知</p>
            <p>• 支持多种位置显示和动画效果</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ToastShowcase: React.FC = () => {
  return (
    <ToastProvider maxToasts={5} defaultDuration={5000}>
      <ToastShowcaseContent />
    </ToastProvider>
  );
};