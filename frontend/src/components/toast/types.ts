/**
 * Toast 相关类型定义
 */

import { ToastType } from './Toast';

export interface ToastData {
  id: string;
  title?: string;
  message: string;
  type: ToastType;
  duration?: number;
  closable?: boolean;
  clickable?: boolean;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  onClick?: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  createdAt: number;
}

export interface ToastOptions {
  title?: string;
  duration?: number;
  closable?: boolean;
  clickable?: boolean;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  onClick?: () => void;
  position?: ToastData['position'];
}

export interface ToastContextValue {
  toasts: ToastData[];
  addToast: (message: string, type?: ToastType, options?: ToastOptions) => string;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  updateToast: (id: string, updates: Partial<ToastData>) => void;
  
  // 快捷方法
  success: (message: string, options?: ToastOptions) => string;
  error: (message: string, options?: ToastOptions) => string;
  warning: (message: string, options?: ToastOptions) => string;
  info: (message: string, options?: ToastOptions) => string;
  loading: (message: string, options?: ToastOptions) => string;
}

export interface ToastProviderProps {
  children: React.ReactNode;
  position?: ToastData['position'];
  maxToasts?: number;
  defaultDuration?: number;
  spacing?: number;
  className?: string;
  zIndex?: number;
}