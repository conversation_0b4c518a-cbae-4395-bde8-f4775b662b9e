/**
 * useToast Hook
 * 提供Toast功能的React Hook
 */

import { useContext } from 'react';
import { ToastContext } from './ToastProvider';
import { ToastContextValue } from './types';

// 重新导出useToast以便单独使用
export const useToast = (): ToastContextValue => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// 创建Toast上下文（需要在ToastProvider中使用）
import { createContext } from 'react';

export const ToastContext = createContext<ToastContextValue | null>(null);