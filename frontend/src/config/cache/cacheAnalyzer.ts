/**
 * 缓存性能分析器
 * 监控和分析React Query缓存性能
 */

import { QueryClient, QueryObserver } from '@tanstack/react-query';

// 缓存性能指标
export interface CachePerformanceMetrics {
  // 基本指标
  totalQueries: number;
  activeQueries: number;
  staleQueries: number;
  errorQueries: number;
  
  // 缓存命中率
  cacheHitRate: number;
  cacheMissRate: number;
  
  // 性能指标
  averageQueryTime: number;
  slowestQuery: QueryPerformanceInfo | null;
  fastestQuery: QueryPerformanceInfo | null;
  
  // 内存使用
  estimatedMemoryUsage: number;
  queryDataSizes: QueryDataSizeInfo[];
  
  // 网络请求
  networkRequestCount: number;
  networkErrorCount: number;
  
  // 时间戳
  timestamp: number;
}

// 查询性能信息
export interface QueryPerformanceInfo {
  queryKey: unknown[];
  queryHash: string;
  executionTime: number;
  dataSize: number;
  isStale: boolean;
  isFetching: boolean;
  errorCount: number;
  successCount: number;
  lastFetchTime: number;
}

// 查询数据大小信息
export interface QueryDataSizeInfo {
  queryKey: unknown[];
  queryHash: string;
  dataSize: number;
  percentage: number;
}

// 缓存健康状态
export interface CacheHealthStatus {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  score: number;
  issues: CacheIssue[];
  recommendations: CacheRecommendation[];
}

// 缓存问题
export interface CacheIssue {
  type: 'memory' | 'performance' | 'network' | 'stale' | 'error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedQueries: string[];
  impact: string;
}

// 缓存建议
export interface CacheRecommendation {
  type: 'optimization' | 'configuration' | 'cleanup' | 'monitoring';
  priority: 'low' | 'medium' | 'high';
  description: string;
  implementation: string;
  expectedImprovement: string;
}

// 性能监控配置
export interface PerformanceMonitorConfig {
  enableRealTimeMonitoring: boolean;
  sampleInterval: number;
  maxHistorySize: number;
  slowQueryThreshold: number;
  memoryThreshold: number;
  errorThreshold: number;
}

// 缓存性能分析器
export class CachePerformanceAnalyzer {
  private queryClient: QueryClient;
  private config: PerformanceMonitorConfig;
  private performanceHistory: CachePerformanceMetrics[] = [];
  private queryPerformanceMap: Map<string, QueryPerformanceInfo> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private observers: Map<string, QueryObserver> = new Map();

  constructor(queryClient: QueryClient, config: Partial<PerformanceMonitorConfig> = {}) {
    this.queryClient = queryClient;
    this.config = {
      enableRealTimeMonitoring: true,
      sampleInterval: 5000, // 5秒
      maxHistorySize: 100,
      slowQueryThreshold: 2000, // 2秒
      memoryThreshold: 50 * 1024 * 1024, // 50MB
      errorThreshold: 0.1, // 10%错误率
      ...config,
    };

    if (this.config.enableRealTimeMonitoring) {
      this.startRealTimeMonitoring();
    }
  }

  /**
   * 启动实时监控
   */
  private startRealTimeMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      this.collectPerformanceMetrics();
    }, this.config.sampleInterval);
  }

  /**
   * 停止实时监控
   */
  stopRealTimeMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * 收集性能指标
   */
  collectPerformanceMetrics(): CachePerformanceMetrics {
    const queries = this.queryClient.getQueryCache().getAll();
    const metrics: CachePerformanceMetrics = {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.observers.length > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      
      cacheHitRate: this.calculateCacheHitRate(queries),
      cacheMissRate: this.calculateCacheMissRate(queries),
      
      averageQueryTime: this.calculateAverageQueryTime(queries),
      slowestQuery: this.findSlowestQuery(queries),
      fastestQuery: this.findFastestQuery(queries),
      
      estimatedMemoryUsage: this.calculateMemoryUsage(queries),
      queryDataSizes: this.analyzeQueryDataSizes(queries),
      
      networkRequestCount: this.calculateNetworkRequests(queries),
      networkErrorCount: this.calculateNetworkErrors(queries),
      
      timestamp: Date.now(),
    };

    // 添加到历史记录
    this.performanceHistory.push(metrics);
    
    // 限制历史记录大小
    if (this.performanceHistory.length > this.config.maxHistorySize) {
      this.performanceHistory.shift();
    }

    return metrics;
  }

  /**
   * 计算缓存命中率
   */
  private calculateCacheHitRate(queries: any[]): number {
    let totalAccess = 0;
    let cacheHits = 0;

    queries.forEach(query => {
      const fetchCount = query.state.fetchFailureCount + query.state.dataUpdateCount;
      totalAccess += fetchCount;
      
      // 如果数据存在且不是第一次获取，则为缓存命中
      if (query.state.data !== undefined && fetchCount > 1) {
        cacheHits += fetchCount - 1;
      }
    });

    return totalAccess > 0 ? cacheHits / totalAccess : 0;
  }

  /**
   * 计算缓存未命中率
   */
  private calculateCacheMissRate(queries: any[]): number {
    return 1 - this.calculateCacheHitRate(queries);
  }

  /**
   * 计算平均查询时间
   */
  private calculateAverageQueryTime(queries: any[]): number {
    let totalTime = 0;
    let count = 0;

    queries.forEach(query => {
      const performanceInfo = this.queryPerformanceMap.get(query.queryHash);
      if (performanceInfo) {
        totalTime += performanceInfo.executionTime;
        count++;
      }
    });

    return count > 0 ? totalTime / count : 0;
  }

  /**
   * 查找最慢的查询
   */
  private findSlowestQuery(queries: any[]): QueryPerformanceInfo | null {
    let slowest: QueryPerformanceInfo | null = null;

    queries.forEach(query => {
      const performanceInfo = this.queryPerformanceMap.get(query.queryHash);
      if (performanceInfo) {
        if (!slowest || performanceInfo.executionTime > slowest.executionTime) {
          slowest = performanceInfo;
        }
      }
    });

    return slowest;
  }

  /**
   * 查找最快的查询
   */
  private findFastestQuery(queries: any[]): QueryPerformanceInfo | null {
    let fastest: QueryPerformanceInfo | null = null;

    queries.forEach(query => {
      const performanceInfo = this.queryPerformanceMap.get(query.queryHash);
      if (performanceInfo) {
        if (!fastest || performanceInfo.executionTime < fastest.executionTime) {
          fastest = performanceInfo;
        }
      }
    });

    return fastest;
  }

  /**
   * 计算内存使用量
   */
  private calculateMemoryUsage(queries: any[]): number {
    return queries.reduce((total, query) => {
      const dataSize = this.calculateDataSize(query.state.data);
      return total + dataSize;
    }, 0);
  }

  /**
   * 分析查询数据大小
   */
  private analyzeQueryDataSizes(queries: any[]): QueryDataSizeInfo[] {
    const dataSizes: QueryDataSizeInfo[] = [];
    const totalSize = this.calculateMemoryUsage(queries);

    queries.forEach(query => {
      const dataSize = this.calculateDataSize(query.state.data);
      dataSizes.push({
        queryKey: query.queryKey,
        queryHash: query.queryHash,
        dataSize,
        percentage: totalSize > 0 ? (dataSize / totalSize) * 100 : 0,
      });
    });

    return dataSizes.sort((a, b) => b.dataSize - a.dataSize);
  }

  /**
   * 计算数据大小
   */
  private calculateDataSize(data: any): number {
    if (data === null || data === undefined) {
      return 0;
    }

    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }

  /**
   * 计算网络请求数量
   */
  private calculateNetworkRequests(queries: any[]): number {
    return queries.reduce((total, query) => {
      return total + (query.state.dataUpdateCount || 0);
    }, 0);
  }

  /**
   * 计算网络错误数量
   */
  private calculateNetworkErrors(queries: any[]): number {
    return queries.reduce((total, query) => {
      return total + (query.state.fetchFailureCount || 0);
    }, 0);
  }

  /**
   * 分析缓存健康状态
   */
  analyzeCacheHealth(): CacheHealthStatus {
    const metrics = this.collectPerformanceMetrics();
    const issues: CacheIssue[] = [];
    const recommendations: CacheRecommendation[] = [];

    // 检查内存使用
    if (metrics.estimatedMemoryUsage > this.config.memoryThreshold) {
      issues.push({
        type: 'memory',
        severity: 'high',
        description: '缓存内存使用量过高',
        affectedQueries: metrics.queryDataSizes.slice(0, 5).map(q => q.queryHash),
        impact: '可能导致页面卡顿和内存溢出',
      });

      recommendations.push({
        type: 'cleanup',
        priority: 'high',
        description: '清理不必要的缓存数据',
        implementation: '减少cacheTime或使用removeUnusedData',
        expectedImprovement: '减少内存使用量50%',
      });
    }

    // 检查查询性能
    if (metrics.averageQueryTime > this.config.slowQueryThreshold) {
      issues.push({
        type: 'performance',
        severity: 'medium',
        description: '查询平均响应时间过长',
        affectedQueries: [metrics.slowestQuery?.queryHash || ''],
        impact: '影响用户体验',
      });

      recommendations.push({
        type: 'optimization',
        priority: 'medium',
        description: '优化慢查询',
        implementation: '增加staleTime或使用数据预取',
        expectedImprovement: '减少查询时间30%',
      });
    }

    // 检查错误率
    const errorRate = metrics.errorQueries / metrics.totalQueries;
    if (errorRate > this.config.errorThreshold) {
      issues.push({
        type: 'error',
        severity: 'high',
        description: '查询错误率过高',
        affectedQueries: [],
        impact: '影响功能可用性',
      });

      recommendations.push({
        type: 'monitoring',
        priority: 'high',
        description: '加强错误监控和重试机制',
        implementation: '增加retry次数和错误处理',
        expectedImprovement: '减少错误率80%',
      });
    }

    // 检查缓存命中率
    if (metrics.cacheHitRate < 0.5) {
      issues.push({
        type: 'performance',
        severity: 'medium',
        description: '缓存命中率过低',
        affectedQueries: [],
        impact: '增加不必要的网络请求',
      });

      recommendations.push({
        type: 'configuration',
        priority: 'medium',
        description: '优化缓存配置',
        implementation: '增加staleTime和cacheTime',
        expectedImprovement: '提高缓存命中率至70%',
      });
    }

    // 计算总体健康分数
    const score = this.calculateHealthScore(metrics, issues);
    const overall = this.determineOverallHealth(score);

    return {
      overall,
      score,
      issues,
      recommendations,
    };
  }

  /**
   * 计算健康分数
   */
  private calculateHealthScore(metrics: CachePerformanceMetrics, issues: CacheIssue[]): number {
    let score = 100;

    // 根据问题严重程度扣分
    issues.forEach(issue => {
      switch (issue.severity) {
        case 'critical':
          score -= 30;
          break;
        case 'high':
          score -= 20;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    // 根据指标表现调整分数
    if (metrics.cacheHitRate > 0.8) score += 10;
    if (metrics.averageQueryTime < 500) score += 5;
    if (metrics.errorQueries === 0) score += 5;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 确定总体健康状态
   */
  private determineOverallHealth(score: number): CacheHealthStatus['overall'] {
    if (score >= 90) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    return 'poor';
  }

  /**
   * 获取性能历史
   */
  getPerformanceHistory(): CachePerformanceMetrics[] {
    return [...this.performanceHistory];
  }

  /**
   * 获取查询性能详情
   */
  getQueryPerformanceDetails(queryHash?: string): QueryPerformanceInfo[] {
    if (queryHash) {
      const info = this.queryPerformanceMap.get(queryHash);
      return info ? [info] : [];
    }

    return Array.from(this.queryPerformanceMap.values());
  }

  /**
   * 导出性能报告
   */
  exportPerformanceReport(): string {
    const currentMetrics = this.collectPerformanceMetrics();
    const healthStatus = this.analyzeCacheHealth();

    const report = {
      timestamp: Date.now(),
      currentMetrics,
      healthStatus,
      performanceHistory: this.performanceHistory,
      queryPerformanceDetails: this.getQueryPerformanceDetails(),
    };

    return JSON.stringify(report, null, 2);
  }

  /**
   * 清理性能数据
   */
  clearPerformanceData(): void {
    this.performanceHistory = [];
    this.queryPerformanceMap.clear();
  }

  /**
   * 销毁分析器
   */
  destroy(): void {
    this.stopRealTimeMonitoring();
    this.clearPerformanceData();
    this.observers.forEach(observer => observer.destroy());
    this.observers.clear();
  }
}

// 性能分析工具函数
export const performanceAnalysisUtils = {
  /**
   * 格式化文件大小
   */
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 格式化时间
   */
  formatTime(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    } else if (milliseconds < 60000) {
      return `${(milliseconds / 1000).toFixed(2)}s`;
    } else {
      return `${(milliseconds / 60000).toFixed(2)}min`;
    }
  },

  /**
   * 格式化百分比
   */
  formatPercentage(value: number): string {
    return `${(value * 100).toFixed(2)}%`;
  },

  /**
   * 获取性能等级颜色
   */
  getPerformanceColor(score: number): string {
    if (score >= 90) return '#4CAF50'; // 绿色
    if (score >= 70) return '#8BC34A'; // 浅绿
    if (score >= 50) return '#FFC107'; // 黄色
    return '#F44336'; // 红色
  },

  /**
   * 分析查询键模式
   */
  analyzeQueryKeyPatterns(queries: QueryPerformanceInfo[]): Map<string, number> {
    const patterns = new Map<string, number>();

    queries.forEach(query => {
      const pattern = query.queryKey[0] as string;
      patterns.set(pattern, (patterns.get(pattern) || 0) + 1);
    });

    return patterns;
  },

  /**
   * 计算趋势
   */
  calculateTrend(history: CachePerformanceMetrics[], field: keyof CachePerformanceMetrics): 'up' | 'down' | 'stable' {
    if (history.length < 2) return 'stable';

    const recent = history.slice(-3);
    const values = recent.map(h => h[field] as number);
    
    const trend = values[values.length - 1] - values[0];
    const threshold = Math.abs(values[0] * 0.1); // 10%阈值

    if (trend > threshold) return 'up';
    if (trend < -threshold) return 'down';
    return 'stable';
  },
};

// 性能分析钩子
export const useCachePerformanceAnalyzer = (queryClient: QueryClient) => {
  const analyzer = new CachePerformanceAnalyzer(queryClient);

  return {
    analyzer,
    
    // 收集指标
    collectMetrics: () => analyzer.collectPerformanceMetrics(),
    
    // 健康检查
    analyzeHealth: () => analyzer.analyzeCacheHealth(),
    
    // 获取历史数据
    getHistory: () => analyzer.getPerformanceHistory(),
    
    // 查询详情
    getQueryDetails: (queryHash?: string) => analyzer.getQueryPerformanceDetails(queryHash),
    
    // 导出报告
    exportReport: () => analyzer.exportPerformanceReport(),
    
    // 工具函数
    utils: performanceAnalysisUtils,
    
    // 清理
    clearData: () => analyzer.clearPerformanceData(),
    destroy: () => analyzer.destroy(),
  };
};

export default CachePerformanceAnalyzer;