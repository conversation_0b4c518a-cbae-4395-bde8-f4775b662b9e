/**
 * React Query 缓存配置
 * 统一管理不同类型数据的缓存策略
 */

import { QueryClient, DefaultOptions } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/query-persist-client-core';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';

// 缓存策略配置
export const CACHE_STRATEGIES = {
  // 用户会话数据 - 长期缓存
  USER_SESSION: {
    staleTime: 15 * 60 * 1000, // 15分钟
    gcTime: 30 * 60 * 1000, // 30分钟（替代cacheTime）
    retry: 3,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
  
  // 聊天消息 - 短期缓存，实时性要求高
  CHAT_MESSAGES: {
    staleTime: 30 * 1000, // 30秒
    gcTime: 5 * 60 * 1000, // 5分钟
    retry: 2,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  },
  
  // 文档列表 - 中期缓存
  DOCUMENT_LIST: {
    staleTime: 2 * 60 * 1000, // 2分钟
    gcTime: 10 * 60 * 1000, // 10分钟
    retry: 3,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
  
  // 文档内容 - 长期缓存
  DOCUMENT_CONTENT: {
    staleTime: 30 * 60 * 1000, // 30分钟
    gcTime: 60 * 60 * 1000, // 1小时
    retry: 3,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  },
  
  // 搜索结果 - 短期缓存
  SEARCH_RESULTS: {
    staleTime: 1 * 60 * 1000, // 1分钟
    gcTime: 5 * 60 * 1000, // 5分钟
    retry: 2,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
  
  // 系统配置 - 长期缓存
  SYSTEM_CONFIG: {
    staleTime: 60 * 60 * 1000, // 1小时
    gcTime: 24 * 60 * 60 * 1000, // 24小时
    retry: 3,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
} as const;

// 默认查询配置
export const DEFAULT_QUERY_OPTIONS: DefaultOptions = {
  queries: {
    // 全局默认设置
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 10 * 60 * 1000, // 10分钟
    retry: 3,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    
    // 网络错误处理
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    
    // 开发环境配置
    ...(process.env.NODE_ENV === 'development' && {
      staleTime: 0, // 开发环境不使用缓存
      gcTime: 0,
    }),
  },
  mutations: {
    retry: 1,
    retryDelay: 1000,
  },
};

// 创建本地存储持久化器
export const createPersister = () => {
  return createSyncStoragePersister({
    storage: window.localStorage,
    key: 'REACT_QUERY_CACHE',
    serialize: JSON.stringify,
    deserialize: JSON.parse,
  });
};

// 需要持久化的查询键模式
export const PERSISTABLE_QUERY_PATTERNS = [
  ['documents'], // 文档相关
  ['chat', 'sessions'], // 聊天会话
  ['user', 'preferences'], // 用户偏好
  ['system', 'config'], // 系统配置
];

// 创建优化的QueryClient
export const createOptimizedQueryClient = (options?: {
  enablePersistence?: boolean;
  enableDevtools?: boolean;
}): QueryClient => {
  const { enablePersistence = true, enableDevtools = false } = options || {};
  
  const queryClient = new QueryClient({
    defaultOptions: DEFAULT_QUERY_OPTIONS,
  });

  // 启用持久化
  if (enablePersistence) {
    const persister = createPersister();
    
    persistQueryClient({
      queryClient,
      persister,
      maxAge: 24 * 60 * 60 * 1000, // 24小时
    });
  }

  // 开发工具
  if (enableDevtools && process.env.NODE_ENV === 'development') {
    // React Query DevTools 将自动检测
    console.log('React Query DevTools enabled');
  }

  return queryClient;
};

// 缓存管理工具
export class CacheManager {
  private queryClient: QueryClient;

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * 清除所有缓存
   */
  clearAll(): void {
    this.queryClient.clear();
  }

  /**
   * 清除特定查询的缓存
   */
  clearQuery(queryKey: unknown[]): void {
    this.queryClient.removeQueries({
      queryKey,
    });
  }

  /**
   * 清除匹配模式的缓存
   */
  clearQueriesByPattern(pattern: unknown[]): void {
    this.queryClient.removeQueries({
      predicate: (query) => {
        return pattern.every((key, index) => query.queryKey[index] === key);
      },
    });
  }

  /**
   * 使查询失效
   */
  invalidateQuery(queryKey: unknown[]): void {
    this.queryClient.invalidateQueries({ queryKey });
  }

  /**
   * 预取数据
   */
  async prefetchQuery<T>(
    queryKey: unknown[],
    queryFn: () => Promise<T>,
    options?: { staleTime?: number }
  ): Promise<void> {
    await this.queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: options?.staleTime || CACHE_STRATEGIES.DOCUMENT_CONTENT.staleTime,
    });
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    totalQueries: number;
    staleQueries: number;
    activeQueries: number;
    cacheSize: number;
  } {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      activeQueries: queries.filter(q => q.observers.length > 0).length,
      cacheSize: this.calculateCacheSize(queries),
    };
  }

  /**
   * 计算缓存大小（估算）
   */
  private calculateCacheSize(queries: any[]): number {
    return queries.reduce((total, query) => {
      const dataString = JSON.stringify(query.state.data);
      return total + (dataString ? dataString.length : 0);
    }, 0);
  }

  /**
   * 导出缓存数据
   */
  exportCache(): string {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const exportData = queries.map(query => ({
      queryKey: query.queryKey,
      data: query.state.data,
      dataUpdatedAt: query.state.dataUpdatedAt,
      isStale: query.isStale(),
    }));

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    this.queryClient.getQueryCache().clear();
  }
}

// 缓存键工厂
export class QueryKeyFactory {
  // 聊天相关
  static chat = {
    all: () => ['chat'] as const,
    sessions: () => ['chat', 'sessions'] as const,
    session: (id: string) => ['chat', 'sessions', id] as const,
    messages: (sessionId: string) => ['chat', 'messages', sessionId] as const,
    message: (sessionId: string, messageId: string) => ['chat', 'messages', sessionId, messageId] as const,
  };

  // 文档相关
  static documents = {
    all: () => ['documents'] as const,
    lists: () => ['documents', 'list'] as const,
    list: (filters?: Record<string, any>) => ['documents', 'list', filters] as const,
    details: () => ['documents', 'detail'] as const,
    detail: (id: string) => ['documents', 'detail', id] as const,
    content: (id: string) => ['documents', 'content', id] as const,
    search: (query: string) => ['documents', 'search', query] as const,
  };

  // 用户相关
  static user = {
    all: () => ['user'] as const,
    profile: () => ['user', 'profile'] as const,
    preferences: () => ['user', 'preferences'] as const,
    sessions: () => ['user', 'sessions'] as const,
  };

  // 系统相关
  static system = {
    all: () => ['system'] as const,
    config: () => ['system', 'config'] as const,
    health: () => ['system', 'health'] as const,
    stats: () => ['system', 'stats'] as const,
  };
}

// 预取策略配置
export const PREFETCH_STRATEGIES = {
  // 用户进入聊天页面时预取会话列表
  CHAT_PAGE_ENTRY: {
    queryKey: QueryKeyFactory.chat.sessions(),
    priority: 'high' as const,
    condition: (route: string) => route.includes('/chat'),
  },
  
  // 选择会话时预取消息历史
  CHAT_SESSION_SELECT: {
    queryKey: (sessionId: string) => QueryKeyFactory.chat.messages(sessionId),
    priority: 'high' as const,
    condition: (sessionId: string) => !!sessionId,
  },
  
  // 文档页面预取文档列表
  DOCUMENT_PAGE_ENTRY: {
    queryKey: QueryKeyFactory.documents.list(),
    priority: 'medium' as const,
    condition: (route: string) => route.includes('/documents'),
  },
  
  // 鼠标悬停时预取文档内容
  DOCUMENT_HOVER: {
    queryKey: (documentId: string) => QueryKeyFactory.documents.detail(documentId),
    priority: 'low' as const,
    delay: 500, // 500ms延迟
  },
} as const;

export default {
  CACHE_STRATEGIES,
  DEFAULT_QUERY_OPTIONS,
  createOptimizedQueryClient,
  CacheManager,
  QueryKeyFactory,
  PREFETCH_STRATEGIES,
};