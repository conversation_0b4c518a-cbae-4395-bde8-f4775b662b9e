/**
 * React Query 缓存配置模块
 * 统一导出所有缓存相关功能
 */

// 核心配置
export {
  CACHE_STRATEGIES,
  DEFAULT_QUERY_OPTIONS,
  createOptimizedQueryClient,
  CacheManager,
  QueryKeyFactory,
  PREFETCH_STRATEGIES,
  createPersister,
  PERSISTABLE_QUERY_PATTERNS,
} from './cacheConfig';

// 乐观更新
export {
  OptimisticUpdateManager,
  optimisticUpdateUtils,
  createOptimisticUpdateHook,
} from './optimisticUpdates';

// 预取策略
export {
  PrefetchStrategyManager,
  createPredefinedStrategies,
  usePrefetchStrategy,
} from './prefetchStrategy';

// 离线缓存
export {
  OfflineCacheManager,
  offlineCacheUtils,
  useOfflineCache,
} from './offlineCache';

// 性能分析
export {
  CachePerformanceAnalyzer,
  performanceAnalysisUtils,
  useCachePerformanceAnalyzer,
} from './cacheAnalyzer';

// 类型定义
export type {
  // 缓存配置类型
  OptimisticUpdateContext,
  OptimisticUpdateConfig,
  
  // 预取策略类型
  PrefetchPriority,
  PrefetchConfig,
  PrefetchContext,
  PrefetchResult,
  
  // 离线缓存类型
  OfflineStatus,
  OfflineOperation,
  OfflineStorageConfig,
  OfflineCacheEvent,
  OfflineCacheStats,
  
  // 性能分析类型
  CachePerformanceMetrics,
  QueryPerformanceInfo,
  QueryDataSizeInfo,
  CacheHealthStatus,
  CacheIssue,
  CacheRecommendation,
  PerformanceMonitorConfig,
} from './cacheConfig';

// 综合缓存管理类
export class ComprehensiveCacheManager {
  private queryClient: any;
  private cacheManager: any;
  private optimisticUpdateManager: any;
  private prefetchManager: any;
  private offlineCacheManager: any;
  private performanceAnalyzer: any;

  constructor(queryClient: any) {
    this.queryClient = queryClient;
    this.initializeManagers();
  }

  /**
   * 初始化所有管理器
   */
  private initializeManagers(): void {
    // 导入时可能会有循环依赖，所以在运行时动态导入
    import('./cacheConfig').then(({ CacheManager }) => {
      this.cacheManager = new CacheManager(this.queryClient);
    });

    import('./optimisticUpdates').then(({ OptimisticUpdateManager }) => {
      this.optimisticUpdateManager = new OptimisticUpdateManager(this.queryClient);
    });

    import('./prefetchStrategy').then(({ PrefetchStrategyManager }) => {
      this.prefetchManager = new PrefetchStrategyManager(this.queryClient);
    });

    import('./offlineCache').then(({ OfflineCacheManager }) => {
      this.offlineCacheManager = new OfflineCacheManager(this.queryClient);
    });

    import('./cacheAnalyzer').then(({ CachePerformanceAnalyzer }) => {
      this.performanceAnalyzer = new CachePerformanceAnalyzer(this.queryClient);
    });
  }

  /**
   * 获取缓存管理器
   */
  get cache() {
    return this.cacheManager;
  }

  /**
   * 获取乐观更新管理器
   */
  get optimisticUpdates() {
    return this.optimisticUpdateManager;
  }

  /**
   * 获取预取管理器
   */
  get prefetch() {
    return this.prefetchManager;
  }

  /**
   * 获取离线缓存管理器
   */
  get offline() {
    return this.offlineCacheManager;
  }

  /**
   * 获取性能分析器
   */
  get performance() {
    return this.performanceAnalyzer;
  }

  /**
   * 获取综合缓存状态
   */
  getComprehensiveStatus(): {
    cache: any;
    offline: any;
    performance: any;
  } {
    return {
      cache: this.cacheManager?.getCacheStats(),
      offline: this.offlineCacheManager?.getOfflineCacheStats(),
      performance: this.performanceAnalyzer?.collectPerformanceMetrics(),
    };
  }

  /**
   * 执行全面的缓存优化
   */
  async optimizeCache(): Promise<void> {
    // 清理过期缓存
    if (this.cacheManager) {
      this.cacheManager.cleanup();
    }

    // 同步离线操作
    if (this.offlineCacheManager) {
      await this.offlineCacheManager.syncOfflineOperations();
    }

    // 执行智能预取
    if (this.prefetchManager) {
      await this.prefetchManager.executePrefetchQueue();
    }

    console.log('缓存优化完成');
  }

  /**
   * 导出完整的缓存报告
   */
  exportComprehensiveReport(): string {
    const report = {
      timestamp: Date.now(),
      cache: this.cacheManager?.exportCache(),
      offline: this.offlineCacheManager?.getOfflineCacheStats(),
      performance: this.performanceAnalyzer?.exportPerformanceReport(),
    };

    return JSON.stringify(report, null, 2);
  }

  /**
   * 销毁所有管理器
   */
  destroy(): void {
    this.cacheManager?.cleanup();
    this.offlineCacheManager?.destroy();
    this.performanceAnalyzer?.destroy();
    
    console.log('缓存管理器已销毁');
  }
}

// 默认导出
export default ComprehensiveCacheManager;