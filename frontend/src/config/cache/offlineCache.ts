/**
 * 离线缓存管理器
 * 提供离线状态下的数据访问和同步功能
 */

import { QueryClient } from '@tanstack/react-query';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { QueryKeyFactory } from './cacheConfig';

// 离线状态类型
export type OfflineStatus = 'online' | 'offline' | 'unknown';

// 离线操作类型
export interface OfflineOperation {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  endpoint: string;
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  queryKey?: unknown[];
}

// 离线存储配置
export interface OfflineStorageConfig {
  storageKey: string;
  maxOperations: number;
  maxRetries: number;
  syncInterval: number;
  enableCompression: boolean;
}

// 离线缓存事件
export interface OfflineCacheEvent {
  type: 'statusChange' | 'operationQueued' | 'operationSynced' | 'operationFailed';
  data: any;
  timestamp: number;
}

// 离线缓存统计
export interface OfflineCacheStats {
  isOnline: boolean;
  queuedOperations: number;
  failedOperations: number;
  lastSyncTime: number;
  cacheSize: number;
  compressionRatio: number;
}

// 离线缓存管理器
export class OfflineCacheManager {
  private queryClient: QueryClient;
  private config: OfflineStorageConfig;
  private isOnline: boolean = navigator.onLine;
  private operationQueue: OfflineOperation[] = [];
  private eventListeners: Map<string, (event: OfflineCacheEvent) => void> = new Map();
  private syncInterval: NodeJS.Timeout | null = null;
  private persister: any;

  constructor(queryClient: QueryClient, config: Partial<OfflineStorageConfig> = {}) {
    this.queryClient = queryClient;
    this.config = {
      storageKey: 'offline_cache',
      maxOperations: 100,
      maxRetries: 3,
      syncInterval: 30000, // 30秒
      enableCompression: true,
      ...config,
    };

    this.initializePersister();
    this.setupEventListeners();
    this.loadOfflineOperations();
    this.startSyncInterval();
  }

  /**
   * 初始化持久化存储
   */
  private initializePersister(): void {
    this.persister = createSyncStoragePersister({
      storage: window.localStorage,
      key: this.config.storageKey,
      serialize: this.config.enableCompression ? this.compress : JSON.stringify,
      deserialize: this.config.enableCompression ? this.decompress : JSON.parse,
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听网络状态变化
    const handleOnline = () => {
      this.isOnline = true;
      this.emitEvent('statusChange', { isOnline: true });
      this.syncOfflineOperations();
    };

    const handleOffline = () => {
      this.isOnline = false;
      this.emitEvent('statusChange', { isOnline: false });
    };

    const handleBeforeUnload = () => {
      this.saveOfflineOperations();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('beforeunload', handleBeforeUnload);
  }

  /**
   * 加载离线操作
   */
  private loadOfflineOperations(): void {
    try {
      const stored = localStorage.getItem(`${this.config.storageKey}_operations`);
      if (stored) {
        this.operationQueue = JSON.parse(stored);
      }
    } catch (error) {
      console.error('加载离线操作失败:', error);
    }
  }

  /**
   * 保存离线操作
   */
  private saveOfflineOperations(): void {
    try {
      localStorage.setItem(
        `${this.config.storageKey}_operations`,
        JSON.stringify(this.operationQueue)
      );
    } catch (error) {
      console.error('保存离线操作失败:', error);
    }
  }

  /**
   * 启动同步间隔
   */
  private startSyncInterval(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline && this.operationQueue.length > 0) {
        this.syncOfflineOperations();
      }
    }, this.config.syncInterval);
  }

  /**
   * 添加离线操作到队列
   */
  addOfflineOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): void {
    const offlineOperation: OfflineOperation = {
      ...operation,
      id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.operationQueue.push(offlineOperation);
    
    // 限制队列大小
    if (this.operationQueue.length > this.config.maxOperations) {
      this.operationQueue.shift();
    }

    this.saveOfflineOperations();
    this.emitEvent('operationQueued', offlineOperation);
  }

  /**
   * 同步离线操作
   */
  async syncOfflineOperations(): Promise<void> {
    if (!this.isOnline || this.operationQueue.length === 0) {
      return;
    }

    const operationsToSync = [...this.operationQueue];
    
    for (const operation of operationsToSync) {
      try {
        await this.executeOfflineOperation(operation);
        
        // 成功后从队列中移除
        this.operationQueue = this.operationQueue.filter(op => op.id !== operation.id);
        this.emitEvent('operationSynced', operation);
        
      } catch (error) {
        console.error('同步离线操作失败:', error);
        
        // 增加重试次数
        operation.retryCount++;
        
        if (operation.retryCount >= operation.maxRetries) {
          // 达到最大重试次数，从队列中移除
          this.operationQueue = this.operationQueue.filter(op => op.id !== operation.id);
          this.emitEvent('operationFailed', { operation, error });
        }
      }
    }

    this.saveOfflineOperations();
  }

  /**
   * 执行离线操作
   */
  private async executeOfflineOperation(operation: OfflineOperation): Promise<void> {
    const { type, endpoint, data, queryKey } = operation;
    
    switch (type) {
      case 'CREATE':
        await this.performCreateOperation(endpoint, data);
        break;
      case 'UPDATE':
        await this.performUpdateOperation(endpoint, data);
        break;
      case 'DELETE':
        await this.performDeleteOperation(endpoint, data);
        break;
    }

    // 同步成功后，使相关查询失效
    if (queryKey) {
      this.queryClient.invalidateQueries({ queryKey });
    }
  }

  /**
   * 执行创建操作
   */
  private async performCreateOperation(endpoint: string, data: any): Promise<void> {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  /**
   * 执行更新操作
   */
  private async performUpdateOperation(endpoint: string, data: any): Promise<void> {
    const response = await fetch(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  /**
   * 执行删除操作
   */
  private async performDeleteOperation(endpoint: string, data: any): Promise<void> {
    const response = await fetch(endpoint, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  /**
   * 压缩数据
   */
  private compress(data: any): string {
    try {
      const jsonString = JSON.stringify(data);
      // 简单的压缩算法（实际应用中可使用更好的压缩算法）
      return btoa(jsonString);
    } catch (error) {
      console.error('数据压缩失败:', error);
      return JSON.stringify(data);
    }
  }

  /**
   * 解压数据
   */
  private decompress(compressedData: string): any {
    try {
      const jsonString = atob(compressedData);
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('数据解压失败:', error);
      return JSON.parse(compressedData);
    }
  }

  /**
   * 发送事件
   */
  private emitEvent(type: OfflineCacheEvent['type'], data: any): void {
    const event: OfflineCacheEvent = {
      type,
      data,
      timestamp: Date.now(),
    };

    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('事件监听器执行失败:', error);
      }
    });
  }

  /**
   * 添加事件监听器
   */
  addEventListener(id: string, listener: (event: OfflineCacheEvent) => void): void {
    this.eventListeners.set(id, listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(id: string): void {
    this.eventListeners.delete(id);
  }

  /**
   * 获取离线状态
   */
  getOfflineStatus(): OfflineStatus {
    return this.isOnline ? 'online' : 'offline';
  }

  /**
   * 获取离线缓存统计
   */
  getOfflineCacheStats(): OfflineCacheStats {
    const cacheSize = this.calculateCacheSize();
    const compressionRatio = this.calculateCompressionRatio();
    
    return {
      isOnline: this.isOnline,
      queuedOperations: this.operationQueue.length,
      failedOperations: this.operationQueue.filter(op => op.retryCount >= op.maxRetries).length,
      lastSyncTime: this.getLastSyncTime(),
      cacheSize,
      compressionRatio,
    };
  }

  /**
   * 计算缓存大小
   */
  private calculateCacheSize(): number {
    try {
      const stored = localStorage.getItem(this.config.storageKey);
      return stored ? stored.length : 0;
    } catch {
      return 0;
    }
  }

  /**
   * 计算压缩比率
   */
  private calculateCompressionRatio(): number {
    if (!this.config.enableCompression) {
      return 1;
    }

    try {
      const originalSize = JSON.stringify(this.operationQueue).length;
      const compressedSize = this.compress(this.operationQueue).length;
      return compressedSize / originalSize;
    } catch {
      return 1;
    }
  }

  /**
   * 获取最后同步时间
   */
  private getLastSyncTime(): number {
    try {
      const stored = localStorage.getItem(`${this.config.storageKey}_lastSync`);
      return stored ? parseInt(stored) : 0;
    } catch {
      return 0;
    }
  }

  /**
   * 清理离线缓存
   */
  clearOfflineCache(): void {
    this.operationQueue = [];
    this.saveOfflineOperations();
    localStorage.removeItem(this.config.storageKey);
    localStorage.removeItem(`${this.config.storageKey}_lastSync`);
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.saveOfflineOperations();
    this.eventListeners.clear();
    
    window.removeEventListener('online', this.syncOfflineOperations);
    window.removeEventListener('offline', () => {});
    window.removeEventListener('beforeunload', this.saveOfflineOperations);
  }
}

// 离线缓存工具函数
export const offlineCacheUtils = {
  /**
   * 检查是否支持离线缓存
   */
  isSupported(): boolean {
    return 'localStorage' in window && 'navigator' in window && 'onLine' in navigator;
  },

  /**
   * 获取网络信息
   */
  getNetworkInfo(): {
    isOnline: boolean;
    connectionType?: string;
    effectiveType?: string;
    downlink?: number;
    rtt?: number;
  } {
    const connection = (navigator as any).connection;
    
    return {
      isOnline: navigator.onLine,
      connectionType: connection?.type,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt,
    };
  },

  /**
   * 估算数据传输时间
   */
  estimateTransferTime(dataSize: number): number {
    const connection = (navigator as any).connection;
    if (!connection || !connection.downlink) {
      return 1000; // 默认1秒
    }

    // 计算传输时间（毫秒）
    const transferTime = (dataSize / (connection.downlink * 125)) * 1000;
    return Math.max(transferTime, 100); // 最少100ms
  },

  /**
   * 判断是否应该使用离线缓存
   */
  shouldUseOfflineCache(dataSize: number): boolean {
    const networkInfo = this.getNetworkInfo();
    
    if (!networkInfo.isOnline) {
      return true;
    }

    // 慢速网络或大数据时使用离线缓存
    if (networkInfo.effectiveType === '2g' || networkInfo.effectiveType === '3g') {
      return true;
    }

    // 数据量大时使用离线缓存
    if (dataSize > 1024 * 1024) { // 1MB
      return true;
    }

    return false;
  },
};

// 离线缓存钩子
export const useOfflineCache = (queryClient: QueryClient) => {
  const manager = new OfflineCacheManager(queryClient);

  return {
    manager,
    
    // 添加离线操作
    addOfflineOperation: (operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>) => {
      manager.addOfflineOperation(operation);
    },
    
    // 同步操作
    syncOfflineOperations: () => manager.syncOfflineOperations(),
    
    // 状态查询
    getOfflineStatus: () => manager.getOfflineStatus(),
    getOfflineCacheStats: () => manager.getOfflineCacheStats(),
    
    // 事件监听
    addEventListener: (id: string, listener: (event: OfflineCacheEvent) => void) => {
      manager.addEventListener(id, listener);
    },
    
    removeEventListener: (id: string) => {
      manager.removeEventListener(id);
    },
    
    // 清理
    clearOfflineCache: () => manager.clearOfflineCache(),
    
    // 工具函数
    utils: offlineCacheUtils,
  };
};

export default OfflineCacheManager;