/**
 * 乐观更新管理器
 * 提供即时UI反馈的数据更新机制
 */

import { QueryClient, InfiniteData } from '@tanstack/react-query';
import { QueryKeyFactory } from './cacheConfig';
import {
  ChatMessageResponse,
  ChatSessionResponse,
  DocumentResponse,
  ChatRequest,
  ChatResponse,
} from '../../types';

// 乐观更新上下文
export interface OptimisticUpdateContext {
  queryClient: QueryClient;
  rollbackData?: any;
  updateId: string;
}

// 乐观更新配置
export interface OptimisticUpdateConfig<TData = any, TError = any> {
  queryKey: unknown[];
  updater: (oldData: TData) => TData;
  onError?: (error: TError, context: OptimisticUpdateContext) => void;
  onSuccess?: (data: TData, context: OptimisticUpdateContext) => void;
  onSettled?: (data: TData | undefined, error: TError | null, context: OptimisticUpdateContext) => void;
}

// 乐观更新管理器
export class OptimisticUpdateManager {
  private queryClient: QueryClient;
  private updateCounter = 0;

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * 执行乐观更新
   */
  async executeOptimisticUpdate<TData = any, TError = any>(
    config: OptimisticUpdateConfig<TData, TError>
  ): Promise<OptimisticUpdateContext> {
    const updateId = `optimistic_${++this.updateCounter}_${Date.now()}`;
    
    // 获取旧数据作为回滚数据
    const rollbackData = this.queryClient.getQueryData(config.queryKey);
    
    // 执行乐观更新
    this.queryClient.setQueryData(config.queryKey, config.updater);
    
    const context: OptimisticUpdateContext = {
      queryClient: this.queryClient,
      rollbackData,
      updateId,
    };

    return context;
  }

  /**
   * 回滚乐观更新
   */
  rollbackOptimisticUpdate(context: OptimisticUpdateContext, queryKey: unknown[]): void {
    if (context.rollbackData !== undefined) {
      this.queryClient.setQueryData(queryKey, context.rollbackData);
    }
  }

  /**
   * 聊天消息乐观更新
   */
  optimisticChatMessage = {
    /**
     * 添加消息到聊天历史
     */
    addMessage: (sessionId: string, message: Omit<ChatMessageResponse, 'id' | 'timestamp'>) => {
      const queryKey = QueryKeyFactory.chat.messages(sessionId);
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { messages: ChatMessageResponse[] } | undefined) => {
          const optimisticMessage: ChatMessageResponse = {
            ...message,
            id: `temp_${Date.now()}`,
            timestamp: new Date().toISOString(),
          };

          return {
            messages: [...(oldData?.messages || []), optimisticMessage],
          };
        },
      });
    },

    /**
     * 更新消息状态
     */
    updateMessage: (sessionId: string, messageId: string, updates: Partial<ChatMessageResponse>) => {
      const queryKey = QueryKeyFactory.chat.messages(sessionId);
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { messages: ChatMessageResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            messages: oldData.messages.map(msg => 
              msg.id === messageId ? { ...msg, ...updates } : msg
            ),
          };
        },
      });
    },

    /**
     * 删除消息
     */
    deleteMessage: (sessionId: string, messageId: string) => {
      const queryKey = QueryKeyFactory.chat.messages(sessionId);
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { messages: ChatMessageResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            messages: oldData.messages.filter(msg => msg.id !== messageId),
          };
        },
      });
    },

    /**
     * 流式消息更新
     */
    updateStreamingMessage: (sessionId: string, messageId: string, chunk: string) => {
      const queryKey = QueryKeyFactory.chat.messages(sessionId);
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { messages: ChatMessageResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            messages: oldData.messages.map(msg => {
              if (msg.id === messageId) {
                return {
                  ...msg,
                  content: msg.content + chunk,
                };
              }
              return msg;
            }),
          };
        },
      });
    },
  };

  /**
   * 聊天会话乐观更新
   */
  optimisticChatSession = {
    /**
     * 创建新会话
     */
    createSession: (sessionData: Omit<ChatSessionResponse, 'id' | 'created_at' | 'updated_at'>) => {
      const queryKey = QueryKeyFactory.chat.sessions();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { sessions: ChatSessionResponse[] } | undefined) => {
          const optimisticSession: ChatSessionResponse = {
            ...sessionData,
            id: `temp_${Date.now()}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };

          return {
            sessions: [optimisticSession, ...(oldData?.sessions || [])],
          };
        },
      });
    },

    /**
     * 更新会话
     */
    updateSession: (sessionId: string, updates: Partial<ChatSessionResponse>) => {
      const queryKey = QueryKeyFactory.chat.sessions();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { sessions: ChatSessionResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            sessions: oldData.sessions.map(session => 
              session.id === sessionId 
                ? { ...session, ...updates, updated_at: new Date().toISOString() }
                : session
            ),
          };
        },
      });
    },

    /**
     * 删除会话
     */
    deleteSession: (sessionId: string) => {
      const queryKey = QueryKeyFactory.chat.sessions();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { sessions: ChatSessionResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            sessions: oldData.sessions.filter(session => session.id !== sessionId),
          };
        },
      });
    },
  };

  /**
   * 文档乐观更新
   */
  optimisticDocument = {
    /**
     * 添加文档到列表
     */
    addDocument: (document: Omit<DocumentResponse, 'id' | 'upload_time'>) => {
      const queryKey = QueryKeyFactory.documents.list();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { documents: DocumentResponse[] } | undefined) => {
          const optimisticDocument: DocumentResponse = {
            ...document,
            id: `temp_${Date.now()}`,
            upload_time: new Date().toISOString(),
          };

          return {
            documents: [optimisticDocument, ...(oldData?.documents || [])],
          };
        },
      });
    },

    /**
     * 更新文档状态
     */
    updateDocumentStatus: (documentId: string, status: string, processingProgress?: number) => {
      const queryKey = QueryKeyFactory.documents.list();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { documents: DocumentResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            documents: oldData.documents.map(doc => 
              doc.id === documentId 
                ? { 
                    ...doc, 
                    processing_status: status,
                    ...(processingProgress !== undefined && { processing_progress: processingProgress })
                  }
                : doc
            ),
          };
        },
      });
    },

    /**
     * 删除文档
     */
    deleteDocument: (documentId: string) => {
      const queryKey = QueryKeyFactory.documents.list();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { documents: DocumentResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            documents: oldData.documents.filter(doc => doc.id !== documentId),
          };
        },
      });
    },

    /**
     * 批量删除文档
     */
    batchDeleteDocuments: (documentIds: string[]) => {
      const queryKey = QueryKeyFactory.documents.list();
      
      return this.executeOptimisticUpdate({
        queryKey,
        updater: (oldData: { documents: DocumentResponse[] } | undefined) => {
          if (!oldData) return oldData;

          return {
            documents: oldData.documents.filter(doc => !documentIds.includes(doc.id)),
          };
        },
      });
    },
  };

  /**
   * 组合乐观更新
   * 同时更新多个查询
   */
  async executeMultipleOptimisticUpdates<T = any>(
    updates: OptimisticUpdateConfig<T>[]
  ): Promise<OptimisticUpdateContext[]> {
    const contexts: OptimisticUpdateContext[] = [];
    
    for (const update of updates) {
      const context = await this.executeOptimisticUpdate(update);
      contexts.push(context);
    }
    
    return contexts;
  }

  /**
   * 批量回滚
   */
  rollbackMultipleUpdates(contexts: OptimisticUpdateContext[], queryKeys: unknown[][]): void {
    contexts.forEach((context, index) => {
      if (queryKeys[index]) {
        this.rollbackOptimisticUpdate(context, queryKeys[index]);
      }
    });
  }
}

// 乐观更新工具函数
export const optimisticUpdateUtils = {
  /**
   * 生成临时ID
   */
  generateTempId: (prefix = 'temp'): string => {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * 检查是否为临时ID
   */
  isTempId: (id: string): boolean => {
    return id.startsWith('temp_');
  },

  /**
   * 替换临时ID为真实ID
   */
  replaceTempId: <T extends { id: string }>(
    items: T[], 
    tempId: string, 
    realId: string
  ): T[] => {
    return items.map(item => 
      item.id === tempId ? { ...item, id: realId } : item
    );
  },

  /**
   * 移除临时项目
   */
  removeTempItems: <T extends { id: string }>(items: T[]): T[] => {
    return items.filter(item => !optimisticUpdateUtils.isTempId(item.id));
  },

  /**
   * 获取临时项目
   */
  getTempItems: <T extends { id: string }>(items: T[]): T[] => {
    return items.filter(item => optimisticUpdateUtils.isTempId(item.id));
  },

  /**
   * 合并实时数据和乐观更新
   */
  mergeOptimisticData: <T extends { id: string }>(
    serverData: T[], 
    optimisticData: T[]
  ): T[] => {
    const serverIds = new Set(serverData.map(item => item.id));
    const optimisticItems = optimisticData.filter(item => 
      optimisticUpdateUtils.isTempId(item.id) || !serverIds.has(item.id)
    );
    
    return [...serverData, ...optimisticItems];
  },
};

// 乐观更新钩子工厂
export const createOptimisticUpdateHook = (queryClient: QueryClient) => {
  const manager = new OptimisticUpdateManager(queryClient);
  
  return {
    manager,
    
    // 聊天相关乐观更新
    chat: manager.optimisticChatMessage,
    session: manager.optimisticChatSession,
    
    // 文档相关乐观更新
    document: manager.optimisticDocument,
    
    // 工具函数
    utils: optimisticUpdateUtils,
  };
};

export default OptimisticUpdateManager;