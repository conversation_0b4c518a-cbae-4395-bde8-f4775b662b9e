/**
 * 数据预取策略管理器
 * 智能预加载相关数据以提升用户体验
 */

import { QueryClient } from '@tanstack/react-query';
import { QueryKeyFactory, CACHE_STRATEGIES } from './cacheConfig';
import { ChatService, DocumentService } from '../../services';

// 预取优先级
export type PrefetchPriority = 'high' | 'medium' | 'low';

// 预取策略配置
export interface PrefetchConfig {
  queryKey: unknown[];
  queryFn: () => Promise<any>;
  priority: PrefetchPriority;
  delay?: number;
  condition?: () => boolean;
  staleTime?: number;
  gcTime?: number;
}

// 预取上下文
export interface PrefetchContext {
  route?: string;
  userId?: string;
  sessionId?: string;
  documentId?: string;
  searchQuery?: string;
  userPreferences?: Record<string, any>;
}

// 预取执行结果
export interface PrefetchResult {
  queryKey: unknown[];
  success: boolean;
  duration: number;
  error?: Error;
  fromCache: boolean;
}

// 预取策略管理器
export class PrefetchStrategyManager {
  private queryClient: QueryClient;
  private prefetchQueue: Map<string, PrefetchConfig> = new Map();
  private executionHistory: PrefetchResult[] = [];
  private isExecuting = false;

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * 添加预取策略到队列
   */
  addPrefetchStrategy(id: string, config: PrefetchConfig): void {
    this.prefetchQueue.set(id, config);
  }

  /**
   * 移除预取策略
   */
  removePrefetchStrategy(id: string): void {
    this.prefetchQueue.delete(id);
  }

  /**
   * 执行预取队列
   */
  async executePrefetchQueue(context: PrefetchContext = {}): Promise<PrefetchResult[]> {
    if (this.isExecuting) {
      console.warn('预取策略正在执行中，跳过重复执行');
      return [];
    }

    this.isExecuting = true;
    const results: PrefetchResult[] = [];

    try {
      // 按优先级排序
      const sortedStrategies = Array.from(this.prefetchQueue.entries())
        .sort(([, a], [, b]) => this.getPriorityWeight(a.priority) - this.getPriorityWeight(b.priority));

      // 分批执行
      for (const [id, config] of sortedStrategies) {
        if (config.condition && !config.condition()) {
          continue;
        }

        const result = await this.executePrefetchStrategy(id, config);
        results.push(result);
        
        // 记录执行历史
        this.executionHistory.push(result);
        
        // 限制历史记录数量
        if (this.executionHistory.length > 100) {
          this.executionHistory.shift();
        }
      }

      return results;
    } finally {
      this.isExecuting = false;
    }
  }

  /**
   * 执行单个预取策略
   */
  private async executePrefetchStrategy(id: string, config: PrefetchConfig): Promise<PrefetchResult> {
    const startTime = Date.now();
    
    try {
      // 检查是否已经有缓存
      const existingData = this.queryClient.getQueryData(config.queryKey);
      const fromCache = existingData !== undefined;

      if (fromCache) {
        const query = this.queryClient.getQueryState(config.queryKey);
        const isStale = query ? query.isStale : false;
        
        if (!isStale) {
          return {
            queryKey: config.queryKey,
            success: true,
            duration: Date.now() - startTime,
            fromCache: true,
          };
        }
      }

      // 添加延迟
      if (config.delay) {
        await new Promise(resolve => setTimeout(resolve, config.delay));
      }

      // 执行预取
      await this.queryClient.prefetchQuery({
        queryKey: config.queryKey,
        queryFn: config.queryFn,
        staleTime: config.staleTime || CACHE_STRATEGIES.DOCUMENT_CONTENT.staleTime,
        gcTime: config.gcTime || CACHE_STRATEGIES.DOCUMENT_CONTENT.gcTime,
      });

      return {
        queryKey: config.queryKey,
        success: true,
        duration: Date.now() - startTime,
        fromCache: false,
      };
    } catch (error) {
      console.error(`预取策略 ${id} 执行失败:`, error);
      return {
        queryKey: config.queryKey,
        success: false,
        duration: Date.now() - startTime,
        error: error as Error,
        fromCache: false,
      };
    }
  }

  /**
   * 获取优先级权重
   */
  private getPriorityWeight(priority: PrefetchPriority): number {
    switch (priority) {
      case 'high': return 1;
      case 'medium': return 2;
      case 'low': return 3;
      default: return 2;
    }
  }

  /**
   * 清空预取队列
   */
  clearPrefetchQueue(): void {
    this.prefetchQueue.clear();
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(): PrefetchResult[] {
    return [...this.executionHistory];
  }

  /**
   * 获取预取统计
   */
  getPrefetchStats(): {
    totalExecutions: number;
    successRate: number;
    averageDuration: number;
    cacheHitRate: number;
  } {
    const history = this.executionHistory;
    
    if (history.length === 0) {
      return {
        totalExecutions: 0,
        successRate: 0,
        averageDuration: 0,
        cacheHitRate: 0,
      };
    }

    const successCount = history.filter(r => r.success).length;
    const cacheHitCount = history.filter(r => r.fromCache).length;
    const totalDuration = history.reduce((sum, r) => sum + r.duration, 0);

    return {
      totalExecutions: history.length,
      successRate: successCount / history.length,
      averageDuration: totalDuration / history.length,
      cacheHitRate: cacheHitCount / history.length,
    };
  }
}

// 预定义预取策略
export const createPredefinedStrategies = (queryClient: QueryClient) => {
  const manager = new PrefetchStrategyManager(queryClient);

  // 聊天页面预取策略
  const chatPageStrategies = {
    // 预取会话列表
    sessions: {
      id: 'chat-sessions',
      config: {
        queryKey: QueryKeyFactory.chat.sessions(),
        queryFn: () => ChatService.getSessions(),
        priority: 'high' as PrefetchPriority,
        staleTime: CACHE_STRATEGIES.USER_SESSION.staleTime,
      },
    },
    
    // 预取当前会话消息
    currentSessionMessages: (sessionId: string) => ({
      id: `chat-messages-${sessionId}`,
      config: {
        queryKey: QueryKeyFactory.chat.messages(sessionId),
        queryFn: () => ChatService.getChatHistory(sessionId),
        priority: 'high' as PrefetchPriority,
        condition: () => !!sessionId,
        staleTime: CACHE_STRATEGIES.CHAT_MESSAGES.staleTime,
      },
    }),
  };

  // 文档页面预取策略
  const documentPageStrategies = {
    // 预取文档列表
    documentList: {
      id: 'document-list',
      config: {
        queryKey: QueryKeyFactory.documents.list(),
        queryFn: () => DocumentService.getDocuments(),
        priority: 'high' as PrefetchPriority,
        staleTime: CACHE_STRATEGIES.DOCUMENT_LIST.staleTime,
      },
    },
    
    // 鼠标悬停预取文档详情
    documentHover: (documentId: string) => ({
      id: `document-hover-${documentId}`,
      config: {
        queryKey: QueryKeyFactory.documents.detail(documentId),
        queryFn: () => DocumentService.getDocument(documentId),
        priority: 'low' as PrefetchPriority,
        delay: 500,
        condition: () => !!documentId,
        staleTime: CACHE_STRATEGIES.DOCUMENT_CONTENT.staleTime,
      },
    }),
  };

  // 智能预取策略
  const intelligentStrategies = {
    // 基于用户行为的预取
    userBehaviorBased: (context: PrefetchContext) => {
      const strategies: Array<{ id: string; config: PrefetchConfig }> = [];

      // 如果用户经常查看特定类型的文档，预取相关文档
      if (context.userPreferences?.favoriteDocumentTypes) {
        strategies.push({
          id: 'favorite-documents',
          config: {
            queryKey: QueryKeyFactory.documents.list({ 
              types: context.userPreferences.favoriteDocumentTypes 
            }),
            queryFn: () => DocumentService.getDocuments({ 
              types: context.userPreferences.favoriteDocumentTypes.join(',') 
            }),
            priority: 'medium' as PrefetchPriority,
            staleTime: CACHE_STRATEGIES.DOCUMENT_LIST.staleTime,
          },
        });
      }

      // 如果用户在聊天页面停留时间长，预取相关文档
      if (context.route?.includes('/chat') && context.sessionId) {
        strategies.push({
          id: 'chat-related-documents',
          config: {
            queryKey: QueryKeyFactory.documents.search('chat-related'),
            queryFn: () => DocumentService.searchDocuments({ 
              query: 'chat-related', 
              limit: 10 
            }),
            priority: 'low' as PrefetchPriority,
            delay: 2000,
            staleTime: CACHE_STRATEGIES.SEARCH_RESULTS.staleTime,
          },
        });
      }

      return strategies;
    },

    // 时间基于的预取
    timeBased: () => {
      const strategies: Array<{ id: string; config: PrefetchConfig }> = [];
      const hour = new Date().getHours();

      // 工作时间预取文档
      if (hour >= 9 && hour <= 17) {
        strategies.push({
          id: 'work-hours-documents',
          config: {
            queryKey: QueryKeyFactory.documents.list({ priority: 'high' }),
            queryFn: () => DocumentService.getDocuments({ priority: 'high' }),
            priority: 'medium' as PrefetchPriority,
            staleTime: CACHE_STRATEGIES.DOCUMENT_LIST.staleTime,
          },
        });
      }

      return strategies;
    },

    // 网络状态基于的预取
    networkBased: () => {
      const strategies: Array<{ id: string; config: PrefetchConfig }> = [];
      const connection = (navigator as any).connection;

      if (connection) {
        // 高速网络时预取更多数据
        if (connection.effectiveType === '4g') {
          strategies.push({
            id: 'high-speed-prefetch',
            config: {
              queryKey: QueryKeyFactory.documents.list({ limit: 50 }),
              queryFn: () => DocumentService.getDocuments({ limit: 50 }),
              priority: 'low' as PrefetchPriority,
              staleTime: CACHE_STRATEGIES.DOCUMENT_LIST.staleTime,
            },
          });
        }
        
        // 低速网络时只预取关键数据
        if (connection.effectiveType === '2g' || connection.effectiveType === '3g') {
          strategies.push({
            id: 'low-speed-prefetch',
            config: {
              queryKey: QueryKeyFactory.documents.list({ limit: 10 }),
              queryFn: () => DocumentService.getDocuments({ limit: 10 }),
              priority: 'high' as PrefetchPriority,
              staleTime: CACHE_STRATEGIES.DOCUMENT_LIST.staleTime,
            },
          });
        }
      }

      return strategies;
    },
  };

  return {
    manager,
    chatPageStrategies,
    documentPageStrategies,
    intelligentStrategies,
  };
};

// 预取策略钩子
export const usePrefetchStrategy = (queryClient: QueryClient) => {
  const { manager, chatPageStrategies, documentPageStrategies, intelligentStrategies } = 
    createPredefinedStrategies(queryClient);

  return {
    manager,
    
    // 页面级别预取
    prefetchChatPage: async (sessionId?: string) => {
      manager.addPrefetchStrategy(
        chatPageStrategies.sessions.id,
        chatPageStrategies.sessions.config
      );
      
      if (sessionId) {
        const messageStrategy = chatPageStrategies.currentSessionMessages(sessionId);
        manager.addPrefetchStrategy(messageStrategy.id, messageStrategy.config);
      }
      
      return manager.executePrefetchQueue({ route: '/chat', sessionId });
    },
    
    prefetchDocumentPage: async () => {
      manager.addPrefetchStrategy(
        documentPageStrategies.documentList.id,
        documentPageStrategies.documentList.config
      );
      
      return manager.executePrefetchQueue({ route: '/documents' });
    },
    
    // 交互级别预取
    prefetchOnDocumentHover: async (documentId: string) => {
      const hoverStrategy = documentPageStrategies.documentHover(documentId);
      manager.addPrefetchStrategy(hoverStrategy.id, hoverStrategy.config);
      
      return manager.executePrefetchQueue({ documentId });
    },
    
    // 智能预取
    prefetchIntelligent: async (context: PrefetchContext) => {
      // 添加基于用户行为的预取策略
      const userStrategies = intelligentStrategies.userBehaviorBased(context);
      userStrategies.forEach(({ id, config }) => {
        manager.addPrefetchStrategy(id, config);
      });
      
      // 添加基于时间的预取策略
      const timeStrategies = intelligentStrategies.timeBased();
      timeStrategies.forEach(({ id, config }) => {
        manager.addPrefetchStrategy(id, config);
      });
      
      // 添加基于网络的预取策略
      const networkStrategies = intelligentStrategies.networkBased();
      networkStrategies.forEach(({ id, config }) => {
        manager.addPrefetchStrategy(id, config);
      });
      
      return manager.executePrefetchQueue(context);
    },
    
    // 清理
    clearPrefetchQueue: () => manager.clearPrefetchQueue(),
    
    // 统计
    getPrefetchStats: () => manager.getPrefetchStats(),
    getExecutionHistory: () => manager.getExecutionHistory(),
  };
};

export default PrefetchStrategyManager;