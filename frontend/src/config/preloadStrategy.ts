import { preloadComponents } from '../utils/preload';

/**
 * 预加载策略配置
 */
export const PreloadStrategy = {
  // 首页加载后预加载的组件
  afterHomePage: ['chat', 'documents'],
  
  // 聊天页面预加载的组件
  afterChatPage: ['virtualization', 'documentManager'],
  
  // 文档页面预加载的组件
  afterDocumentsPage: ['chat', 'chatInterface'],
  
  // 预加载延迟时间（毫秒）
  delays: {
    idle: 2000,
    hover: 100,
    route: 500,
  },
  
  // 关键路由组件（优先预加载）
  critical: ['chat', 'documents'],
  
  // 非关键组件（低优先级预加载）
  nonCritical: ['virtualization'],
};

/**
 * 初始化预加载策略
 */
export const initPreloadStrategy = () => {
  // 延迟预加载关键组件
  setTimeout(() => {
    preloadComponents(PreloadStrategy.critical);
  }, PreloadStrategy.delays.idle);
  
  // 更长延迟预加载非关键组件
  setTimeout(() => {
    preloadComponents(PreloadStrategy.nonCritical);
  }, PreloadStrategy.delays.idle * 2);
};

/**
 * 基于当前页面的智能预加载
 */
export const preloadForPage = (pageName: string) => {
  const strategy = PreloadStrategy as any;
  const componentsToPreload = strategy[`after${pageName}Page`];
  
  if (componentsToPreload) {
    setTimeout(() => {
      preloadComponents(componentsToPreload);
    }, PreloadStrategy.delays.route);
  }
};

export default PreloadStrategy;