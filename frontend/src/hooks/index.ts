/**
 * Hooks导出索引
 */

// 流式处理Hook
export { default as useStreaming } from './useStreaming';
export type { StreamingState, StreamingOptions } from './useStreaming';

// 聊天管理Hook
export { useChat } from './useChat';
export type { 
  UseChatOptions, 
  UseChatReturn 
} from './useChat';

// 文档管理Hook
export { useDocument } from './useDocument';
export type { 
  UseDocumentOptions, 
  UseDocumentReturn 
} from './useDocument';

// 会话管理Hook
export { useSession } from './useSession';
export type { 
  UseSessionOptions, 
  UseSessionReturn,
  SessionState,
  UserProfile,
  UserPreferences
} from './useSession';

// 主题管理Hook
export { useTheme } from './useTheme';
export type { 
  ThemeMode, 
  ThemePreset, 
  ThemeConfig 
} from './useTheme';

// 动画和微交互Hook
export {
  useMicroInteraction,
  useButtonInteraction,
  useCardInteraction,
  useInputInteraction,
} from './useMicroInteraction';
export type {
  MicroInteractionState,
  MicroInteractionHandlers,
  MicroInteractionOptions,
} from './useMicroInteraction';

// 响应式设计Hook
export {
  useResponsive,
  useMobile,
  useBreakpoint,
  useBreakpointUp,
  useBreakpointDown,
} from './useResponsive';
export type {
  BreakpointConfig,
  ResponsiveState,
  ResponsiveOptions,
} from './useResponsive';

// 手势识别Hook
export {
  useGestures,
  useSwipe,
  useLongPress,
  useDoubleTap,
} from './useGestures';
export type {
  TouchPoint,
  SwipeGesture,
  GestureState,
  GestureHandlers,
  GestureOptions,
} from './useGestures';

// 移动端性能优化Hook
export {
  useMobilePerformance,
  useLazyImage,
  useVirtualScroll,
  useMobileDebounce,
  useMobileScroll,
  useMobileKeyboard,
} from './useMobilePerformance';
export type {
  PerformanceMetrics,
  LazyImageOptions,
  VirtualScrollOptions,
} from './useMobilePerformance'; 