/**
 * useChat Hook - 聊天相关状态管理
 * 集成React Query和本地存储支持，使用优化的缓存策略
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ChatService } from '../services';
import { QueryKeyFactory, CACHE_STRATEGIES, createOptimisticUpdateHook } from '../config/cache';
import {
  ChatSessionResponse,
  ChatMessageResponse,
  ChatRequest,
  ChatResponse,
  ChatState,
  ChatSessionCreate,
  MessageRole,
} from '../types/chat';

// Hook选项
export interface UseChatOptions {
  autoLoadSessions?: boolean;
  persistentSession?: boolean;
  enableStreaming?: boolean;
  enableOptimisticUpdates?: boolean;
}

// Hook返回类型
export interface UseChatReturn {
  // 状态
  state: ChatState;
  
  // 聊天操作
  sendMessage: (message: string, sessionId?: string) => Promise<void>;
  
  // 会话操作
  createSession: (title?: string) => Promise<void>;
  loadSessions: () => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  setCurrentSession: (session: ChatSessionResponse | null) => void;
  
  // 错误处理
  clearError: () => void;
  
  // 查询状态
  isLoading: boolean;
  isSending: boolean;
  error: string | null;
  
  // 缓存管理
  prefetchSessionMessages: (sessionId: string) => Promise<void>;
  invalidateSessionData: () => void;
}

// 本地存储键
const STORAGE_KEYS = {
  CURRENT_SESSION: 'chat_current_session',
} as const;

// 默认选项
const DEFAULT_OPTIONS: Required<UseChatOptions> = {
  autoLoadSessions: true,
  persistentSession: true,
  enableStreaming: false,
  enableOptimisticUpdates: true,
} as const;

export function useChat(options: UseChatOptions = {}): UseChatReturn {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const queryClient = useQueryClient();
  const optimisticUpdates = createOptimisticUpdateHook(queryClient);
  
  // 本地状态
  const [currentSession, setCurrentSession] = useState<ChatSessionResponse | null>(() => {
    if (opts.persistentSession) {
      try {
        const stored = localStorage.getItem(STORAGE_KEYS.CURRENT_SESSION);
        return stored ? JSON.parse(stored) : null;
      } catch {
        return null;
      }
    }
    return null;
  });
  
  const [messages, setMessages] = useState<ChatMessageResponse[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  // 查询：会话列表
  const {
    data: sessionsData,
    isLoading: isLoadingSessions,
    refetch: refetchSessions,
  } = useQuery({
    queryKey: QueryKeyFactory.chat.sessions(),
    queryFn: async () => {
      try {
        return await ChatService.getSessions();
      } catch (error) {
        console.error('加载会话列表失败:', error);
        throw error;
      }
    },
    enabled: opts.autoLoadSessions,
    ...CACHE_STRATEGIES.USER_SESSION,
  });

  // 查询：当前会话的消息
  const {
    data: messagesData,
    isLoading: isLoadingMessages,
    refetch: refetchMessages,
  } = useQuery({
    queryKey: currentSession ? QueryKeyFactory.chat.messages(currentSession.id) : ['disabled'],
    queryFn: async () => {
      if (!currentSession) return null;
      try {
        return await ChatService.getChatHistory(currentSession.id);
      } catch (error) {
        console.error('加载消息历史失败:', error);
        throw error;
      }
    },
    enabled: !!currentSession,
    ...CACHE_STRATEGIES.CHAT_MESSAGES,
  });

  // 突变：发送消息
  const sendMessageMutation = useMutation({
    mutationFn: async ({ message, sessionId }: { message: string; sessionId?: string }) => {
      const targetSessionId = sessionId || currentSession?.id;
      if (!targetSessionId) {
        throw new Error('没有可用的会话ID');
      }

      const request: ChatRequest = {
        message,
        session_id: targetSessionId,
        stream: false,
      };
      
      return await ChatService.sendMessage(request);
    },
    onMutate: async ({ message, sessionId }) => {
      if (!opts.enableOptimisticUpdates) return;
      
      const targetSessionId = sessionId || currentSession?.id;
      if (!targetSessionId) return;

      // 乐观更新：添加用户消息
      return optimisticUpdates.chat.addMessage(targetSessionId, {
        content: message,
        role: 'user' as MessageRole,
      });
    },
    onSuccess: (response, variables, context) => {
      // 刷新消息查询
      if (currentSession) {
        queryClient.invalidateQueries({
          queryKey: QueryKeyFactory.chat.messages(currentSession.id),
        });
      }
      setError(null);
    },
    onError: (error: any, variables, context) => {
      setError(error?.response?.data?.message || error.message || '发送消息失败');
      
      // 回滚乐观更新
      if (context && opts.enableOptimisticUpdates) {
        const targetSessionId = variables.sessionId || currentSession?.id;
        if (targetSessionId) {
          optimisticUpdates.manager.rollbackOptimisticUpdate(
            context,
            QueryKeyFactory.chat.messages(targetSessionId)
          );
        }
      }
    },
  });

  // 突变：创建会话
  const createSessionMutation = useMutation({
    mutationFn: async (title?: string) => {
      const sessionData: ChatSessionCreate = {};
      if (title) {
        sessionData.title = title;
      }
      return await ChatService.createSession(sessionData);
    },
    onMutate: async (title) => {
      if (!opts.enableOptimisticUpdates) return;
      
      // 乐观更新：添加新会话
      return optimisticUpdates.session.createSession({
        title: title || '新对话',
      });
    },
    onSuccess: (session, variables, context) => {
      if (opts.persistentSession) {
        localStorage.setItem(STORAGE_KEYS.CURRENT_SESSION, JSON.stringify(session));
      }
      setCurrentSession(session);
      setMessages([]);
      setError(null);
      
      // 刷新会话列表
      queryClient.invalidateQueries({
        queryKey: QueryKeyFactory.chat.sessions(),
      });
    },
    onError: (error: any, variables, context) => {
      setError(error?.response?.data?.message || error.message || '创建会话失败');
      
      // 回滚乐观更新
      if (context && opts.enableOptimisticUpdates) {
        optimisticUpdates.manager.rollbackOptimisticUpdate(
          context,
          QueryKeyFactory.chat.sessions()
        );
      }
    },
  });

  // 突变：删除会话
  const deleteSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      await ChatService.deleteSession(sessionId);
      return sessionId;
    },
    onMutate: async (sessionId) => {
      if (!opts.enableOptimisticUpdates) return;
      
      // 乐观更新：删除会话
      return optimisticUpdates.session.deleteSession(sessionId);
    },
    onSuccess: (sessionId, variables, context) => {
      // 如果删除的是当前会话，清除当前会话
      if (currentSession?.id === sessionId) {
        setCurrentSession(null);
        setMessages([]);
        if (opts.persistentSession) {
          localStorage.removeItem(STORAGE_KEYS.CURRENT_SESSION);
        }
      }
      
      // 刷新会话列表
      queryClient.invalidateQueries({
        queryKey: QueryKeyFactory.chat.sessions(),
      });
    },
    onError: (error: any, variables, context) => {
      setError(error?.response?.data?.message || error.message || '删除会话失败');
      
      // 回滚乐观更新
      if (context && opts.enableOptimisticUpdates) {
        optimisticUpdates.manager.rollbackOptimisticUpdate(
          context,
          QueryKeyFactory.chat.sessions()
        );
      }
    },
  });

  // 设置当前会话
  const handleSetCurrentSession = useCallback((session: ChatSessionResponse | null) => {
    setCurrentSession(session);
    setMessages([]);
    setError(null);
    
    if (opts.persistentSession) {
      if (session) {
        localStorage.setItem(STORAGE_KEYS.CURRENT_SESSION, JSON.stringify(session));
      } else {
        localStorage.removeItem(STORAGE_KEYS.CURRENT_SESSION);
      }
    }
  }, [opts.persistentSession]);

  // 预取会话消息
  const prefetchSessionMessages = useCallback(async (sessionId: string) => {
    await queryClient.prefetchQuery({
      queryKey: QueryKeyFactory.chat.messages(sessionId),
      queryFn: () => ChatService.getChatHistory(sessionId),
      ...CACHE_STRATEGIES.CHAT_MESSAGES,
    });
  }, [queryClient]);

  // 使会话数据失效
  const invalidateSessionData = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: QueryKeyFactory.chat.all(),
    });
  }, [queryClient]);

  // 更新messages状态当messagesData变化时
  useEffect(() => {
    if (messagesData?.messages) {
      setMessages(messagesData.messages);
    }
  }, [messagesData]);

  // 组合状态
  const state: ChatState = {
    currentSession,
    sessions: sessionsData?.sessions || [],
    messages,
    isLoading: isLoadingSessions || isLoadingMessages,
    isStreaming: false,
    error,
    sessionsListPagination: {
      page: sessionsData?.page || 1,
      page_size: sessionsData?.page_size || 20,
      total: sessionsData?.total || 0,
      has_next: sessionsData?.has_next || false,
    },
  };

  return {
    state,
    
    // 聊天操作
    sendMessage: async (message: string, sessionId?: string) => {
      await sendMessageMutation.mutateAsync({ message, ...(sessionId && { sessionId }) });
    },
    
    // 会话操作
    createSession: async (title?: string) => {
      await createSessionMutation.mutateAsync(title);
    },
    loadSessions: async () => {
      await refetchSessions();
    },
    deleteSession: async (sessionId: string) => {
      await deleteSessionMutation.mutateAsync(sessionId);
    },
    setCurrentSession: handleSetCurrentSession,
    
    // 错误处理
    clearError: () => setError(null),
    
    // 查询状态
    isLoading: isLoadingSessions || isLoadingMessages,
    isSending: sendMessageMutation.isPending || createSessionMutation.isPending,
    error,
    
    // 缓存管理
    prefetchSessionMessages,
    invalidateSessionData,
  };
}