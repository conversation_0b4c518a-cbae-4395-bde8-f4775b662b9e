/**
 * useDocument Hook - 文档相关状态管理
 * 集成React Query和本地存储支持，使用优化的缓存策略
 */

import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DocumentService } from '../services';
import { QueryKeyFactory, CACHE_STRATEGIES, createOptimisticUpdateHook } from '../config/cache';
import {
  DocumentResponse,
  DocumentUploadResponse,
  DocumentListResponse,
  DocumentState,
  DocumentListFilters,
  DocumentSearchResult,
  FileUploadProgress,
} from '../types/document';

// Hook选项
export interface UseDocumentOptions {
  autoLoadDocuments?: boolean;
  persistentFilters?: boolean;
  maxUploadSize?: number; // MB
  enableOptimisticUpdates?: boolean;
  enablePrefetch?: boolean;
}

// Hook返回类型
export interface UseDocumentReturn {
  // 状态
  state: DocumentState;
  
  // 文档操作
  uploadDocument: (file: File) => Promise<void>;
  deleteDocument: (documentId: string) => Promise<void>;
  loadDocuments: () => Promise<void>;
  searchDocuments: (query: string) => Promise<void>;
  
  // 过滤和排序
  setFilters: (filters: Partial<DocumentListFilters>) => void;
  clearFilters: () => void;
  
  // 错误处理
  clearError: () => void;
  clearUploadProgress: (fileId?: string) => void;
  
  // 查询状态
  isLoading: boolean;
  isUploading: boolean;
  error: string | null;
  
  // 缓存管理
  prefetchDocument: (documentId: string) => Promise<void>;
  invalidateDocumentData: () => void;
}

// 本地存储键
const STORAGE_KEYS = {
  DOCUMENT_FILTERS: 'document_filters',
} as const;

// 默认选项
const DEFAULT_OPTIONS: Required<UseDocumentOptions> = {
  autoLoadDocuments: true,
  persistentFilters: true,
  maxUploadSize: 10, // 10MB
  enableOptimisticUpdates: true,
  enablePrefetch: true,
} as const;

// 默认过滤器
const DEFAULT_FILTERS: DocumentListFilters = {
  page: 1,
  page_size: 20,
  sort_by: 'upload_time',
  sort_order: 'desc',
};

export function useDocument(options: UseDocumentOptions = {}): UseDocumentReturn {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const queryClient = useQueryClient();
  const optimisticUpdates = createOptimisticUpdateHook(queryClient);
  
  // 本地状态
  const [filters, setFiltersState] = useState<DocumentListFilters>(() => {
    if (opts.persistentFilters) {
      try {
        const stored = localStorage.getItem(STORAGE_KEYS.DOCUMENT_FILTERS);
        return stored ? { ...DEFAULT_FILTERS, ...JSON.parse(stored) } : DEFAULT_FILTERS;
      } catch {
        return DEFAULT_FILTERS;
      }
    }
    return DEFAULT_FILTERS;
  });
  
  const [uploadProgress, setUploadProgress] = useState<Record<string, FileUploadProgress>>({});
  const [searchResults, setSearchResults] = useState<DocumentSearchResult[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 查询：文档列表
  const {
    data: documentsData,
    isLoading: isLoadingDocuments,
    refetch: refetchDocuments,
  } = useQuery({
    queryKey: QueryKeyFactory.documents.list(filters),
    queryFn: async () => {
      try {
        // 转换过滤器格式以匹配API
        const apiParams: any = {};
        if (filters.page) apiParams.page = filters.page;
        if (filters.page_size) apiParams.page_size = filters.page_size;
        if (filters.search) apiParams.search = filters.search;
        if (filters.status && filters.status.length > 0) {
          apiParams.status = filters.status.join(',');
        }
        return await DocumentService.getDocuments(apiParams);
      } catch (error) {
        console.error('加载文档列表失败:', error);
        throw error;
      }
    },
    enabled: opts.autoLoadDocuments,
    ...CACHE_STRATEGIES.DOCUMENT_LIST,
  });

  // 突变：上传文档
  const uploadDocumentMutation = useMutation({
    mutationFn: async (file: File) => {
      // 验证文件
      if (file.size > opts.maxUploadSize * 1024 * 1024) {
        throw new Error(`文件大小不能超过 ${opts.maxUploadSize}MB`);
      }

      const fileId = `${file.name}_${Date.now()}`;
      
      // 更新上传进度
      setUploadProgress(prev => ({
        ...prev,
        [fileId]: {
          file,
          progress: 0,
          status: 'uploading',
        },
      }));

      try {
        const response = await DocumentService.uploadDocument(file, (progress) => {
          setUploadProgress(prev => ({
            ...prev,
            [fileId]: {
              file,
              progress: Math.round(progress),
              status: 'uploading',
            },
          }));
        });

        // 上传成功
        setUploadProgress(prev => ({
          ...prev,
          [fileId]: {
            file,
            status: 'success',
            progress: 100,
            document_id: response.document_id,
          },
        }));

        return response;
      } catch (error: any) {
        // 上传失败
        setUploadProgress(prev => ({
          ...prev,
          [fileId]: {
            file,
            status: 'error',
            progress: 0,
            error: error.message || '上传失败',
          },
        }));
        throw error;
      }
    },
    onMutate: async (file) => {
      if (!opts.enableOptimisticUpdates) return;
      
      // 乐观更新：添加文档到列表
      return optimisticUpdates.document.addDocument({
        filename: file.name,
        file_size: file.size,
        file_type: file.type,
        processing_status: 'processing',
        processing_progress: 0,
      });
    },
    onSuccess: (response, variables, context) => {
      // 刷新文档列表
      queryClient.invalidateQueries({
        queryKey: QueryKeyFactory.documents.lists(),
      });
      setError(null);
    },
    onError: (error: any, variables, context) => {
      setError(error?.response?.data?.message || error.message || '上传文档失败');
      
      // 回滚乐观更新
      if (context && opts.enableOptimisticUpdates) {
        optimisticUpdates.manager.rollbackOptimisticUpdate(
          context,
          QueryKeyFactory.documents.list(filters)
        );
      }
    },
  });

  // 突变：删除文档
  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: string) => {
      await DocumentService.deleteDocument(documentId);
      return documentId;
    },
    onMutate: async (documentId) => {
      if (!opts.enableOptimisticUpdates) return;
      
      // 乐观更新：删除文档
      return optimisticUpdates.document.deleteDocument(documentId);
    },
    onSuccess: (documentId, variables, context) => {
      // 刷新文档列表
      queryClient.invalidateQueries({
        queryKey: QueryKeyFactory.documents.lists(),
      });
      
      // 移除文档详情缓存
      queryClient.removeQueries({
        queryKey: QueryKeyFactory.documents.detail(documentId),
      });
      
      setError(null);
    },
    onError: (error: any, variables, context) => {
      setError(error?.response?.data?.message || error.message || '删除文档失败');
      
      // 回滚乐观更新
      if (context && opts.enableOptimisticUpdates) {
        optimisticUpdates.manager.rollbackOptimisticUpdate(
          context,
          QueryKeyFactory.documents.list(filters)
        );
      }
    },
  });

  // 突变：搜索文档
  const searchDocumentsMutation = useMutation({
    mutationFn: async (query: string) => {
      return await DocumentService.searchDocuments({
        query,
        limit: 50,
      });
    },
    onSuccess: (data) => {
      setSearchResults(data.results);
      setError(null);
    },
    onError: (error: any) => {
      setError(error?.response?.data?.message || error.message || '搜索文档失败');
      setSearchResults([]);
    },
  });

  // 设置过滤器
  const setFilters = useCallback((newFilters: Partial<DocumentListFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFiltersState(updatedFilters);
    
    if (opts.persistentFilters) {
      localStorage.setItem(STORAGE_KEYS.DOCUMENT_FILTERS, JSON.stringify(updatedFilters));
    }
  }, [filters, opts.persistentFilters]);

  // 清除过滤器
  const clearFilters = useCallback(() => {
    setFiltersState(DEFAULT_FILTERS);
    if (opts.persistentFilters) {
      localStorage.removeItem(STORAGE_KEYS.DOCUMENT_FILTERS);
    }
  }, [opts.persistentFilters]);

  // 清除上传进度
  const clearUploadProgress = useCallback((fileId?: string) => {
    if (fileId) {
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });
    } else {
      setUploadProgress({});
    }
  }, []);

  // 预取文档详情
  const prefetchDocument = useCallback(async (documentId: string) => {
    if (!opts.enablePrefetch) return;
    
    await queryClient.prefetchQuery({
      queryKey: QueryKeyFactory.documents.detail(documentId),
      queryFn: () => DocumentService.getDocument(documentId),
      ...CACHE_STRATEGIES.DOCUMENT_CONTENT,
    });
  }, [queryClient, opts.enablePrefetch]);

  // 使文档数据失效
  const invalidateDocumentData = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: QueryKeyFactory.documents.all(),
    });
  }, [queryClient]);

  // 组合状态
  const state: DocumentState = {
    documents: documentsData?.documents || [],
    isLoading: isLoadingDocuments,
    isUploading: uploadDocumentMutation.isPending,
    error,
    uploadProgress,
    searchResults,
    filters,
    pagination: {
      page: documentsData?.page || 1,
      page_size: documentsData?.page_size || 20,
      total: documentsData?.total || 0,
      has_next: (documentsData?.page || 1) < Math.ceil((documentsData?.total || 0) / (documentsData?.page_size || 20)),
    },
  };

  return {
    state,
    
    // 文档操作
    uploadDocument: async (file: File) => {
      await uploadDocumentMutation.mutateAsync(file);
    },
    deleteDocument: async (documentId: string) => {
      await deleteDocumentMutation.mutateAsync(documentId);
    },
    loadDocuments: async () => {
      await refetchDocuments();
    },
    searchDocuments: async (query: string) => {
      await searchDocumentsMutation.mutateAsync(query);
    },
    
    // 过滤和排序
    setFilters,
    clearFilters,
    
    // 错误处理
    clearError: () => setError(null),
    clearUploadProgress,
    
    // 查询状态
    isLoading: isLoadingDocuments,
    isUploading: uploadDocumentMutation.isPending,
    error,
    
    // 缓存管理
    prefetchDocument,
    invalidateDocumentData,
  };
}