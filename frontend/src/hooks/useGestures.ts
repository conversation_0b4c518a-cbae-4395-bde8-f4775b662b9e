import { useRef, useCallback, useEffect } from 'react';

export interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

export interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  velocity: number;
  duration: number;
}

export interface GestureState {
  isPressed: boolean;
  startPoint: TouchPoint | null;
  currentPoint: TouchPoint | null;
  endPoint: TouchPoint | null;
}

export interface GestureHandlers {
  onSwipeLeft?: (gesture: SwipeGesture) => void;
  onSwipeRight?: (gesture: SwipeGesture) => void;
  onSwipeUp?: (gesture: SwipeGesture) => void;
  onSwipeDown?: (gesture: SwipeGesture) => void;
  onTap?: (point: TouchPoint) => void;
  onLongPress?: (point: TouchPoint) => void;
  onPinch?: (scale: number, center: TouchPoint) => void;
  onDoubleTap?: (point: TouchPoint) => void;
}

export interface GestureOptions {
  // 滑动检测选项
  swipeThreshold?: number;        // 最小滑动距离 (px)
  velocityThreshold?: number;     // 最小滑动速度 (px/ms)
  
  // 长按检测选项
  longPressDelay?: number;        // 长按触发延迟 (ms)
  longPressThreshold?: number;    // 长按时允许的最大移动距离 (px)
  
  // 双击检测选项
  doubleTapDelay?: number;        // 双击检测延迟 (ms)
  doubleTapThreshold?: number;    // 双击时允许的最大移动距离 (px)
  
  // 缩放检测选项
  pinchThreshold?: number;        // 最小缩放变化
  
  // 是否阻止默认行为
  preventDefault?: boolean;
}

const DEFAULT_OPTIONS: Required<GestureOptions> = {
  swipeThreshold: 50,
  velocityThreshold: 0.3,
  longPressDelay: 500,
  longPressThreshold: 10,
  doubleTapDelay: 300,
  doubleTapThreshold: 20,
  pinchThreshold: 0.1,
  preventDefault: true,
};

/**
 * 移动端手势识别Hook
 * 支持滑动、点击、长按、双击、缩放等手势
 */
export function useGestures(
  handlers: GestureHandlers = {},
  options: GestureOptions = {}
) {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const elementRef = useRef<HTMLElement>(null);
  const gestureStateRef = useRef<GestureState>({
    isPressed: false,
    startPoint: null,
    currentPoint: null,
    endPoint: null,
  });
  
  // 用于检测双击和长按的定时器
  const longPressTimerRef = useRef<NodeJS.Timeout>();
  const doubleTapTimerRef = useRef<NodeJS.Timeout>();
  const lastTapRef = useRef<TouchPoint | null>(null);

  // 缩放相关状态
  const initialDistanceRef = useRef<number>(0);
  const lastScaleRef = useRef<number>(1);

  // 工具函数：计算两点距离
  const calculateDistance = useCallback((point1: TouchPoint, point2: TouchPoint): number => {
    const dx = point2.x - point1.x;
    const dy = point2.y - point1.y;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  // 工具函数：计算两点间的触摸距离（用于缩放检测）
  const calculateTouchDistance = useCallback((touch1: Touch, touch2: Touch): number => {
    const dx = touch2.clientX - touch1.clientX;
    const dy = touch2.clientY - touch1.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  // 工具函数：获取触摸中心点
  const getTouchCenter = useCallback((touch1: Touch, touch2: Touch): TouchPoint => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
      timestamp: Date.now(),
    };
  }, []);

  // 工具函数：获取滑动方向
  const getSwipeDirection = useCallback((start: TouchPoint, end: TouchPoint): 'left' | 'right' | 'up' | 'down' => {
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'down' : 'up';
    }
  }, []);

  // 清理定时器
  const clearTimers = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = undefined;
    }
    if (doubleTapTimerRef.current) {
      clearTimeout(doubleTapTimerRef.current);
      doubleTapTimerRef.current = undefined;
    }
  }, []);

  // 触摸开始
  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (config.preventDefault) {
      event.preventDefault();
    }

    const touch = event.touches[0];
    if (!touch) return;
    
    const point: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };

    gestureStateRef.current = {
      isPressed: true,
      startPoint: point,
      currentPoint: point,
      endPoint: null,
    };

    // 多点触摸检测（缩放）
    if (event.touches.length === 2 && event.touches[0] && event.touches[1]) {
      const distance = calculateTouchDistance(event.touches[0], event.touches[1]);
      initialDistanceRef.current = distance;
      lastScaleRef.current = 1;
      clearTimers(); // 取消长按检测
      return;
    }

    // 设置长按检测
    longPressTimerRef.current = setTimeout(() => {
      const current = gestureStateRef.current;
      if (current.isPressed && current.startPoint && current.currentPoint) {
        const distance = calculateDistance(current.startPoint, current.currentPoint);
        if (distance <= config.longPressThreshold) {
          handlers.onLongPress?.(current.startPoint);
        }
      }
    }, config.longPressDelay);
  }, [config, handlers, calculateDistance, calculateTouchDistance, clearTimers]);

  // 触摸移动
  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (config.preventDefault) {
      event.preventDefault();
    }

    const current = gestureStateRef.current;
    if (!current.isPressed || !current.startPoint) return;

    // 多点触摸处理（缩放）
    if (event.touches.length === 2 && event.touches[0] && event.touches[1]) {
      const distance = calculateTouchDistance(event.touches[0], event.touches[1]);
      if (initialDistanceRef.current > 0) {
        const scale = distance / initialDistanceRef.current;
        const scaleDiff = Math.abs(scale - lastScaleRef.current);
        
        if (scaleDiff > config.pinchThreshold) {
          const center = getTouchCenter(event.touches[0], event.touches[1]);
          handlers.onPinch?.(scale, center);
          lastScaleRef.current = scale;
        }
      }
      return;
    }

    const touch = event.touches[0];
    if (!touch) return;
    
    const point: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };

    gestureStateRef.current.currentPoint = point;

    // 如果移动距离超过阈值，取消长按检测
    const distance = calculateDistance(current.startPoint, point);
    if (distance > config.longPressThreshold) {
      clearTimers();
    }
  }, [config, handlers, calculateDistance, calculateTouchDistance, getTouchCenter, clearTimers]);

  // 触摸结束
  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (config.preventDefault) {
      event.preventDefault();
    }

    const current = gestureStateRef.current;
    if (!current.isPressed || !current.startPoint) return;

    clearTimers();

    const touch = event.changedTouches[0];
    if (!touch) return;
    
    const endPoint: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now(),
    };

    gestureStateRef.current = {
      ...current,
      isPressed: false,
      endPoint,
    };

    const distance = calculateDistance(current.startPoint, endPoint);
    const duration = endPoint.timestamp - current.startPoint.timestamp;
    const velocity = distance / duration;

    // 滑动检测
    if (distance >= config.swipeThreshold && velocity >= config.velocityThreshold) {
      const direction = getSwipeDirection(current.startPoint, endPoint);
      const gesture: SwipeGesture = {
        direction,
        distance,
        velocity,
        duration,
      };

      switch (direction) {
        case 'left':
          handlers.onSwipeLeft?.(gesture);
          break;
        case 'right':
          handlers.onSwipeRight?.(gesture);
          break;
        case 'up':
          handlers.onSwipeUp?.(gesture);
          break;
        case 'down':
          handlers.onSwipeDown?.(gesture);
          break;
      }
    } else {
      // 点击检测
      if (distance <= config.doubleTapThreshold) {
        const now = Date.now();
        const lastTap = lastTapRef.current;

        // 双击检测
        if (
          lastTap &&
          now - lastTap.timestamp <= config.doubleTapDelay &&
          calculateDistance(lastTap, endPoint) <= config.doubleTapThreshold
        ) {
          handlers.onDoubleTap?.(endPoint);
          lastTapRef.current = null;
          if (doubleTapTimerRef.current) {
            clearTimeout(doubleTapTimerRef.current);
          }
        } else {
          // 设置单击延迟，等待可能的双击
          lastTapRef.current = endPoint;
          doubleTapTimerRef.current = setTimeout(() => {
            handlers.onTap?.(endPoint);
            lastTapRef.current = null;
          }, config.doubleTapDelay);
        }
      }
    }
  }, [config, handlers, calculateDistance, getSwipeDirection, clearTimers]);

  // 绑定事件监听器
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 添加触摸事件监听器
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      clearTimers();
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, clearTimers]);

  return {
    ref: elementRef,
    gestureState: gestureStateRef.current,
  };
}

/**
 * 简化的滑动检测Hook
 * 专门用于滑动手势检测
 */
export function useSwipe(
  onSwipe: (direction: 'left' | 'right' | 'up' | 'down', gesture: SwipeGesture) => void,
  options: Pick<GestureOptions, 'swipeThreshold' | 'velocityThreshold' | 'preventDefault'> = {}
) {
  return useGestures({
    onSwipeLeft: (gesture) => onSwipe('left', gesture),
    onSwipeRight: (gesture) => onSwipe('right', gesture),
    onSwipeUp: (gesture) => onSwipe('up', gesture),
    onSwipeDown: (gesture) => onSwipe('down', gesture),
  }, options);
}

/**
 * 长按检测Hook
 */
export function useLongPress(
  onLongPress: (point: TouchPoint) => void,
  options: Pick<GestureOptions, 'longPressDelay' | 'longPressThreshold' | 'preventDefault'> = {}
) {
  return useGestures({ onLongPress }, options);
}

/**
 * 双击检测Hook
 */
export function useDoubleTap(
  onDoubleTap: (point: TouchPoint) => void,
  options: Pick<GestureOptions, 'doubleTapDelay' | 'doubleTapThreshold' | 'preventDefault'> = {}
) {
  return useGestures({ onDoubleTap }, options);
} 