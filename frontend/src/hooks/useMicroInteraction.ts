import { useState, useCallback, useRef, useEffect } from 'react';
import { useAnimation } from '../components/animation/AnimationProvider';

export interface MicroInteractionState {
  isHovered: boolean;
  isFocused: boolean;
  isPressed: boolean;
  isActive: boolean;
}

export interface MicroInteractionHandlers {
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  onMouseDown: () => void;
  onMouseUp: () => void;
  onFocus: () => void;
  onBlur: () => void;
  onTouchStart: () => void;
  onTouchEnd: () => void;
}

export interface MicroInteractionOptions {
  enableHover?: boolean;
  enableFocus?: boolean;
  enablePress?: boolean;
  enableTouch?: boolean;
  hoverDelay?: number;
  pressScale?: number;
  bounceOnClick?: boolean;
}

/**
 * 微交互动画hook
 * 提供常见的UI交互状态管理和动画效果
 */
export function useMicroInteraction(options: MicroInteractionOptions = {}) {
  const {
    enableHover = true,
    enableFocus = true,
    enablePress = true,
    enableTouch = true,
    hoverDelay = 0,
    pressScale = 0.95,
    bounceOnClick = false,
  } = options;

  const { shouldAnimate, getDuration } = useAnimation();
  
  const [state, setState] = useState<MicroInteractionState>({
    isHovered: false,
    isFocused: false,
    isPressed: false,
    isActive: false,
  });

  const hoverTimeoutRef = useRef<NodeJS.Timeout>();
  const bounceTimeoutRef = useRef<NodeJS.Timeout>();

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      if (bounceTimeoutRef.current) {
        clearTimeout(bounceTimeoutRef.current);
      }
    };
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (!enableHover || !shouldAnimate()) return;

    if (hoverDelay > 0) {
      hoverTimeoutRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, isHovered: true }));
      }, hoverDelay);
    } else {
      setState(prev => ({ ...prev, isHovered: true }));
    }
  }, [enableHover, hoverDelay, shouldAnimate]);

  const handleMouseLeave = useCallback(() => {
    if (!enableHover) return;

    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    setState(prev => ({ ...prev, isHovered: false }));
  }, [enableHover]);

  const handleMouseDown = useCallback(() => {
    if (!enablePress || !shouldAnimate()) return;
    setState(prev => ({ ...prev, isPressed: true }));
  }, [enablePress, shouldAnimate]);

  const handleMouseUp = useCallback(() => {
    if (!enablePress) return;
    setState(prev => ({ ...prev, isPressed: false }));

    if (bounceOnClick && shouldAnimate()) {
      setState(prev => ({ ...prev, isActive: true }));
      bounceTimeoutRef.current = setTimeout(() => {
        setState(prev => ({ ...prev, isActive: false }));
      }, getDuration(200));
    }
  }, [enablePress, bounceOnClick, shouldAnimate, getDuration]);

  const handleFocus = useCallback(() => {
    if (!enableFocus || !shouldAnimate()) return;
    setState(prev => ({ ...prev, isFocused: true }));
  }, [enableFocus, shouldAnimate]);

  const handleBlur = useCallback(() => {
    if (!enableFocus) return;
    setState(prev => ({ ...prev, isFocused: false }));
  }, [enableFocus]);

  const handleTouchStart = useCallback(() => {
    if (!enableTouch || !shouldAnimate()) return;
    setState(prev => ({ ...prev, isPressed: true }));
  }, [enableTouch, shouldAnimate]);

  const handleTouchEnd = useCallback(() => {
    if (!enableTouch) return;
    setState(prev => ({ ...prev, isPressed: false }));
  }, [enableTouch]);

  const handlers: MicroInteractionHandlers = {
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    onMouseDown: handleMouseDown,
    onMouseUp: handleMouseUp,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
  };

  // 生成动画class名
  const getAnimationClasses = useCallback((baseClasses: string = '') => {
    if (!shouldAnimate()) {
      return baseClasses;
    }

    const animationClasses = [
      'transition-all duration-200 ease-out',
      state.isHovered ? 'scale-105 shadow-lg' : '',
      state.isFocused ? 'ring-2 ring-primary-500 ring-opacity-50' : '',
      state.isPressed ? `scale-[${pressScale}]` : '',
      state.isActive && bounceOnClick ? 'animate-bounce-click' : '',
    ].filter(Boolean).join(' ');

    return `${baseClasses} ${animationClasses}`.trim();
  }, [state, shouldAnimate, pressScale, bounceOnClick]);

  return {
    state,
    handlers,
    getAnimationClasses,
  };
}

/**
 * 按钮专用的微交互hook
 */
export function useButtonInteraction(options: MicroInteractionOptions = {}) {
  return useMicroInteraction({
    enableHover: true,
    enableFocus: true,
    enablePress: true,
    enableTouch: true,
    bounceOnClick: true,
    pressScale: 0.95,
    ...options,
  });
}

/**
 * 卡片专用的微交互hook
 */
export function useCardInteraction(options: MicroInteractionOptions = {}) {
  return useMicroInteraction({
    enableHover: true,
    enableFocus: false,
    enablePress: false,
    enableTouch: false,
    hoverDelay: 100,
    ...options,
  });
}

/**
 * 输入框专用的微交互hook
 */
export function useInputInteraction(options: MicroInteractionOptions = {}) {
  return useMicroInteraction({
    enableHover: false,
    enableFocus: true,
    enablePress: false,
    enableTouch: false,
    ...options,
  });
} 