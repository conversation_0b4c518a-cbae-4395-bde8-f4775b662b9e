import { useState, useEffect, useRef, useCallback } from 'react';
import { useMobile } from './useResponsive';

interface MemoryInfo {
  usedJSHeapSize: number;
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
}

export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage?: MemoryInfo;
  connectionType?: string;
  isSlowConnection: boolean;
  isLowMemory: boolean;
}

export interface LazyImageOptions {
  rootMargin?: string;
  threshold?: number;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export interface VirtualScrollOptions {
  itemHeight: number;
  bufferSize?: number;
  overscan?: number;
}

/**
 * 移动端性能监控和优化Hook
 */
export function useMobilePerformance() {
  const { isMobile, isTouchDevice } = useMobile();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    isSlowConnection: false,
    isLowMemory: false,
  });

  // 检测网络连接类型
  useEffect(() => {
    if (!('connection' in navigator)) return;

    const connection = (navigator as any).connection;
    if (!connection) return;

    const updateConnectionInfo = () => {
      const isSlowConnection = 
        connection.effectiveType === 'slow-2g' || 
        connection.effectiveType === '2g' ||
        (connection.downlink && connection.downlink < 1.5);

      setMetrics(prev => ({
        ...prev,
        connectionType: connection.effectiveType,
        isSlowConnection,
      }));
    };

    updateConnectionInfo();
    connection.addEventListener('change', updateConnectionInfo);

    return () => {
      connection.removeEventListener('change', updateConnectionInfo);
    };
  }, []);

  // 检测内存使用情况
  useEffect(() => {
    if (!('memory' in performance)) return;

    const checkMemory = () => {
      const memory = (performance as any).memory;
      if (memory) {
        const isLowMemory = memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8;
        
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory,
          isLowMemory,
        }));
      }
    };

    checkMemory();
    const interval = setInterval(checkMemory, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, []);

  // 优化建议
  const getOptimizationRecommendations = useCallback(() => {
    const recommendations: string[] = [];

    if (metrics.isSlowConnection) {
      recommendations.push('减少图片质量');
      recommendations.push('启用图片懒加载');
      recommendations.push('使用更小的bundle');
    }

    if (metrics.isLowMemory) {
      recommendations.push('启用虚拟滚动');
      recommendations.push('减少同时渲染的组件数量');
      recommendations.push('清理不必要的事件监听器');
    }

    if (isMobile) {
      recommendations.push('使用触摸友好的组件');
      recommendations.push('优化触摸事件处理');
    }

    return recommendations;
  }, [metrics, isMobile]);

  return {
    metrics,
    recommendations: getOptimizationRecommendations(),
    isMobile,
    isTouchDevice,
  };
}

/**
 * 图片懒加载Hook
 */
export function useLazyImage(src: string, options: LazyImageOptions = {}) {
  const {
    rootMargin = '50px',
    threshold = 0.1,
    fallbackSrc,
    onLoad,
    onError,
  } = options;

  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 设置 Intersection Observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin,
        threshold,
      }
    );

    observerRef.current = observer;

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [rootMargin, threshold]);

  // 加载图片
  useEffect(() => {
    if (!isInView) return;

    const img = new Image();
    img.onload = () => {
      setIsLoaded(true);
      setError(null);
      onLoad?.();
    };
    img.onerror = () => {
      setError('Failed to load image');
      onError?.();
      if (fallbackSrc) {
        setIsLoaded(true); // 使用fallback
      }
    };
    img.src = src;
  }, [isInView, src, fallbackSrc, onLoad, onError]);

  return {
    ref: imgRef,
    src: isLoaded ? (error && fallbackSrc ? fallbackSrc : src) : undefined,
    isLoaded,
    isInView,
    error,
  };
}

/**
 * 虚拟滚动Hook
 */
export function useVirtualScroll<T>(
  items: T[],
  containerHeight: number,
  options: VirtualScrollOptions
) {
  const { itemHeight, bufferSize = 5, overscan = 3 } = options;
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const totalHeight = items.length * itemHeight;

  const startIndex = Math.max(
    0,
    Math.floor(scrollTop / itemHeight) - overscan
  );
  const endIndex = Math.min(
    items.length,
    startIndex + visibleCount + overscan * 2
  );

  const visibleItems = items.slice(startIndex, endIndex).map((item, index) => ({
    item,
    index: startIndex + index,
    style: {
      position: 'absolute' as const,
      top: (startIndex + index) * itemHeight,
      height: itemHeight,
      width: '100%',
    },
  }));

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    scrollElementRef,
    visibleItems,
    totalHeight,
    onScroll: handleScroll,
  };
}

/**
 * 防抖Hook（移动端输入优化）
 */
export function useMobileDebounce<T>(value: T, delay: number = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const { isMobile } = useMobile();

  useEffect(() => {
    // 移动端使用更长的防抖延迟
    const actualDelay = isMobile ? delay * 1.5 : delay;
    
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, actualDelay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay, isMobile]);

  return debouncedValue;
}

/**
 * 移动端滚动优化Hook
 */
export function useMobileScroll() {
  const { isMobile, isTouchDevice } = useMobile();
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  const handleScroll = useCallback(() => {
    setIsScrolling(true);
    
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
  }, []);

  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 移动端滚动优化样式
  const getScrollStyles = useCallback((): React.CSSProperties => {
    if (!isTouchDevice) return {};

    return {
      WebkitOverflowScrolling: 'touch' as const,
      overscrollBehavior: 'contain' as const,
      scrollBehavior: 'smooth' as const,
    };
  }, [isTouchDevice]);

  return {
    isScrolling,
    handleScroll,
    getScrollStyles,
    isMobile,
    isTouchDevice,
  };
}

/**
 * 移动端键盘检测Hook
 */
export function useMobileKeyboard() {
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight);
  const { isMobile } = useMobile();

  useEffect(() => {
    if (!isMobile) return;

    const handleResize = () => {
      const currentHeight = window.innerHeight;
      const heightDifference = viewportHeight - currentHeight;
      
      // 如果高度减少超过150px，认为键盘打开了
      setIsKeyboardOpen(heightDifference > 150);
    };

    const handleVisualViewport = () => {
      if ('visualViewport' in window) {
        const viewport = window.visualViewport as VisualViewport;
        const heightDifference = window.innerHeight - viewport.height;
        setIsKeyboardOpen(heightDifference > 150);
      }
    };

    // 监听窗口尺寸变化
    window.addEventListener('resize', handleResize);
    
    // 监听 Visual Viewport API（更准确）
    if ('visualViewport' in window) {
      window.visualViewport!.addEventListener('resize', handleVisualViewport);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      if ('visualViewport' in window) {
        window.visualViewport!.removeEventListener('resize', handleVisualViewport);
      }
    };
  }, [isMobile, viewportHeight]);

  return {
    isKeyboardOpen,
    viewportHeight,
  };
} 