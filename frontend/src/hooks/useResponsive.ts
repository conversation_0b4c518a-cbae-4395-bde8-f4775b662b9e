import { useState, useEffect, useCallback } from 'react';

export interface BreakpointConfig {
  xs: number;    // 0-575px (extra small phones)
  sm: number;    // 576-767px (small phones)
  md: number;    // 768-991px (tablets)
  lg: number;    // 992-1199px (small laptops)
  xl: number;    // 1200-1399px (laptops)
  xxl: number;   // 1400px+ (large screens)
}

export interface ResponsiveState {
  // 屏幕信息
  width: number;
  height: number;
  
  // 断点状态
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  isXxl: boolean;
  
  // 设备类型
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  
  // 设备特性
  isTouchDevice: boolean;
  isPortrait: boolean;
  isLandscape: boolean;
  
  // 性能相关
  hasHighDPI: boolean;
  supportsHover: boolean;
  
  // 当前断点
  currentBreakpoint: keyof BreakpointConfig;
}

export interface ResponsiveOptions {
  debounceMs?: number;
  breakpoints?: Partial<BreakpointConfig>;
}

const DEFAULT_BREAKPOINTS: BreakpointConfig = {
  xs: 575,
  sm: 767,
  md: 991,
  lg: 1199,
  xl: 1399,
  xxl: Infinity,
};

/**
 * 增强的响应式设计Hook
 * 提供详细的屏幕信息和设备特性检测
 */
export function useResponsive(options: ResponsiveOptions = {}): ResponsiveState {
  const {
    debounceMs = 150,
    breakpoints = {},
  } = options;

  const finalBreakpoints = { ...DEFAULT_BREAKPOINTS, ...breakpoints };

  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      // SSR 默认值
      return {
        width: 1200,
        height: 800,
        isXs: false,
        isSm: false,
        isMd: false,
        isLg: true,
        isXl: false,
        isXxl: false,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isTouchDevice: false,
        isPortrait: false,
        isLandscape: true,
        hasHighDPI: false,
        supportsHover: true,
        currentBreakpoint: 'lg',
      };
    }

    return calculateState(window.innerWidth, window.innerHeight, finalBreakpoints);
  });

  // 计算响应式状态
  const calculateState = useCallback(
    (width: number, height: number, breakpoints: BreakpointConfig): ResponsiveState => {
      // 断点检测
      const isXs = width <= breakpoints.xs;
      const isSm = width > breakpoints.xs && width <= breakpoints.sm;
      const isMd = width > breakpoints.sm && width <= breakpoints.md;
      const isLg = width > breakpoints.md && width <= breakpoints.lg;
      const isXl = width > breakpoints.lg && width <= breakpoints.xl;
      const isXxl = width > breakpoints.xl;

      // 设备类型
      const isMobile = isXs || isSm;
      const isTablet = isMd;
      const isDesktop = isLg || isXl || isXxl;

      // 设备特性
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isPortrait = height > width;
      const isLandscape = width > height;
      const hasHighDPI = window.devicePixelRatio > 1;

      // 检测是否支持hover（非触摸设备）
      const supportsHover = window.matchMedia('(hover: hover)').matches;

      // 当前断点
      let currentBreakpoint: keyof BreakpointConfig = 'lg';
      if (isXs) currentBreakpoint = 'xs';
      else if (isSm) currentBreakpoint = 'sm';
      else if (isMd) currentBreakpoint = 'md';
      else if (isLg) currentBreakpoint = 'lg';
      else if (isXl) currentBreakpoint = 'xl';
      else if (isXxl) currentBreakpoint = 'xxl';

      return {
        width,
        height,
        isXs,
        isSm,
        isMd,
        isLg,
        isXl,
        isXxl,
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
        isPortrait,
        isLandscape,
        hasHighDPI,
        supportsHover,
        currentBreakpoint,
      };
    },
    [finalBreakpoints]
  );

  // 防抖的resize处理
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setState(calculateState(window.innerWidth, window.innerHeight, finalBreakpoints));
      }, debounceMs);
    };

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
    
    // 监听方向变化
    window.addEventListener('orientationchange', handleResize);

    // 初始计算
    setState(calculateState(window.innerWidth, window.innerHeight, finalBreakpoints));

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      clearTimeout(timeoutId);
    };
  }, [calculateState, debounceMs, finalBreakpoints]);

  return state;
}

/**
 * 移动端专用hook
 * 简化的API专门用于移动端检测
 */
export function useMobile() {
  const { isMobile, isTablet, isTouchDevice, isPortrait } = useResponsive();
  return {
    isMobile,
    isTablet,
    isTouchDevice,
    isPortrait,
    isMobileOrTablet: isMobile || isTablet,
  };
}

/**
 * 断点匹配hook
 * 类似于CSS媒体查询的用法
 */
export function useBreakpoint(breakpoint: keyof BreakpointConfig) {
  const responsive = useResponsive();
  
  switch (breakpoint) {
    case 'xs':
      return responsive.isXs;
    case 'sm':
      return responsive.isSm;
    case 'md':
      return responsive.isMd;
    case 'lg':
      return responsive.isLg;
    case 'xl':
      return responsive.isXl;
    case 'xxl':
      return responsive.isXxl;
    default:
      return false;
  }
}

/**
 * 最小宽度断点检测
 * 检测屏幕宽度是否大于等于指定断点
 */
export function useBreakpointUp(breakpoint: keyof BreakpointConfig) {
  const responsive = useResponsive();
  
  switch (breakpoint) {
    case 'xs':
      return true; // xs及以上始终为true
    case 'sm':
      return !responsive.isXs;
    case 'md':
      return responsive.isDesktop || responsive.isTablet;
    case 'lg':
      return responsive.isLg || responsive.isXl || responsive.isXxl;
    case 'xl':
      return responsive.isXl || responsive.isXxl;
    case 'xxl':
      return responsive.isXxl;
    default:
      return false;
  }
}

/**
 * 最大宽度断点检测
 * 检测屏幕宽度是否小于等于指定断点
 */
export function useBreakpointDown(breakpoint: keyof BreakpointConfig) {
  const responsive = useResponsive();
  
  switch (breakpoint) {
    case 'xs':
      return responsive.isXs;
    case 'sm':
      return responsive.isXs || responsive.isSm;
    case 'md':
      return responsive.isMobile || responsive.isTablet;
    case 'lg':
      return !responsive.isXl && !responsive.isXxl;
    case 'xl':
      return !responsive.isXxl;
    case 'xxl':
      return true; // xxl及以下始终为true
    default:
      return false;
  }
} 