/**
 * useSession Hook - 会话管理
 * 提供用户会话状态管理和本地存储支持
 */

import { useState, useCallback, useEffect } from 'react';

// 会话状态接口
export interface SessionState {
  isAuthenticated: boolean;
  user: UserProfile | null;
  sessionId: string | null;
  expiresAt: number | null;
  lastActivity: number;
  preferences: UserPreferences;
}

// 用户配置文件接口
export interface UserProfile {
  id: string;
  username?: string;
  email?: string;
  avatar?: string;
  createdAt?: string;
  lastLoginAt?: string;
}

// 用户偏好设置接口
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'zh' | 'en';
  autoSave: boolean;
  notifications: boolean;
  chatSettings: {
    streaming: boolean;
    autoScroll: boolean;
    showTimestamps: boolean;
  };
  documentSettings: {
    defaultView: 'list' | 'grid';
    autoRefresh: boolean;
    showPreview: boolean;
  };
}

// Hook选项
export interface UseSessionOptions {
  persistSession?: boolean;
  sessionTimeout?: number; // 毫秒
  autoRefresh?: boolean;
  onSessionExpired?: () => void;
  onUserUpdated?: (user: UserProfile) => void;
}

// Hook返回类型
export interface UseSessionReturn {
  // 状态
  state: SessionState;
  
  // 会话操作
  startSession: (user: UserProfile, sessionId?: string) => void;
  endSession: () => void;
  refreshSession: () => void;
  isSessionValid: () => boolean;
  
  // 用户操作
  updateUser: (user: Partial<UserProfile>) => void;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  
  // 活动追踪
  updateActivity: () => void;
  
  // 工具函数
  getSessionInfo: () => {
    duration: number;
    timeLeft: number;
    isExpiring: boolean;
  };
}

// 本地存储键
const STORAGE_KEYS = {
  SESSION_STATE: 'user_session',
  USER_PREFERENCES: 'user_preferences',
  LAST_ACTIVITY: 'last_activity',
} as const;

// 默认偏好设置
const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'zh',
  autoSave: true,
  notifications: true,
  chatSettings: {
    streaming: true,
    autoScroll: true,
    showTimestamps: false,
  },
  documentSettings: {
    defaultView: 'list',
    autoRefresh: true,
    showPreview: true,
  },
};

// 默认选项
const DEFAULT_OPTIONS: Required<UseSessionOptions> = {
  persistSession: true,
  sessionTimeout: 24 * 60 * 60 * 1000, // 24小时
  autoRefresh: true,
  onSessionExpired: () => {},
  onUserUpdated: () => {},
};

export function useSession(options: UseSessionOptions = {}): UseSessionReturn {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // 初始化状态
  const [state, setState] = useState<SessionState>(() => {
    if (opts.persistSession) {
      try {
        const storedSession = localStorage.getItem(STORAGE_KEYS.SESSION_STATE);
        const storedPreferences = localStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
        const storedActivity = localStorage.getItem(STORAGE_KEYS.LAST_ACTIVITY);
        
        const preferences = storedPreferences 
          ? { ...DEFAULT_PREFERENCES, ...JSON.parse(storedPreferences) }
          : DEFAULT_PREFERENCES;
        
        if (storedSession) {
          const session = JSON.parse(storedSession);
          const now = Date.now();
          
          // 检查会话是否过期
          if (session.expiresAt && session.expiresAt > now) {
            return {
              ...session,
              preferences,
              lastActivity: storedActivity ? parseInt(storedActivity) : now,
            };
          }
        }
        
        return {
          isAuthenticated: false,
          user: null,
          sessionId: null,
          expiresAt: null,
          lastActivity: Date.now(),
          preferences,
        };
      } catch (error) {
        console.warn('恢复会话状态失败:', error);
      }
    }
    
    return {
      isAuthenticated: false,
      user: null,
      sessionId: null,
      expiresAt: null,
      lastActivity: Date.now(),
      preferences: DEFAULT_PREFERENCES,
    };
  });

  // 保存状态到本地存储
  const saveToStorage = useCallback((newState: SessionState) => {
    if (opts.persistSession) {
      try {
        const sessionData = {
          isAuthenticated: newState.isAuthenticated,
          user: newState.user,
          sessionId: newState.sessionId,
          expiresAt: newState.expiresAt,
        };
        
        localStorage.setItem(STORAGE_KEYS.SESSION_STATE, JSON.stringify(sessionData));
        localStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(newState.preferences));
        localStorage.setItem(STORAGE_KEYS.LAST_ACTIVITY, newState.lastActivity.toString());
      } catch (error) {
        console.warn('保存会话状态失败:', error);
      }
    }
  }, [opts.persistSession]);

  // 开始会话
  const startSession = useCallback((user: UserProfile, sessionId?: string) => {
    const now = Date.now();
    const newState: SessionState = {
      isAuthenticated: true,
      user,
      sessionId: sessionId || `session_${now}_${Math.random().toString(36).substr(2, 9)}`,
      expiresAt: now + opts.sessionTimeout,
      lastActivity: now,
      preferences: state.preferences,
    };
    
    setState(newState);
    saveToStorage(newState);
    opts.onUserUpdated(user);
  }, [opts, state.preferences, saveToStorage]);

  // 结束会话
  const endSession = useCallback(() => {
    const newState: SessionState = {
      isAuthenticated: false,
      user: null,
      sessionId: null,
      expiresAt: null,
      lastActivity: Date.now(),
      preferences: state.preferences,
    };
    
    setState(newState);
    
    if (opts.persistSession) {
      localStorage.removeItem(STORAGE_KEYS.SESSION_STATE);
      localStorage.removeItem(STORAGE_KEYS.LAST_ACTIVITY);
      // 保留用户偏好设置
    }
  }, [opts.persistSession, state.preferences]);

  // 刷新会话
  const refreshSession = useCallback(() => {
    if (state.isAuthenticated && state.sessionId) {
      const now = Date.now();
      const newState = {
        ...state,
        expiresAt: now + opts.sessionTimeout,
        lastActivity: now,
      };
      
      setState(newState);
      saveToStorage(newState);
    }
  }, [state, opts.sessionTimeout, saveToStorage]);

  // 检查会话是否有效
  const isSessionValid = useCallback(() => {
    if (!state.isAuthenticated || !state.expiresAt) {
      return false;
    }
    
    return Date.now() < state.expiresAt;
  }, [state.isAuthenticated, state.expiresAt]);

  // 更新用户信息
  const updateUser = useCallback((userUpdates: Partial<UserProfile>) => {
    if (state.user) {
      const updatedUser = { ...state.user, ...userUpdates };
      const newState = { ...state, user: updatedUser };
      
      setState(newState);
      saveToStorage(newState);
      opts.onUserUpdated(updatedUser);
    }
  }, [state, saveToStorage, opts]);

  // 更新偏好设置
  const updatePreferences = useCallback((preferencesUpdates: Partial<UserPreferences>) => {
    const updatedPreferences = { ...state.preferences, ...preferencesUpdates };
    const newState = { ...state, preferences: updatedPreferences };
    
    setState(newState);
    saveToStorage(newState);
  }, [state, saveToStorage]);

  // 重置偏好设置
  const resetPreferences = useCallback(() => {
    const newState = { ...state, preferences: DEFAULT_PREFERENCES };
    setState(newState);
    saveToStorage(newState);
  }, [state, saveToStorage]);

  // 更新活动时间
  const updateActivity = useCallback(() => {
    const now = Date.now();
    const newState = { ...state, lastActivity: now };
    
    setState(newState);
    
    if (opts.persistSession) {
      localStorage.setItem(STORAGE_KEYS.LAST_ACTIVITY, now.toString());
    }
  }, [state, opts.persistSession]);

  // 获取会话信息
  const getSessionInfo = useCallback(() => {
    const now = Date.now();
    const duration = now - (state.lastActivity || now);
    const timeLeft = state.expiresAt ? Math.max(0, state.expiresAt - now) : 0;
    const isExpiring = timeLeft > 0 && timeLeft < 5 * 60 * 1000; // 5分钟内过期
    
    return {
      duration,
      timeLeft,
      isExpiring,
    };
  }, [state.lastActivity, state.expiresAt]);

  // 自动刷新和过期检查
  useEffect(() => {
    if (!opts.autoRefresh) return;
    
    const interval = setInterval(() => {
      if (state.isAuthenticated) {
        if (!isSessionValid()) {
          opts.onSessionExpired();
          endSession();
        } else {
          // 自动刷新活动时间
          updateActivity();
        }
      }
    }, 60000); // 每分钟检查一次
    
    return () => clearInterval(interval);
  }, [state.isAuthenticated, isSessionValid, endSession, updateActivity, opts]);

  return {
    state,
    
    // 会话操作
    startSession,
    endSession,
    refreshSession,
    isSessionValid,
    
    // 用户操作
    updateUser,
    updatePreferences,
    resetPreferences,
    
    // 活动追踪
    updateActivity,
    
    // 工具函数
    getSessionInfo,
  };
} 