/**
 * 流式响应处理Hook
 * 支持Server-Sent Events、自动重连、错误处理
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { ChatService } from '../services/chatService';
import { StreamChatResponse, ChatRequest, ApiError } from '../types';

export interface StreamingState {
  isConnected: boolean;
  isStreaming: boolean;
  content: string;
  error: string | null;
  connectionState: 'idle' | 'connecting' | 'connected' | 'disconnected' | 'error';
  retryCount: number;
}

export interface StreamingOptions {
  maxRetries?: number;
  retryDelay?: number;
  autoReconnect?: boolean;
  onChunk?: (chunk: StreamChatResponse) => void;
  onComplete?: (finalContent: string) => void;
  onError?: (error: ApiError) => void;
  onStateChange?: (state: StreamingState) => void;
}

const DEFAULT_OPTIONS: Required<StreamingOptions> = {
  maxRetries: 3,
  retryDelay: 1000,
  autoReconnect: true,
  onChunk: () => {},
  onComplete: () => {},
  onError: () => {},
  onStateChange: () => {},
};

export function useStreaming(options: StreamingOptions = {}) {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  const [state, setState] = useState<StreamingState>({
    isConnected: false,
    isStreaming: false,
    content: '',
    error: null,
    connectionState: 'idle',
    retryCount: 0,
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const abortControllerRef = useRef<AbortController>();
  const accumulatedContentRef = useRef<string>('');

  // 更新状态并通知回调
  const updateState = useCallback((updates: Partial<StreamingState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      opts.onStateChange(newState);
      return newState;
    });
  }, [opts]);

  // 重置状态
  const reset = useCallback(() => {
    accumulatedContentRef.current = '';
    updateState({
      isConnected: false,
      isStreaming: false,
      content: '',
      error: null,
      connectionState: 'idle',
      retryCount: 0,
    });
  }, [updateState]);

  // 处理流式数据块
  const handleChunk = useCallback((chunk: StreamChatResponse) => {
    if (chunk.chunk) {
      accumulatedContentRef.current += chunk.chunk;
      updateState({
        content: accumulatedContentRef.current,
        isStreaming: true,
        connectionState: 'connected',
      });
      opts.onChunk(chunk);
    }
  }, [updateState, opts]);

  // 处理流式完成
  const handleComplete = useCallback(() => {
    updateState({
      isStreaming: false,
      connectionState: 'disconnected',
    });
    opts.onComplete(accumulatedContentRef.current);
  }, [updateState, opts]);

  // 处理错误
  const handleError = useCallback((error: ApiError) => {
    console.error('Streaming error:', error);
    
    updateState({
      error: error.message,
      isStreaming: false,
      connectionState: 'error',
    });

    opts.onError(error);

    // 自动重连逻辑
    if (opts.autoReconnect && state.retryCount < opts.maxRetries) {
      const delay = opts.retryDelay * Math.pow(2, state.retryCount); // 指数退避
      
      retryTimeoutRef.current = setTimeout(() => {
        updateState({
          retryCount: state.retryCount + 1,
          connectionState: 'connecting',
          error: null,
        });
      }, delay);
    }
  }, [state.retryCount, updateState, opts]);

  // 开始流式请求
  const startStream = useCallback(async (request: ChatRequest) => {
    // 清理之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }

    // 重置状态
    accumulatedContentRef.current = '';
    updateState({
      isConnected: true,
      isStreaming: true,
      content: '',
      error: null,
      connectionState: 'connecting',
    });

    try {
      // 启动流式请求
      await ChatService.streamMessage(
        { ...request, stream: true },
        handleChunk,
        handleError,
        handleComplete
      );
      
      updateState({
        connectionState: 'connected',
      });
    } catch (error) {
      handleError(error as ApiError);
    }
  }, [updateState, handleChunk, handleError, handleComplete]);

  // 停止流式请求
  const stopStream = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    
    updateState({
      isConnected: false,
      isStreaming: false,
      connectionState: 'disconnected',
    });
  }, [updateState]);

  // 重试连接
  const retry = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    
    updateState({
      retryCount: 0,
      error: null,
      connectionState: 'connecting',
    });
  }, [updateState]);

  // 清理副作用
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    state,
    startStream,
    stopStream,
    retry,
    reset,
  };
}

export default useStreaming; 