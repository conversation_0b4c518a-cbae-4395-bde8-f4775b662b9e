import { useState, useEffect, useCallback } from 'react';

export type ThemeMode = 'light' | 'dark' | 'system';
export type ThemePreset = 'default' | 'blue' | 'purple' | 'green' | 'orange';

export interface ThemeConfig {
  mode: ThemeMode;
  preset: ThemePreset;
  animated: boolean;
  autoSwitch: boolean;
  autoSwitchTime?: {
    light: string; // "07:00"
    dark: string;  // "19:00"
  };
}

interface UseThemeReturn {
  theme: ThemeMode;
  preset: ThemePreset;
  isDarkMode: boolean;
  config: ThemeConfig;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
  setPreset: (preset: ThemePreset) => void;
  updateConfig: (config: Partial<ThemeConfig>) => void;
  resetToDefault: () => void;
}

const DEFAULT_CONFIG: ThemeConfig = {
  mode: 'system',
  preset: 'default',
  animated: true,
  autoSwitch: false,
  autoSwitchTime: {
    light: '07:00',
    dark: '19:00'
  }
};

const THEME_PRESETS: Record<ThemePreset, Record<string, string>> = {
  default: {
    '--primary-hue': '217', // blue
    '--primary-saturation': '91%',
  },
  blue: {
    '--primary-hue': '217',
    '--primary-saturation': '91%',
  },
  purple: {
    '--primary-hue': '262',
    '--primary-saturation': '83%',
  },
  green: {
    '--primary-hue': '142',
    '--primary-saturation': '76%',
  },
  orange: {
    '--primary-hue': '25',
    '--primary-saturation': '95%',
  }
};

/**
 * 增强版主题管理Hook
 * 支持多种主题模式、预设颜色、动画效果和自动切换
 */
export const useTheme = (): UseThemeReturn => {
  const [config, setConfigState] = useState<ThemeConfig>(() => {
    if (typeof window !== 'undefined') {
      const savedConfig = localStorage.getItem('theme-config');
      if (savedConfig) {
        try {
          const parsed = JSON.parse(savedConfig);
          return { ...DEFAULT_CONFIG, ...parsed };
        } catch {
          return DEFAULT_CONFIG;
        }
      }
    }
    return DEFAULT_CONFIG;
  });

  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  });

  // 计算实际使用的主题
  const theme = config.mode === 'system' ? systemTheme : config.mode;
  const isDarkMode = theme === 'dark';

  // 保存配置到localStorage
  const saveConfig = useCallback((newConfig: ThemeConfig) => {
    localStorage.setItem('theme-config', JSON.stringify(newConfig));
  }, []);

  // 更新配置
  const updateConfig = useCallback((partialConfig: Partial<ThemeConfig>) => {
    setConfigState(prevConfig => {
      const newConfig = { ...prevConfig, ...partialConfig };
      saveConfig(newConfig);
      return newConfig;
    });
  }, [saveConfig]);

  // 设置主题模式
  const setTheme = useCallback((newTheme: ThemeMode) => {
    updateConfig({ mode: newTheme });
  }, [updateConfig]);

  // 设置主题预设
  const setPreset = useCallback((newPreset: ThemePreset) => {
    updateConfig({ preset: newPreset });
  }, [updateConfig]);

  // 切换主题
  const toggleTheme = useCallback(() => {
    if (config.mode === 'system') {
      setTheme(systemTheme === 'dark' ? 'light' : 'dark');
    } else {
      setTheme(config.mode === 'light' ? 'dark' : 'light');
    }
  }, [config.mode, systemTheme, setTheme]);

  // 重置为默认配置
  const resetToDefault = useCallback(() => {
    setConfigState(DEFAULT_CONFIG);
    saveConfig(DEFAULT_CONFIG);
  }, [saveConfig]);

  // 应用主题到DOM
  const applyThemeToDOM = useCallback((currentTheme: 'light' | 'dark', preset: ThemePreset, animated: boolean) => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    
    // 添加/移除动画类
    if (animated) {
      root.style.transition = 'background-color 0.3s ease, color 0.3s ease';
    } else {
      root.style.transition = '';
    }

    // 设置主题模式
    if (currentTheme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // 应用主题预设
    const presetVars = THEME_PRESETS[preset];
    Object.entries(presetVars).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // 设置主题标识
    root.setAttribute('data-theme', `${currentTheme}-${preset}`);
  }, []);

  // 检查是否需要自动切换主题
  const checkAutoSwitch = useCallback(() => {
    if (!config.autoSwitch || config.mode !== 'system') return;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const lightTime = config.autoSwitchTime?.light || '07:00';
    const darkTime = config.autoSwitchTime?.dark || '19:00';

    if (currentTime >= darkTime || currentTime < lightTime) {
      if (systemTheme !== 'dark') {
        setSystemTheme('dark');
      }
    } else {
      if (systemTheme !== 'light') {
        setSystemTheme('light');
      }
    }
  }, [config.autoSwitch, config.mode, config.autoSwitchTime, systemTheme]);

  // 监听系统主题变化
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // 应用主题变化
  useEffect(() => {
    applyThemeToDOM(theme, config.preset, config.animated);
  }, [theme, config.preset, config.animated, applyThemeToDOM]);

  // 自动切换主题检查
  useEffect(() => {
    if (!config.autoSwitch) return;

    checkAutoSwitch();
    const interval = setInterval(checkAutoSwitch, 60000); // 每分钟检查一次

    return () => clearInterval(interval);
  }, [config.autoSwitch, checkAutoSwitch]);

  // 页面可见性变化时检查自动切换
  useEffect(() => {
    if (!config.autoSwitch || typeof window === 'undefined') return;

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        checkAutoSwitch();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [config.autoSwitch, checkAutoSwitch]);

  return {
    theme: config.mode,
    preset: config.preset,
    isDarkMode,
    config,
    toggleTheme,
    setTheme,
    setPreset,
    updateConfig,
    resetToDefault
  };
};

export default useTheme; 