import React from 'react'
import ReactDOM from 'react-dom/client'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { createOptimizedQueryClient } from './config/cache'
import AppRouter from './routes/index'
import { initPreloadStrategy } from './config/preloadStrategy'
import './styles/index.css'

// 创建优化的 React Query 客户端
const queryClient = createOptimizedQueryClient({
  enablePersistence: true,
  enableDevtools: process.env.NODE_ENV === 'development',
})

// 初始化预加载策略
initPreloadStrategy()

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AppRouter />
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  </React.StrictMode>,
)