import React from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import Layout from '../components/Layout';
import SuspenseBoundary from '../components/common/SuspenseBoundary';
import { createLazyComponent } from '../utils/preload';

// 使用懒加载创建页面组件
const HomePage = createLazyComponent('home', () => import('../components/HomePage'));
const ChatPage = createLazyComponent('chat', () => import('../components/ChatPage'));
const DocumentsPage = createLazyComponent('documents', () => import('../components/DocumentsPage'));

// 代码分割演示页面
const CodeSplittingDemo = createLazyComponent(
  'codeSplitting', 
  () => import('../components/CodeSplittingDemo')
);

// 缓存性能监控页面
const CachePerformanceMonitor = createLazyComponent(
  'cacheMonitor',
  () => import('../components/cache/CachePerformanceMonitor')
);

// 缓存优化演示页面
const CacheOptimizationDemo = createLazyComponent(
  'cacheDemo',
  () => import('../components/cache/CacheOptimizationDemo')
);

// 错误页面组件
const ErrorPage: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">页面加载错误</h1>
      <p className="text-gray-600 mb-4">抱歉，页面加载时出现了错误。</p>
      <button 
        onClick={() => window.location.reload()}
        className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
      >
        刷新页面
      </button>
    </div>
  </div>
);

// 路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: (
          <SuspenseBoundary loadingText="首页加载中...">
            <HomePage />
          </SuspenseBoundary>
        ),
      },
      {
        path: 'chat/:sessionId?',
        element: (
          <SuspenseBoundary loadingText="聊天页面加载中...">
            <ChatPage />
          </SuspenseBoundary>
        ),
      },
      {
        path: 'documents',
        element: (
          <SuspenseBoundary loadingText="文档管理页面加载中...">
            <DocumentsPage />
          </SuspenseBoundary>
        ),
      },
      {
        path: 'performance',
        element: (
          <SuspenseBoundary loadingText="性能演示页面加载中...">
            <CodeSplittingDemo />
          </SuspenseBoundary>
        ),
      },
      {
        path: 'cache-monitor',
        element: (
          <SuspenseBoundary loadingText="缓存监控页面加载中...">
            <CachePerformanceMonitor />
          </SuspenseBoundary>
        ),
      },
      {
        path: 'cache-demo',
        element: (
          <SuspenseBoundary loadingText="缓存演示页面加载中...">
            <CacheOptimizationDemo />
          </SuspenseBoundary>
        ),
      },
    ],
  },
]);

// 路由提供者组件
export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};

export default AppRouter;