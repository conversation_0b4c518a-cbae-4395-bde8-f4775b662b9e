/**
 * 基础API客户端
 * 提供HTTP请求封装、拦截器和错误处理
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiError } from '../types';

// 扩展axios配置类型以支持metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: number;
    };
  }
}

// API配置
const API_CONFIG = {
  baseURL: '/api/v1', // 使用Vite代理配置
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
};

// 创建axios实例
const apiClient: AxiosInstance = axios.create(API_CONFIG);

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加请求时间戳
    config.metadata = { startTime: Date.now() };
    
    // 这里可以添加认证token等
    // const token = localStorage.getItem('auth_token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }

    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 计算请求耗时
    const duration = Date.now() - (response.config.metadata?.startTime || 0);
    console.log(
      `✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`
    );
    
    return response;
  },
  (error) => {
    // 统一错误处理
    const apiError = handleApiError(error);
    console.error('❌ API Error:', apiError);
    return Promise.reject(apiError);
  }
);

// 错误处理函数
function handleApiError(error: any): ApiError {
  if (error.response) {
    // 服务器响应了错误状态码
    const { status, data } = error.response;
    
    return {
      error: true,
      message: data?.message || `HTTP Error ${status}`,
      status_code: status,
      details: data?.details || data,
    };
  } else if (error.request) {
    // 请求已发送但没有收到响应
    return {
      error: true,
      message: '网络连接失败，请检查网络设置',
      status_code: 0,
      details: error.request,
    };
  } else {
    // 其他错误
    return {
      error: true,
      message: error.message || '未知错误',
      status_code: -1,
      details: error,
    };
  }
}

// API方法封装
export class ApiClient {
  /**
   * GET请求
   */
  static async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await apiClient.get<T>(url, config);
    return response.data;
  }

  /**
   * POST请求
   */
  static async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await apiClient.post<T>(url, data, config);
    return response.data;
  }

  /**
   * PUT请求
   */
  static async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await apiClient.put<T>(url, data, config);
    return response.data;
  }

  /**
   * DELETE请求
   */
  static async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await apiClient.delete<T>(url, config);
    return response.data;
  }

  /**
   * PATCH请求
   */
  static async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await apiClient.patch<T>(url, data, config);
    return response.data;
  }

  /**
   * 文件上传
   */
  static async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const uploadConfig: AxiosRequestConfig = {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(progress);
        }
      },
    };

    const response = await apiClient.post<T>(url, formData, uploadConfig);
    return response.data;
  }

  /**
   * 流式请求（用于Server-Sent Events）
   */
  static async stream(
    url: string,
    data?: any,
    onMessage?: (chunk: string) => void,
    onError?: (error: ApiError) => void,
    onComplete?: () => void
  ): Promise<void> {
    try {
      const response = await fetch(`${window.location.origin}/api/v1${url}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('Stream reader not available');
      }

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { value, done } = await reader.read();
        
        if (done) {
          onComplete?.();
          break;
        }

        const chunk = decoder.decode(value);
        onMessage?.(chunk);
      }
    } catch (error: any) {
      const apiError = handleApiError(error);
      onError?.(apiError);
    }
  }
}

// 导出实例和配置
export { apiClient, API_CONFIG };
export default ApiClient; 