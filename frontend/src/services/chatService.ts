/**
 * 聊天服务
 * 提供聊天相关的API调用方法
 */

import ApiClient from './api';
import {
  ChatRequest,
  ChatResponse,
  StreamChatResponse,
  ChatSessionResponse,
  ChatSessionCreate,
  ChatSessionUpdate,
  ChatSessionListResponse,
  ChatHistoryResponse,
  ChatMessageResponse,
  PaginationParams,
  ApiError,
} from '../types';

export class ChatService {
  /**
   * 发送聊天消息
   */
  static async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    return ApiClient.post<ChatResponse>('/chat/message', request);
  }

  /**
   * 流式聊天消息
   */
  static async streamMessage(
    request: ChatRequest,
    onMessage: (chunk: StreamChatResponse) => void,
    onError: (error: ApiError) => void,
    onComplete: () => void
  ): Promise<void> {
    return ApiClient.stream(
      '/chat/stream',
      request,
      (chunk: string) => {
        try {
          // 解析每个chunk的JSON数据
          const lines = chunk.split('\n').filter(line => line.trim());
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonStr = line.substring(6);
              if (jsonStr === '[DONE]') {
                onComplete();
                return;
              }
              const data = JSON.parse(jsonStr) as StreamChatResponse;
              onMessage(data);
            }
          }
        } catch (error) {
          console.warn('解析流式响应失败:', error);
        }
      },
      onError,
      onComplete
    );
  }

  /**
   * 获取聊天历史
   */
  static async getChatHistory(sessionId: string): Promise<ChatHistoryResponse> {
    return ApiClient.get<ChatHistoryResponse>(`/chat/history/${sessionId}`);
  }

  /**
   * 创建新的聊天会话
   */
  static async createSession(data: ChatSessionCreate): Promise<ChatSessionResponse> {
    return ApiClient.post<ChatSessionResponse>('/sessions', data);
  }

  /**
   * 获取会话列表
   */
  static async getSessions(
    params?: PaginationParams
  ): Promise<ChatSessionListResponse> {
    return ApiClient.get<ChatSessionListResponse>('/sessions', { params });
  }

  /**
   * 获取特定会话详情
   */
  static async getSession(sessionId: string): Promise<ChatSessionResponse> {
    return ApiClient.get<ChatSessionResponse>(`/sessions/${sessionId}`);
  }

  /**
   * 更新会话信息
   */
  static async updateSession(
    sessionId: string,
    data: ChatSessionUpdate
  ): Promise<ChatSessionResponse> {
    return ApiClient.put<ChatSessionResponse>(`/sessions/${sessionId}`, data);
  }

  /**
   * 删除会话
   */
  static async deleteSession(sessionId: string): Promise<void> {
    return ApiClient.delete(`/sessions/${sessionId}`);
  }

  /**
   * 获取会话中的消息列表
   */
  static async getSessionMessages(
    sessionId: string,
    params?: PaginationParams
  ): Promise<ChatMessageResponse[]> {
    return ApiClient.get<ChatMessageResponse[]>(`/sessions/${sessionId}/messages`, {
      params,
    });
  }

  /**
   * 清空会话消息
   */
  static async clearSessionMessages(sessionId: string): Promise<void> {
    return ApiClient.delete(`/sessions/${sessionId}/messages`);
  }

  /**
   * 重新生成会话标题
   */
  static async regenerateSessionTitle(sessionId: string): Promise<ChatSessionResponse> {
    return ApiClient.post<ChatSessionResponse>(`/sessions/${sessionId}/regenerate-title`);
  }

  /**
   * 搜索会话
   */
  static async searchSessions(
    query: string,
    params?: PaginationParams
  ): Promise<ChatSessionListResponse> {
    return ApiClient.get<ChatSessionListResponse>('/sessions/search', {
      params: { query, ...params },
    });
  }

  /**
   * 导出会话数据
   */
  static async exportSession(sessionId: string, format: 'json' | 'markdown' = 'json'): Promise<Blob> {
    const response = await ApiClient.get(`/sessions/${sessionId}/export`, {
      params: { format },
      responseType: 'blob',
    });
    return response;
  }

  /**
   * 批量删除会话
   */
  static async batchDeleteSessions(sessionIds: string[]): Promise<void> {
    return ApiClient.post('/sessions/batch-delete', { session_ids: sessionIds });
  }
}

export default ChatService; 