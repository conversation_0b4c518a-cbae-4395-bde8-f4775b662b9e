/**
 * 文档服务
 * 提供文档管理相关的API调用方法
 */

import ApiClient from './api';
import {
  DocumentResponse,
  DocumentListResponse,
  DocumentUploadResponse,
  DocumentProcessingStatus,
  DocumentSearchRequest,
  DocumentSearchResponse,
  PaginationParams,
  FileUploadProgress,
} from '../types';

export class DocumentService {
  /**
   * 上传文档
   */
  static async uploadDocument(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<DocumentUploadResponse> {
    return ApiClient.upload<DocumentUploadResponse>('/documents/upload', file, onProgress);
  }

  /**
   * 批量上传文档
   */
  static async uploadDocuments(
    files: File[],
    onProgress?: (fileIndex: number, progress: number) => void,
    onFileComplete?: (fileIndex: number, result: DocumentUploadResponse | Error) => void
  ): Promise<(DocumentUploadResponse | Error)[]> {
    const uploadPromises = files.map((file, index) =>
      this.uploadDocument(
        file,
        (progress) => onProgress?.(index, progress)
      )
        .then((result) => {
          onFileComplete?.(index, result);
          return result;
        })
        .catch((error) => {
          onFileComplete?.(index, error);
          return error;
        })
    );

    return Promise.all(uploadPromises);
  }

  /**
   * 获取文档列表
   */
  static async getDocuments(
    params?: PaginationParams & {
      status?: string;
      search?: string;
    }
  ): Promise<DocumentListResponse> {
    return ApiClient.get<DocumentListResponse>('/documents', { params });
  }

  /**
   * 获取特定文档详情
   */
  static async getDocument(documentId: string): Promise<DocumentResponse> {
    return ApiClient.get<DocumentResponse>(`/documents/${documentId}`);
  }

  /**
   * 删除文档
   */
  static async deleteDocument(documentId: string): Promise<void> {
    return ApiClient.delete(`/documents/${documentId}`);
  }

  /**
   * 批量删除文档
   */
  static async batchDeleteDocuments(documentIds: string[]): Promise<void> {
    return ApiClient.post('/documents/batch-delete', { document_ids: documentIds });
  }

  /**
   * 获取文档处理状态
   */
  static async getDocumentStatus(documentId: string): Promise<DocumentProcessingStatus> {
    return ApiClient.get<DocumentProcessingStatus>(`/documents/${documentId}/status`);
  }

  /**
   * 重新处理文档
   */
  static async reprocessDocument(documentId: string): Promise<DocumentProcessingStatus> {
    return ApiClient.post<DocumentProcessingStatus>(`/documents/${documentId}/reprocess`);
  }

  /**
   * 搜索文档内容
   */
  static async searchDocuments(
    request: DocumentSearchRequest
  ): Promise<DocumentSearchResponse> {
    return ApiClient.post<DocumentSearchResponse>('/documents/search', request);
  }

  /**
   * 下载文档
   */
  static async downloadDocument(documentId: string): Promise<Blob> {
    return ApiClient.get(`/documents/${documentId}/download`, {
      responseType: 'blob',
    });
  }

  /**
   * 获取文档预览
   */
  static async getDocumentPreview(
    documentId: string,
    pageNumber?: number
  ): Promise<string> {
    return ApiClient.get<string>(`/documents/${documentId}/preview`, {
      params: { page: pageNumber },
    });
  }

  /**
   * 获取文档统计信息
   */
  static async getDocumentStats(): Promise<{
    total: number;
    processed: number;
    processing: number;
    failed: number;
    total_size_mb: number;
  }> {
    return ApiClient.get('/documents/stats');
  }

  /**
   * 检查文件类型是否支持
   */
  static isSupportedFileType(file: File): boolean {
    const supportedTypes = [
      'text/plain',
      'text/markdown',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
    ];

    const supportedExtensions = ['.txt', '.md', '.pdf', '.docx', '.doc'];

    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    return (
      supportedTypes.includes(file.type) ||
      supportedExtensions.includes(fileExtension)
    );
  }

  /**
   * 验证文件大小
   */
  static validateFileSize(file: File, maxSizeMB: number = 10): boolean {
    const fileSizeMB = file.size / (1024 * 1024);
    return fileSizeMB <= maxSizeMB;
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取文件图标类名
   */
  static getFileIcon(filename: string): string {
    const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));

    const iconMap: Record<string, string> = {
      '.pdf': 'file-pdf',
      '.doc': 'file-word',
      '.docx': 'file-word',
      '.txt': 'file-text',
      '.md': 'file-text',
    };

    return iconMap[extension] || 'file';
  }

  /**
   * 创建文件上传进度跟踪器
   */
  static createUploadTracker(files: File[]): Map<string, FileUploadProgress> {
    const tracker = new Map<string, FileUploadProgress>();

    files.forEach((file) => {
      const key = `${file.name}-${file.size}`;
      tracker.set(key, {
        file,
        progress: 0,
        status: 'pending',
      });
    });

    return tracker;
  }

  /**
   * 轮询文档处理状态
   */
  static async pollDocumentStatus(
    documentId: string,
    onStatusUpdate?: (status: DocumentProcessingStatus) => void,
    maxAttempts: number = 60,
    intervalMs: number = 2000
  ): Promise<DocumentProcessingStatus> {
    let attempts = 0;

    const poll = async (): Promise<DocumentProcessingStatus> => {
      const status = await this.getDocumentStatus(documentId);
      onStatusUpdate?.(status);

      if (status.processing_status === 'completed' || status.processing_status === 'failed') {
        return status;
      }

      attempts++;
      if (attempts >= maxAttempts) {
        throw new Error('文档处理超时');
      }

      await new Promise((resolve) => setTimeout(resolve, intervalMs));
      return poll();
    };

    return poll();
  }
}

export default DocumentService; 