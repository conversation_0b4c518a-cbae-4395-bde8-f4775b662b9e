/**
 * 健康检查服务
 * 提供系统状态检查的API调用方法
 */

import ApiClient from './api';
import { HealthResponse } from '../types';

export class HealthService {
  /**
   * 检查API健康状态
   */
  static async checkHealth(): Promise<HealthResponse> {
    return ApiClient.get<HealthResponse>('/health');
  }

  /**
   * 检查数据库健康状态
   */
  static async checkDatabaseHealth(): Promise<HealthResponse> {
    return ApiClient.get<HealthResponse>('/health/db');
  }

  /**
   * 检查系统健康状态
   */
  static async checkSystemHealth(): Promise<HealthResponse> {
    return ApiClient.get<HealthResponse>('/health/system');
  }

  /**
   * 获取应用信息
   */
  static async getAppInfo(): Promise<{
    message: string;
    version: string;
    docs: string;
    health: string;
  }> {
    // 使用根路径获取应用信息
    return ApiClient.get('/');
  }

  /**
   * 综合健康检查
   */
  static async performHealthCheck(): Promise<{
    overall: 'healthy' | 'unhealthy' | 'degraded';
    components: {
      api: HealthResponse;
      database: HealthResponse;
      system: HealthResponse;
    };
    timestamp: string;
  }> {
    try {
      const [apiHealth, dbHealth, systemHealth] = await Promise.allSettled([
        this.checkHealth(),
        this.checkDatabaseHealth(),
        this.checkSystemHealth(),
      ]);

      const components = {
        api: apiHealth.status === 'fulfilled' 
          ? apiHealth.value 
          : { status: 'unhealthy' as const, error: (apiHealth as PromiseRejectedResult).reason },
        database: dbHealth.status === 'fulfilled' 
          ? dbHealth.value 
          : { status: 'unhealthy' as const, error: (dbHealth as PromiseRejectedResult).reason },
        system: systemHealth.status === 'fulfilled' 
          ? systemHealth.value 
          : { status: 'unhealthy' as const, error: (systemHealth as PromiseRejectedResult).reason },
      };

      // 计算整体健康状态
      const statuses = Object.values(components).map(c => c.status);
      let overall: 'healthy' | 'unhealthy' | 'degraded';

      if (statuses.every(s => s === 'healthy')) {
        overall = 'healthy';
      } else if (statuses.every(s => s === 'unhealthy')) {
        overall = 'unhealthy';
      } else {
        overall = 'degraded';
      }

      return {
        overall,
        components,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        overall: 'unhealthy',
        components: {
          api: { status: 'unhealthy', error: error },
          database: { status: 'unhealthy', error: 'Unable to check' },
          system: { status: 'unhealthy', error: 'Unable to check' },
        },
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * 心跳检查（轻量级健康检查）
   */
  static async heartbeat(): Promise<boolean> {
    try {
      await this.checkHealth();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 定期健康检查
   */
  static startPeriodicHealthCheck(
    intervalMs: number = 30000,
    onStatusChange?: (status: 'healthy' | 'unhealthy' | 'degraded') => void
  ): () => void {
    let lastStatus: 'healthy' | 'unhealthy' | 'degraded' | null = null;

    const checkHealth = async () => {
      try {
        const result = await this.performHealthCheck();
        
        if (lastStatus !== result.overall) {
          lastStatus = result.overall;
          onStatusChange?.(result.overall);
        }
      } catch (error) {
        if (lastStatus !== 'unhealthy') {
          lastStatus = 'unhealthy';
          onStatusChange?.('unhealthy');
        }
      }
    };

    // 立即执行一次
    checkHealth();

    // 设置定期检查
    const intervalId = setInterval(checkHealth, intervalMs);

    // 返回清理函数
    return () => clearInterval(intervalId);
  }
}

export default HealthService; 