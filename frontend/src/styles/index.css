@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  body.dark {
    @apply bg-dark-900 text-dark-50;
  }
  
  /* 选择文本颜色 */
  ::selection {
    @apply bg-primary-200 text-primary-800;
  }
  
  .dark ::selection {
    @apply bg-primary-800 text-primary-200;
  }
  
  /* 滚动条样式 - 浅色模式 */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* 滚动条样式 - 深色模式 */
  .dark ::-webkit-scrollbar-track {
    @apply bg-dark-800;
  }
  
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-dark-600;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-500;
  }
  
  /* 焦点样式 */
  .focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
  }
  
  .dark .focus-visible {
    @apply ring-offset-dark-900;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center font-medium text-sm leading-4 rounded-lg transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white shadow-sm hover:shadow-md focus-visible:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 hover:bg-secondary-200 active:bg-secondary-300 text-secondary-800 border border-secondary-200 hover:border-secondary-300 focus-visible:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn bg-transparent hover:bg-gray-50 active:bg-gray-100 text-gray-700 border border-gray-300 hover:border-gray-400 focus-visible:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent hover:bg-gray-100 active:bg-gray-200 text-gray-700 focus-visible:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-error-600 hover:bg-error-700 active:bg-error-800 text-white shadow-sm hover:shadow-md focus-visible:ring-error-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 hover:bg-success-700 active:bg-success-800 text-white shadow-sm hover:shadow-md focus-visible:ring-success-500;
  }
  
  /* 深色模式按钮 */
  .dark .btn-secondary {
    @apply bg-dark-800 hover:bg-dark-700 active:bg-dark-600 text-dark-200 border-dark-700 hover:border-dark-600;
  }
  
  .dark .btn-outline {
    @apply bg-transparent hover:bg-dark-800 active:bg-dark-700 text-dark-200 border-dark-600 hover:border-dark-500;
  }
  
  .dark .btn-ghost {
    @apply bg-transparent hover:bg-dark-800 active:bg-dark-700 text-dark-200;
  }
  
  /* 按钮尺寸 */
  .btn-xs {
    @apply px-2 py-1 text-xs;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-md {
    @apply px-4 py-2 text-sm;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg;
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg shadow-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
  }
  
  .input-error {
    @apply border-error-300 focus:border-error-500 focus:ring-error-500;
  }
  
  .input-success {
    @apply border-success-300 focus:border-success-500 focus:ring-success-500;
  }
  
  /* 深色模式输入框 */
  .dark .input {
    @apply bg-dark-800 border-dark-600 text-dark-200 placeholder-dark-400 focus:border-primary-500;
  }
  
  .dark .input-error {
    @apply border-error-600 focus:border-error-500;
  }
  
  .dark .input-success {
    @apply border-success-600 focus:border-success-500;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  }
  
  .card-hover {
    @apply card hover:shadow-md transition-shadow duration-200;
  }
  
  .card-interactive {
    @apply card-hover cursor-pointer hover:border-gray-300 active:scale-[0.98] transition-all duration-200;
  }
  
  /* 深色模式卡片 */
  .dark .card {
    @apply bg-dark-800 border-dark-700;
  }
  
  .dark .card-hover {
    @apply hover:border-dark-600;
  }
  
  .dark .card-interactive {
    @apply hover:border-dark-600;
  }
  
  /* 标签样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  .badge-info {
    @apply badge bg-info-100 text-info-800;
  }
  
  /* 深色模式标签 */
  .dark .badge-primary {
    @apply bg-primary-900 text-primary-200;
  }
  
  .dark .badge-secondary {
    @apply bg-secondary-900 text-secondary-200;
  }
  
  .dark .badge-success {
    @apply bg-success-900 text-success-200;
  }
  
  .dark .badge-warning {
    @apply bg-warning-900 text-warning-200;
  }
  
  .dark .badge-error {
    @apply bg-error-900 text-error-200;
  }
  
  .dark .badge-info {
    @apply bg-info-900 text-info-200;
  }
  
  /* 分隔线 */
  .divider {
    @apply border-0 border-t border-gray-200;
  }
  
  .dark .divider {
    @apply border-dark-700;
  }
  
  /* 加载状态 */
  .loading {
    @apply relative overflow-hidden;
  }
  
  .loading::before {
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white to-transparent;
    content: '';
    animation: shimmer 1.5s infinite;
  }
  
  .dark .loading::before {
    @apply via-dark-700;
  }
  
  /* 骨架屏 */
  .skeleton {
    @apply animate-pulse bg-gray-300 rounded;
  }
  
  .dark .skeleton {
    @apply bg-dark-700;
  }
  
  /* 玻璃效果 */
  .glass-effect {
    @apply backdrop-blur-md bg-white/80 border border-white/20;
  }
  
  .dark .glass-effect {
    @apply bg-dark-900/80 border-dark-700/50;
  }
  
  /* 阴影效果 */
  .shadow-glow {
    @apply shadow-lg;
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.3));
  }
  
  .dark .shadow-glow {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
  }
  
  /* 渐变文本 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .dark .text-gradient {
    @apply from-primary-400 to-secondary-400;
  }
  
  /* 动画效果 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.4s ease-out;
  }
  
  /* 过渡效果 */
  .transition-smooth {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .transition-fast {
    @apply transition-all duration-150 ease-out;
  }
  
  .transition-slow {
    @apply transition-all duration-500 ease-in-out;
  }
}

/* 自定义动画 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 辅助类 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 安全区域 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* 响应式隐藏/显示 */
  .hide-mobile {
    @apply block sm:hidden;
  }
  
  .show-mobile {
    @apply hidden sm:block;
  }
  
  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .btn {
      @apply border-2 border-current;
    }
  }
  
  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
} 