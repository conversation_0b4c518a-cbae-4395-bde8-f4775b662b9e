export interface AnimationConfig {
  duration?: number;
  delay?: number;
  easing?: string;
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
}

export interface TransitionConfig extends AnimationConfig {
  enter?: string;
  exit?: string;
  appear?: boolean;
}

export interface AnimationPreferences {
  enableAnimations: boolean;
  reduceMotion: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast';
}

export interface MicroInteractionConfig {
  hover?: AnimationConfig;
  focus?: AnimationConfig;
  active?: AnimationConfig;
  disabled?: AnimationConfig;
}

export type AnimationType = 
  | 'fade'
  | 'slide-up'
  | 'slide-down'
  | 'slide-left'
  | 'slide-right'
  | 'scale'
  | 'rotate'
  | 'bounce'
  | 'elastic'
  | 'spring';

export type AnimationDirection = 'up' | 'down' | 'left' | 'right';

export interface AnimationContextValue {
  preferences: AnimationPreferences;
  updatePreferences: (preferences: Partial<AnimationPreferences>) => void;
  getDuration: (base: number) => number;
  shouldAnimate: () => boolean;
}

export interface PageTransitionProps {
  children: React.ReactNode;
  type?: AnimationType;
  direction?: AnimationDirection;
  duration?: number;
  className?: string;
}

export interface AnimatedElementProps {
  children: React.ReactNode;
  animation: AnimationType;
  trigger?: 'visible' | 'hover' | 'focus' | 'click' | 'immediate';
  direction?: AnimationDirection;
  duration?: number;
  delay?: number;
  className?: string;
  onAnimationComplete?: () => void;
} 