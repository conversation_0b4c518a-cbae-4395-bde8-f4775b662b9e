/**
 * API服务相关的TypeScript类型定义
 * 包含HTTP客户端、请求/响应处理、错误处理等类型
 */

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// API 配置类型
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers?: Record<string, string>;
  withCredentials?: boolean;
}

// 请求配置类型
export interface RequestConfig {
  url: string;
  method: HttpMethod;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  timeout?: number;
  signal?: AbortSignal;
}

// 响应类型
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// 错误响应类型
export interface ApiErrorResponse {
  error: boolean;
  message: string;
  status_code: number;
  details?: any;
}

// 请求拦截器类型
export interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
  onRequestError?: (error: any) => any;
}

// 响应拦截器类型
export interface ResponseInterceptor {
  onResponse?: <T>(response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>;
  onResponseError?: (error: any) => any;
}

// API 客户端接口
export interface ApiClient {
  get<T>(url: string, config?: Partial<RequestConfig>): Promise<T>;
  post<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T>;
  put<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T>;
  delete<T>(url: string, config?: Partial<RequestConfig>): Promise<T>;
  patch<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T>;
  
  // 拦截器方法
  addRequestInterceptor(interceptor: RequestInterceptor): void;
  addResponseInterceptor(interceptor: ResponseInterceptor): void;
  
  // 配置方法
  setBaseURL(baseURL: string): void;
  setDefaultHeaders(headers: Record<string, string>): void;
}

// 重试配置类型
export interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: any) => boolean;
}

// 缓存配置类型
export interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number;
  key?: string;
}

// API 端点类型
export interface ApiEndpoints {
  // Auth endpoints
  auth: {
    login: string;
    logout: string;
    refresh: string;
  };
  
  // Chat endpoints
  chat: {
    message: string;
    stream: string;
    history: (sessionId: string) => string;
    documentQa: string;
    documentQaStream: string;
    processorStatus: string;
    processorCleanup: string;
  };
  
  // Sessions endpoints
  sessions: {
    list: string;
    create: string;
    get: (sessionId: string) => string;
    update: (sessionId: string) => string;
    delete: (sessionId: string) => string;
    summary: (sessionId: string) => string;
  };
  
  // Documents endpoints
  documents: {
    upload: string;
    list: string;
    get: (documentId: string) => string;
    update: (documentId: string) => string;
    delete: (documentId: string) => string;
    status: (documentId: string) => string;
    statistics: string;
    cleanup: string;
    healthCheck: string;
  };
  
  // Health endpoints
  health: {
    check: string;
    detailed: string;
  };
}

// 服务基类接口
export interface BaseService {
  apiClient: ApiClient;
  endpoints: Partial<ApiEndpoints>;
}

// 文件上传进度回调类型
export type UploadProgressCallback = (progressEvent: {
  loaded: number;
  total: number;
  percentage: number;
}) => void;

// 流式响应处理器类型
export type StreamResponseHandler = (chunk: string) => void;
export type StreamCompleteHandler = () => void;
export type StreamErrorHandler = (error: any) => void;

// 流式响应配置
export interface StreamConfig {
  onChunk: StreamResponseHandler;
  onComplete?: StreamCompleteHandler;
  onError?: StreamErrorHandler;
  signal?: AbortSignal;
}

// WebSocket 消息类型
export interface WebSocketMessage<T = any> {
  type: string;
  data: T;
  timestamp: number;
}

// WebSocket 配置类型
export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onOpen?: () => void;
  onMessage?: (message: WebSocketMessage) => void;
  onError?: (error: Event) => void;
  onClose?: (event: CloseEvent) => void;
}

// API 状态管理类型
export interface ApiState {
  isOnline: boolean;
  isLoading: boolean;
  pendingRequests: number;
  lastError: ApiErrorResponse | null;
  retryCount: number;
}

// 批量操作类型
export interface BatchOperation<T> {
  id: string;
  operation: () => Promise<T>;
  retries?: number;
  priority?: number;
}

export interface BatchConfig {
  maxConcurrent: number;
  delayBetweenBatches: number;
  onProgress?: (completed: number, total: number) => void;
  onComplete?: (results: any[]) => void;
  onError?: (error: any, operation: BatchOperation<any>) => void;
} 