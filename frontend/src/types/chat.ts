/**
 * 聊天相关的TypeScript类型定义
 * 对应后端的Pydantic Schema
 */

export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system'
}

// Chat Session Types
export interface ChatSessionBase {
  title?: string;
  user_id?: string;
}

export interface ChatSessionCreate extends ChatSessionBase {}

export interface ChatSessionUpdate {
  title?: string;
}

export interface ChatSessionResponse extends ChatSessionBase {
  id: string;
  created_at?: string;
  updated_at?: string;
  messages_count?: number;
}

// Chat Message Types
export interface ChatMessageBase {
  role: MessageRole;
  content: string;
  sources?: Record<string, any>;
}

export interface ChatMessageCreate extends ChatMessageBase {
  session_id: string;
}

export interface ChatMessageResponse extends ChatMessageBase {
  id: string;
  session_id: string;
  created_at?: string;
  content_preview?: string;
}

// Combined Types
export interface ChatHistoryResponse {
  session: ChatSessionResponse;
  messages: ChatMessageResponse[];
}

export interface ChatSessionWithMessages extends ChatSessionResponse {
  messages: ChatMessageResponse[];
}

// Chat Request/Response Types
export interface ChatRequest {
  message: string;
  session_id?: string;
  stream?: boolean;
}

export interface ChatResponse {
  message: string;
  session_id: string;
  message_id: string;
  sources?: Array<Record<string, any>>;
  created_at: string;
}

export interface StreamChatResponse {
  chunk: string;
  session_id: string;
  message_id?: string;
  is_complete: boolean;
  sources?: Array<Record<string, any>>;
}

// Session List Type (Updated to match backend)
export interface ChatSessionListResponse {
  sessions: ChatSessionResponse[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean; // 新增字段
}

// Error Types
export interface ApiError {
  error: boolean;
  message: string;
  status_code: number;
  details?: any;
}

// Chat Component Props Types
export interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

export interface ChatMessageProps {
  message: ChatMessageResponse;
  isStreaming?: boolean;
  onRetry?: () => void;
}

export interface ChatSessionItemProps {
  session: ChatSessionResponse;
  isActive?: boolean;
  onClick?: (sessionId: string) => void;
  onDelete?: (sessionId: string) => void;
  onRename?: (sessionId: string, newTitle: string) => void;
}

export interface ChatHistoryProps {
  messages: ChatMessageResponse[];
  isLoading?: boolean;
  onLoadMore?: (() => Promise<void>) | (() => void) | undefined;
  hasMore?: boolean;
}

// Chat State Management Types
export interface ChatState {
  currentSession: ChatSessionResponse | null;
  sessions: ChatSessionResponse[];
  messages: ChatMessageResponse[];
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  sessionsListPagination: {
    page: number;
    page_size: number;
    total: number;
    has_next: boolean;
  };
}

export interface ChatActions {
  sendMessage: (message: string, sessionId?: string) => Promise<void>;
  createSession: (title?: string) => Promise<ChatSessionResponse>;
  deleteSession: (sessionId: string) => Promise<void>;
  loadSessions: (page?: number) => Promise<void>;
  loadChatHistory: (sessionId: string) => Promise<void>;
  setCurrentSession: (session: ChatSessionResponse | null) => void;
  clearError: () => void;
}

// Stream Event Types
export interface StreamEvent {
  type: 'chunk' | 'complete' | 'error';
  data: StreamChatResponse | string;
}

// Source Reference Types for AI responses
export interface SourceReference {
  content: string;
  similarity: number;
  document_id?: string;
  document_name?: string;
  chunk_id?: string;
  page?: number;
  section?: string;
}

export interface SourceReferencesProps {
  sources: SourceReference[];
  className?: string;
  maxInitialVisible?: number;
  showSimilarityScores?: boolean;
  onSourceClick?: ((source: SourceReference) => void) | undefined;
}

export interface SourceReferenceItemProps {
  source: SourceReference;
  index: number;
  isExpanded?: boolean;
  onToggle?: () => void;
  onSourceClick?: ((source: SourceReference) => void) | undefined;
  showSimilarityScore?: boolean;
} 