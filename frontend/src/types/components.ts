/**
 * 组件Props相关的TypeScript类型定义
 * 包含各种组件的Props类型、事件处理器等
 */

import type { 
  ChatMessageResponse, 
  DocumentResponse,
  BaseComponentProps, 
  ActionProps,
  ChangeHandler 
} from './';

// Layout Components
export interface LayoutProps extends BaseComponentProps {
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  main: React.ReactNode;
}

export interface HeaderProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  showBreadcrumb?: boolean;
  onMenuToggle?: () => void;
}

export interface SidebarProps extends BaseComponentProps {
  isOpen: boolean;
  width?: number;
  onClose?: () => void;
  overlay?: boolean;
  children: React.ReactNode;
}

// Button Components
export interface ButtonProps extends BaseComponentProps, ActionProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  type?: 'button' | 'submit' | 'reset';
}

export interface IconButtonProps extends BaseComponentProps, ActionProps {
  icon: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'ghost';
  tooltip?: string;
}

// Input Components
export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  onChange?: ChangeHandler<string>;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  error?: string;
  label?: string;
  helperText?: string;
  maxLength?: number;
  minLength?: number;
  autoFocus?: boolean;
  autoComplete?: string;
  size?: 'sm' | 'md' | 'lg';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

export interface TextareaProps extends Omit<InputProps, 'type' | 'startIcon' | 'endIcon'> {
  rows?: number;
  cols?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
}

// Select Components
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

export interface SelectProps extends BaseComponentProps {
  options: SelectOption[];
  value?: string | number;
  defaultValue?: string | number;
  onChange?: ChangeHandler<string | number>;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
  helperText?: string;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
}

// Modal Components
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closable?: boolean;
  maskClosable?: boolean;
  centered?: boolean;
  footer?: React.ReactNode;
  children: React.ReactNode;
}

export interface DialogProps extends ModalProps {
  type?: 'info' | 'success' | 'warning' | 'error' | 'confirm';
  message?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

// Table Components
export interface TableHeaderProps {
  title: string;
  sortable?: boolean;
  sorted?: 'asc' | 'desc' | null;
  onSort?: (direction: 'asc' | 'desc') => void;
  align?: 'left' | 'center' | 'right';
  width?: string | number;
}

export interface TableRowProps {
  data: Record<string, any>;
  columns: TableHeaderProps[];
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
  onClick?: () => void;
  className?: string;
}

export interface TablePaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
}

// Form Components
export interface FormProps extends BaseComponentProps {
  onSubmit: (data: Record<string, any>) => void | Promise<void>;
  initialValues?: Record<string, any>;
  validationSchema?: any;
  loading?: boolean;
  children: React.ReactNode;
}

export interface FormFieldProps extends BaseComponentProps {
  name: string;
  label?: string;
  required?: boolean;
  error?: string;
  helperText?: string;
  children: React.ReactNode;
}

// Navigation Components
export interface NavItemProps extends Omit<BaseComponentProps, 'children'> {
  to: string;
  label: string;
  icon?: React.ReactNode;
  badge?: string | number;
  active?: boolean;
  disabled?: boolean;
  exact?: boolean;
  children?: NavItemProps[];
}

export interface BreadcrumbProps extends BaseComponentProps {
  items: Array<{
    label: string;
    path?: string;
    icon?: React.ReactNode;
  }>;
  separator?: React.ReactNode;
  maxItems?: number;
}

// Loading Components
export interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  text?: string;
}

export interface SkeletonProps extends BaseComponentProps {
  width?: string | number;
  height?: string | number;
  variant?: 'text' | 'rect' | 'circle';
  animation?: 'pulse' | 'wave' | false;
}

// Notification Components
export interface ToastProps extends BaseComponentProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  closable?: boolean;
  onClose?: () => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface AlertProps extends BaseComponentProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  closable?: boolean;
  onClose?: () => void;
  showIcon?: boolean;
  banner?: boolean;
}

// Utility Components
export interface CopyButtonProps extends BaseComponentProps {
  text: string;
  tooltip?: string;
  onCopy?: () => void;
  children?: React.ReactNode;
}

export interface SearchBoxProps extends BaseComponentProps {
  value?: string;
  onSearch: (query: string) => void;
  onChange?: ChangeHandler<string>;
  placeholder?: string;
  loading?: boolean;
  disabled?: boolean;
  autoFocus?: boolean;
  showClearButton?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export interface PaginationProps extends BaseComponentProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number, pageSize?: number) => void;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: ((total: number, range: [number, number]) => React.ReactNode) | boolean;
  simple?: boolean;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
}

// Chat Specific Components (Extended from chat.ts)
export interface ChatContainerProps extends BaseComponentProps {
  sessionId?: string;
  height?: string | number;
  showHeader?: boolean;
  showSidebar?: boolean;
  onSessionChange?: (sessionId: string) => void;
}

export interface MessageBubbleProps extends BaseComponentProps {
  message: ChatMessageResponse;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  isStreaming?: boolean;
  onRetry?: () => void;
  onCopy?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export interface ChatInputBoxProps extends BaseComponentProps {
  value?: string;
  onChange?: ChangeHandler<string>;
  onSend: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  maxLength?: number;
  showFileUpload?: boolean;
  showEmojiPicker?: boolean;
  autoFocus?: boolean;
}

// Document Specific Components (Extended from document.ts)
export interface DocumentCardProps extends BaseComponentProps {
  document: DocumentResponse;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
  onView?: () => void;
  onDownload?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

export interface FileDropzoneProps extends BaseComponentProps {
  onDrop: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  disabled?: boolean;
  loading?: boolean;
  height?: string | number;
  showPreview?: boolean;
  children?: React.ReactNode;
}

export interface UploadProgressProps extends BaseComponentProps {
  progress: number;
  fileName: string;
  fileSize?: number;
  status: 'uploading' | 'success' | 'error' | 'paused';
  error?: string;
  onCancel?: () => void;
  onRetry?: () => void;
  onPause?: () => void;
  onResume?: () => void;
} 