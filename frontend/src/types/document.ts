/**
 * 文档相关的TypeScript类型定义
 * 对应后端的Pydantic Schema
 */

export enum ProcessingStatusEnum {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Document Base Types
export interface DocumentBase {
  filename: string;
  file_type: string;
  file_size: number;
}

export interface DocumentCreate extends DocumentBase {}

export interface DocumentUpdate {
  filename?: string;
  processing_status?: ProcessingStatusEnum;
  chunks_count?: number;
  error_message?: string;
}

export interface DocumentResponse extends DocumentBase {
  id: string;
  upload_time?: string;
  processing_status?: string;
  chunks_count?: number;
  error_message?: string;
  file_size_mb?: number;
  is_processed?: boolean;
  is_processing?: boolean;
  has_error?: boolean;
}

export interface DocumentListResponse {
  documents: DocumentResponse[];
  total: number;
  page: number;
  page_size: number;
}

// Document Upload Types
export interface DocumentUploadResponse {
  document_id: string;
  filename: string;
  file_size: number;
  file_size_mb: number;
  upload_time: string;
  processing_status: ProcessingStatusEnum;
  message: string;
}

export interface DocumentProcessingStatus {
  document_id: string;
  filename: string;
  processing_status: ProcessingStatusEnum;
  progress?: number;
  chunks_count?: number;
  error_message?: string;
  estimated_time_remaining?: number;
}

// Document Search Types
export interface DocumentSearchRequest {
  query: string;
  document_ids?: string[];
  limit?: number;
}

export interface DocumentSearchResult {
  document_id: string;
  filename: string;
  chunk_content: string;
  relevance_score: number;
  page_number?: number;
}

export interface DocumentSearchResponse {
  query: string;
  results: DocumentSearchResult[];
  total_results: number;
  search_time_ms: number;
}

// File Upload Types (for frontend use)
export interface FileUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  document_id?: string;
}

// Document Component Props Types
export interface DocumentUploadProps {
  onUploadSuccess?: (document: DocumentUploadResponse) => void;
  onUploadError?: (error: string) => void;
  onUploadProgress?: (progress: number) => void;
  accept?: string;
  maxSize?: number; // in MB
  multiple?: boolean;
}

export interface DocumentListItemProps {
  document: DocumentResponse;
  onDownload?: (documentId: string) => void;
  onDelete?: (documentId: string) => void;
  onView?: (documentId: string) => void;
  showActions?: boolean;
}

export interface DocumentFilterProps {
  onFilterChange: (filters: DocumentListFilters) => void;
  currentFilters: DocumentListFilters;
  statusOptions?: ProcessingStatusEnum[];
  fileTypeOptions?: string[];
}

export interface DocumentSearchProps {
  onSearch: (query: string, filters?: DocumentSearchFilters) => void;
  isLoading?: boolean;
  placeholder?: string;
  showAdvanced?: boolean;
}

export interface DocumentProcessingIndicatorProps {
  status: ProcessingStatusEnum;
  progress?: number;
  error?: string;
  estimatedTime?: number;
  showText?: boolean;
}

// Document State Management Types
export interface DocumentState {
  documents: DocumentResponse[];
  isLoading: boolean;
  isUploading: boolean;
  error: string | null;
  uploadProgress: Record<string, FileUploadProgress>;
  searchResults: DocumentSearchResult[];
  filters: DocumentListFilters;
  pagination: {
    page: number;
    page_size: number;
    total: number;
    has_next: boolean;
  };
}

export interface DocumentActions {
  uploadDocument: (file: File) => Promise<DocumentUploadResponse>;
  loadDocuments: (filters?: DocumentListFilters, page?: number) => Promise<void>;
  deleteDocument: (documentId: string, hardDelete?: boolean) => Promise<void>;
  searchDocuments: (query: string, filters?: DocumentSearchFilters) => Promise<void>;
  getDocumentStatus: (documentId: string) => Promise<DocumentProcessingStatus>;
  updateDocument: (documentId: string, updates: DocumentUpdate) => Promise<void>;
  clearError: () => void;
  setFilters: (filters: Partial<DocumentListFilters>) => void;
}

// Filter and Search Types
export interface DocumentListFilters {
  status?: ProcessingStatusEnum[];
  file_type?: string[];
  search?: string;
  sort_by?: 'upload_time' | 'filename' | 'file_size';
  sort_order?: 'asc' | 'desc';
  page?: number;
  page_size?: number;
}

export interface DocumentSearchFilters {
  document_ids?: string[];
  limit?: number;
}

// Statistics Types
export interface DocumentStatistics {
  total_documents: number;
  processed_documents: number;
  failed_documents: number;
  processing_documents: number;
  total_size_mb: number;
  avg_processing_time_seconds: number;
  most_common_file_types: Array<{
    type: string;
    count: number;
  }>;
}

// Drag and Drop Types
export interface DragDropFile {
  file: File;
  id: string;
  preview?: string;
  isValid: boolean;
  errors: string[];
}

export interface DragDropState {
  isDragOver: boolean;
  files: DragDropFile[];
  invalidFiles: DragDropFile[];
}

// File Validation Types
export interface FileValidationRule {
  maxSize?: number; // in MB
  allowedTypes?: string[];
  maxFiles?: number;
  requireNonEmpty?: boolean;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
} 