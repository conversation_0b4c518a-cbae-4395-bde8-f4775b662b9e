/**
 * 统一导出所有类型定义
 */

// Chat types
export * from './chat';

// Document types
export * from './document';

// API types
export * from './api';

// Store types
export * from './store';

// Component types
export * from './components';

// Animation types
export * from './animation';

// Common API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  status?: number;
  success?: boolean;
}

export interface ApiError {
  error: boolean;
  message: string;
  status_code: number;
  details?: any;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  page_size?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationResponse {
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
  has_prev: boolean;
}

// Health Check Types
export interface HealthResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  service?: string;
  version?: string;
  database?: string;
  schema_valid?: boolean;
  component?: string;
  error?: any;
}

// Loading and State Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated?: string;
}

export interface AsyncOperationState<T = any> extends LoadingState {
  data: T | null;
  isSuccess: boolean;
  isFetching: boolean;
}

// API State Management Types (Added to fix linter error)
export interface ApiState {
  isOnline: boolean;
  isLoading: boolean;
  pendingRequests: number;
  lastError: ApiError | null;
  retryCount: number;
}

// Form Types
export interface FormFieldError {
  field: string;
  message: string;
}

export interface FormState<T = Record<string, any>> {
  values: T;
  errors: FormFieldError[];
  isSubmitting: boolean;
  isDirty: boolean;
  isValid: boolean;
}

// Modal and Dialog Types
export interface ModalState {
  isOpen: boolean;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closable?: boolean;
}

export interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  variant?: 'info' | 'warning' | 'danger';
}

// Notification Types
export interface NotificationItem {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

export interface NotificationState {
  notifications: NotificationItem[];
}

// Theme and UI Types
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  fontSize: 'sm' | 'md' | 'lg';
  animations: boolean;
}

export interface UIState {
  theme: ThemeConfig;
  sidebar: {
    isOpen: boolean;
    width: number;
  };
  header: {
    isVisible: boolean;
    height: number;
  };
}

// Navigation Types
export interface NavItem {
  id: string;
  label: string;
  path: string;
  icon?: string;
  badge?: string | number;
  children?: NavItem[];
  disabled?: boolean;
}

export interface BreadcrumbItem {
  label: string;
  path?: string;
  isActive?: boolean;
}

// Search and Filter Types
export interface SearchState {
  query: string;
  filters: Record<string, any>;
  results: any[];
  isSearching: boolean;
  totalResults: number;
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// File and Upload Types
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface UploadState {
  progress: number;
  status: 'idle' | 'uploading' | 'success' | 'error';
  error?: string;
}

// Utility Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type Required<T, K extends keyof T> = T & { [P in K]-?: T[P] };
export type Nullable<T> = T | null;
export type ValueOf<T> = T[keyof T];
export type KeysOfType<T, U> = { [K in keyof T]: T[K] extends U ? K : never }[keyof T];

// Event Handler Types
export type EventHandler<T = Event> = (event: T) => void;
export type ChangeHandler<T = any> = (value: T) => void;
export type ClickHandler = EventHandler<React.MouseEvent>;
export type SubmitHandler<T = any> = (data: T) => void | Promise<void>;

// Component Common Props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  testId?: string;
}

export interface ActionProps {
  loading?: boolean;
  disabled?: boolean;
  onClick?: ClickHandler;
}

// Data Table Types
export interface TableColumn<T = any> {
  key: keyof T | string;
  title: string;
  width?: number | string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  pagination?: PaginationResponse;
  onPageChange?: (page: number) => void;
  onSort?: (field: string, direction: 'asc' | 'desc') => void;
  rowKey?: keyof T | ((record: T) => string);
  emptyText?: string;
}

// WebSocket and Real-time Types
export interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: any;
  connectionUrl: string;
}

export interface StreamingState {
  isStreaming: boolean;
  chunks: string[];
  error: string | null;
  isComplete: boolean;
} 