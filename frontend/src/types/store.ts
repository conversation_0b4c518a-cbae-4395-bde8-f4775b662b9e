/**
 * 状态管理相关的TypeScript类型定义
 * 包含全局状态、Actions、Reducers等类型
 */

import type { 
  ChatState, 
  ChatActions, 
  DocumentState, 
  DocumentActions,
  NotificationState,
  UIState,
  LoadingState,
  ApiState 
} from './';

// 全局应用状态类型
export interface AppState {
  // 功能模块状态
  chat: ChatState;
  documents: DocumentState;
  
  // UI 状态
  ui: UIState;
  notifications: NotificationState;
  
  // 系统状态
  api: ApiState;
  auth: AuthState;
  settings: SettingsState;
}

// 认证状态类型
export interface AuthState extends LoadingState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  permissions: string[];
  sessionExpiry: string | null;
}

// 用户信息类型
export interface User {
  id: string;
  username: string;
  email?: string;
  displayName?: string;
  avatar?: string;
  role: UserRole;
  preferences: UserPreferences;
  createdAt: string;
  lastLoginAt?: string;
}

// 用户角色类型
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// 用户偏好设置类型
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    desktop: boolean;
  };
  chat: {
    autoSave: boolean;
    soundEnabled: boolean;
    showTypingIndicator: boolean;
  };
  documents: {
    defaultView: 'list' | 'grid';
    autoProcessing: boolean;
    keepOriginals: boolean;
  };
}

// 应用设置状态类型
export interface SettingsState extends LoadingState {
  config: AppConfig;
  features: FeatureFlags;
  limits: SystemLimits;
}

// 应用配置类型
export interface AppConfig {
  apiUrl: string;
  wsUrl: string;
  appName: string;
  version: string;
  environment: 'development' | 'staging' | 'production';
  debug: boolean;
  analytics: {
    enabled: boolean;
    provider?: string;
    trackingId?: string;
  };
}

// 功能开关类型
export interface FeatureFlags {
  enableStreaming: boolean;
  enableDocumentUpload: boolean;
  enableAdvancedSearch: boolean;
  enableNotifications: boolean;
  enableWebSocket: boolean;
  enableVoiceInput: boolean;
  enableFilePreview: boolean;
  maxChatSessions: number;
  maxDocumentSize: number; // in MB
}

// 系统限制类型
export interface SystemLimits {
  maxMessageLength: number;
  maxSessionsPerUser: number;
  maxDocumentsPerUser: number;
  maxFileUploadSize: number; // in MB
  rateLimits: {
    messages: { count: number; window: number }; // count per window (seconds)
    uploads: { count: number; window: number };
    apiCalls: { count: number; window: number };
  };
}

// Action 类型联合
export type AppActions = ChatActions & DocumentActions & UIActions & AuthActions & SettingsActions;

// UI Actions 类型
export interface UIActions {
  toggleSidebar: () => void;
  setSidebarWidth: (width: number) => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  showNotification: (notification: Omit<import('./').NotificationItem, 'id'>) => void;
  hideNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

// Auth Actions 类型
export interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

// Settings Actions 类型
export interface SettingsActions {
  loadSettings: () => Promise<void>;
  updateConfig: (config: Partial<AppConfig>) => Promise<void>;
  updateFeatureFlags: (flags: Partial<FeatureFlags>) => Promise<void>;
  resetSettings: () => Promise<void>;
}

// 登录凭据类型
export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// Store 配置类型
export interface StoreConfig {
  persist?: {
    key: string;
    storage: Storage;
    whitelist?: (keyof AppState)[];
    blacklist?: (keyof AppState)[];
  };
  middleware?: {
    logger?: boolean;
    devTools?: boolean;
    thunk?: boolean;
  };
  initialState?: Partial<AppState>;
}

// Action Creator 类型
export interface ActionCreator<T = any> {
  type: string;
  payload?: T;
  meta?: any;
  error?: boolean;
}

// Async Action Creator 类型
export interface AsyncActionCreator<T = any> {
  pending: ActionCreator;
  fulfilled: ActionCreator<T>;
  rejected: ActionCreator<Error>;
}

// Reducer 类型
export type Reducer<S = any, A = ActionCreator> = (state: S, action: A) => S;

// Epic/Saga 类型（用于处理副作用）
export type Epic<State = AppState, Action = ActionCreator> = (
  action$: Observable<Action>,
  state$: Observable<State>
) => Observable<Action>;

// Selector 类型
export type Selector<State = AppState, Result = any> = (state: State) => Result;

// Computed Selector 类型
export type ComputedSelector<State = AppState, Result = any, Params extends readonly any[] = any[]> = (
  ...args: Params
) => Selector<State, Result>;

// Store 实例类型
export interface Store<State = AppState> {
  getState(): State;
  dispatch<A extends ActionCreator>(action: A): A;
  subscribe(listener: () => void): () => void;
  replaceReducer(nextReducer: Reducer<State>): void;
}

// Provider Props 类型
export interface StoreProviderProps {
  store: Store;
  children: React.ReactNode;
}

// Hook 返回类型
export interface UseStoreHook<State = AppState> {
  state: State;
  dispatch: Store['dispatch'];
}

export interface UseSelectorHook {
  <Selected>(selector: Selector<AppState, Selected>): Selected;
}

export interface UseActionsHook {
  <Actions>(actions: Actions): Actions;
}

// Store 中间件类型
export interface Middleware<State = AppState> {
  (store: Store<State>): (next: (action: ActionCreator) => any) => (action: ActionCreator) => any;
}

// DevTools 配置类型
export interface DevToolsConfig {
  name?: string;
  maxAge?: number;
  trace?: boolean;
  traceLimit?: number;
  actionSanitizer?: (action: ActionCreator) => ActionCreator;
  stateSanitizer?: (state: AppState) => AppState;
}

// Observable 类型（用于响应式编程）
export interface Observable<T> {
  subscribe(observer: {
    next?: (value: T) => void;
    error?: (error: any) => void;
    complete?: () => void;
  }): { unsubscribe(): void };
} 