/**
 * API服务使用示例
 * 展示如何使用各种API服务
 */

import { 
  ChatService, 
  DocumentService, 
  HealthService
} from '../services';
import { ChatRequest } from '../types';

/**
 * 聊天服务使用示例
 */
export class ChatExample {
  // 发送简单消息
  static async sendSimpleMessage() {
    try {
      const request: ChatRequest = {
        message: "你好，请介绍一下这个AI知识系统",
        stream: false
      };

      const response = await ChatService.sendMessage(request);
      console.log('聊天响应:', response);
      return response;
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  // 流式聊天示例
  static async streamChat() {
    try {
      const request: ChatRequest = {
        message: "请详细介绍React的核心概念",
        stream: true
      };

      await ChatService.streamMessage(
        request,
        (chunk) => {
          console.log('收到消息块:', chunk);
          // 在实际应用中，这里会更新UI
        },
        (error) => {
          console.error('流式响应错误:', error);
        },
        () => {
          console.log('流式响应完成');
        }
      );
    } catch (error) {
      console.error('流式聊天失败:', error);
      throw error;
    }
  }

  // 会话管理示例
  static async sessionManagement() {
    try {
      // 创建新会话
      const newSession = await ChatService.createSession({
        title: "React学习讨论",
        user_id: "user_123"
      });
      console.log('新会话创建成功:', newSession);

      // 获取会话列表
      const sessionList = await ChatService.getSessions({
        page: 1,
        page_size: 10
      });
      console.log('会话列表:', sessionList);

      // 获取会话历史
      if (newSession.id) {
        const history = await ChatService.getChatHistory(newSession.id);
        console.log('会话历史:', history);
      }

      return newSession;
    } catch (error) {
      console.error('会话管理失败:', error);
      throw error;
    }
  }
}

/**
 * 文档服务使用示例
 */
export class DocumentExample {
  // 文档上传示例
  static async uploadDocument(file: File) {
    try {
      // 验证文件类型和大小
      if (!DocumentService.isSupportedFileType(file)) {
        throw new Error('不支持的文件类型');
      }

      if (!DocumentService.validateFileSize(file, 10)) {
        throw new Error('文件大小超过10MB限制');
      }

      // 上传文档
      const response = await DocumentService.uploadDocument(
        file,
        (progress) => {
          console.log(`上传进度: ${progress}%`);
        }
      );

      console.log('文档上传成功:', response);

      // 轮询处理状态
      const finalStatus = await DocumentService.pollDocumentStatus(
        response.document_id,
        (status) => {
          console.log('处理状态更新:', status);
        }
      );

      console.log('文档处理完成:', finalStatus);
      return response;
    } catch (error) {
      console.error('文档上传失败:', error);
      throw error;
    }
  }

  // 文档搜索示例
  static async searchDocuments() {
    try {
      const searchRequest = {
        query: "React hooks使用方法",
        limit: 5
      };

      const searchResults = await DocumentService.searchDocuments(searchRequest);
      console.log('搜索结果:', searchResults);
      return searchResults;
    } catch (error) {
      console.error('文档搜索失败:', error);
      throw error;
    }
  }

  // 文档管理示例
  static async documentManagement() {
    try {
      // 获取文档列表
      const documentList = await DocumentService.getDocuments({
        page: 1,
        page_size: 10,
        status: 'completed'
      });
      console.log('文档列表:', documentList);

      // 获取统计信息
      const stats = await DocumentService.getDocumentStats();
      console.log('文档统计:', stats);

      return { documentList, stats };
    } catch (error) {
      console.error('文档管理失败:', error);
      throw error;
    }
  }
}

/**
 * 健康检查示例
 */
export class HealthExample {
  // 基础健康检查
  static async basicHealthCheck() {
    try {
      const health = await HealthService.checkHealth();
      console.log('系统健康状态:', health);
      return health;
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }

  // 综合健康检查
  static async comprehensiveHealthCheck() {
    try {
      const healthReport = await HealthService.performHealthCheck();
      console.log('综合健康报告:', healthReport);
      return healthReport;
    } catch (error) {
      console.error('综合健康检查失败:', error);
      throw error;
    }
  }

  // 启动定期健康监控
  static startHealthMonitoring() {
    console.log('启动健康监控...');
    
    const stopMonitoring = HealthService.startPeriodicHealthCheck(
      30000, // 30秒间隔
      (status) => {
        console.log(`系统状态变更: ${status}`);
        // 在实际应用中，这里会更新UI状态指示器
      }
    );

    // 返回停止函数，以便在组件卸载时清理
    return stopMonitoring;
  }
}

/**
 * 错误处理示例
 */
export class ErrorHandlingExample {
  static async handleApiErrors() {
    try {
      // 故意调用一个不存在的端点来演示错误处理
      await ChatService.getSession('non-existent-id');
    } catch (error: any) {
      console.log('API错误处理示例:');
      console.log('- 错误类型:', error.error ? 'API错误' : '其他错误');
      console.log('- 错误消息:', error.message);
      console.log('- 状态码:', error.status_code);
      console.log('- 详细信息:', error.details);
      
      // 根据错误类型进行不同处理
      if (error.status_code === 404) {
        console.log('处理404错误: 会话不存在');
      } else if (error.status_code === 0) {
        console.log('处理网络错误: 检查网络连接');
      } else {
        console.log('处理其他错误');
      }
    }
  }
}

// 使用示例的入口函数
export async function runApiExamples() {
  console.log('🚀 开始API服务示例演示...');

  try {
    // 1. 健康检查
    console.log('\n--- 健康检查示例 ---');
    await HealthExample.basicHealthCheck();

    // 2. 聊天功能
    console.log('\n--- 聊天功能示例 ---');
    await ChatExample.sendSimpleMessage();

    // 3. 会话管理
    console.log('\n--- 会话管理示例 ---');
    await ChatExample.sessionManagement();

    // 4. 文档管理
    console.log('\n--- 文档管理示例 ---');
    await DocumentExample.documentManagement();

    // 5. 错误处理
    console.log('\n--- 错误处理示例 ---');
    await ErrorHandlingExample.handleApiErrors();

    console.log('\n✅ API服务示例演示完成!');
  } catch (error) {
    console.error('\n❌ 示例演示过程中发生错误:', error);
  }
} 