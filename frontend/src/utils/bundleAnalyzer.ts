/**
 * Bundle 分析工具
 * 用于分析和优化代码分割效果
 */

interface BundleStats {
  totalSize: number;
  chunkCount: number;
  initialSize: number;
  asyncSize: number;
  chunks: Array<{
    name: string;
    size: number;
    type: 'initial' | 'async';
  }>;
}

// 模拟bundle统计信息（实际应用中可以通过webpack/vite分析工具获取）
export const getBundleStats = (): BundleStats => {
  // 在开发环境下返回模拟数据
  return {
    totalSize: 2048, // KB
    chunkCount: 8,
    initialSize: 512, // KB
    asyncSize: 1536, // KB
    chunks: [
      { name: 'vendor', size: 256, type: 'initial' },
      { name: 'main', size: 128, type: 'initial' },
      { name: 'router', size: 64, type: 'initial' },
      { name: 'pages', size: 320, type: 'async' },
      { name: 'chat', size: 256, type: 'async' },
      { name: 'document', size: 192, type: 'async' },
      { name: 'virtualization', size: 128, type: 'async' },
      { name: 'utils', size: 96, type: 'async' },
    ],
  };
};

/**
 * 计算代码分割优化效果
 */
export const calculateOptimization = (stats: BundleStats) => {
  const { initialSize, asyncSize, totalSize } = stats;
  
  const optimizationRatio = (asyncSize / totalSize) * 100;
  const initialLoadReduction = Math.max(0, (totalSize - initialSize) / totalSize * 100);
  
  return {
    optimizationRatio: Math.round(optimizationRatio),
    initialLoadReduction: Math.round(initialLoadReduction),
    asyncChunks: stats.chunks.filter(chunk => chunk.type === 'async').length,
    initialChunks: stats.chunks.filter(chunk => chunk.type === 'initial').length,
  };
};

/**
 * 格式化文件大小
 */
export const formatSize = (sizeInKB: number): string => {
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`;
  }
  return `${(sizeInKB / 1024).toFixed(1)} MB`;
};

/**
 * 获取代码分割建议
 */
export const getOptimizationSuggestions = (stats: BundleStats): string[] => {
  const suggestions: string[] = [];
  const optimization = calculateOptimization(stats);
  
  if (optimization.optimizationRatio < 60) {
    suggestions.push('建议增加更多异步组件来提高代码分割比例');
  }
  
  if (stats.initialSize > 300) {
    suggestions.push('初始包体积较大，考虑将更多组件设为懒加载');
  }
  
  if (stats.chunkCount < 5) {
    suggestions.push('可以创建更多细分的chunk来优化加载性能');
  }
  
  const largestChunk = stats.chunks.reduce((max, chunk) => 
    chunk.size > max.size ? chunk : max
  );
  
  if (largestChunk.size > 400) {
    suggestions.push(`${largestChunk.name} chunk过大，建议进一步分割`);
  }
  
  return suggestions;
};

export default {
  getBundleStats,
  calculateOptimization,
  formatSize,
  getOptimizationSuggestions,
};