import React from 'react';

/**
 * 预加载工具 - 用于提前加载组件和资源
 */

// 预加载的组件映射
const componentPreloadMap = new Map<string, () => Promise<any>>();

/**
 * 注册可预加载的组件
 */
export const registerPreloadComponent = (name: string, loader: () => Promise<any>) => {
  componentPreloadMap.set(name, loader);
};

/**
 * 预加载指定组件
 */
export const preloadComponent = async (name: string): Promise<void> => {
  const loader = componentPreloadMap.get(name);
  if (loader) {
    try {
      await loader();
      console.log(`组件 ${name} 预加载完成`);
    } catch (error) {
      console.warn(`组件 ${name} 预加载失败:`, error);
    }
  }
};

/**
 * 批量预加载组件
 */
export const preloadComponents = async (names: string[]): Promise<void> => {
  const preloadPromises = names.map(name => preloadComponent(name));
  await Promise.allSettled(preloadPromises);
};

/**
 * 预加载延迟器 - 在空闲时间预加载
 */
export const preloadOnIdle = (name: string, delay: number = 2000) => {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      setTimeout(() => preloadComponent(name), delay);
    });
  } else {
    // 回退到setTimeout
    setTimeout(() => preloadComponent(name), delay);
  }
};

/**
 * 鼠标悬停预加载
 */
export const preloadOnHover = (element: HTMLElement | null, componentName: string) => {
  if (!element) return;

  const handleMouseEnter = () => {
    preloadComponent(componentName);
    element.removeEventListener('mouseenter', handleMouseEnter);
  };

  element.addEventListener('mouseenter', handleMouseEnter);
};

/**
 * 创建懒加载组件的高阶函数
 */
export const createLazyComponent = <T extends React.ComponentType<any>>(
  name: string,
  loader: () => Promise<{ default: T }>
) => {
  // 注册到预加载映射
  registerPreloadComponent(name, loader);
  
  // 返回React.lazy包装的组件
  return React.lazy(loader);
};

export default {
  registerPreloadComponent,
  preloadComponent,
  preloadComponents,
  preloadOnIdle,
  preloadOnHover,
  createLazyComponent
};