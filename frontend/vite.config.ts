import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    // 代码分割优化
    rollupOptions: {
      output: {
        // 手动代码分割
        manualChunks: {
          // 第三方库单独分割
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          query: ['@tanstack/react-query'],
          ui: ['lucide-react', 'react-dropzone'],
          // 页面组件分割
          pages: [
            'src/components/HomePage.tsx',
            'src/components/ChatPage.tsx',
            'src/components/DocumentsPage.tsx'
          ],
          // 聊天相关组件
          chat: [
            'src/components/chat/ChatInterface.tsx',
            'src/components/chat/MessageList.tsx',
            'src/components/chat/VirtualizedMessageList.tsx'
          ],
          // 文档相关组件
          document: [
            'src/components/document/DocumentManager.tsx',
            'src/components/document/DocumentUpload.tsx'
          ],
        },
        // 优化chunk大小
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop().replace('.tsx', '').replace('.ts', '')
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
      },
    },
    // 优化构建
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'lucide-react'
    ],
  },
}) 