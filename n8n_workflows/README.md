# n8n RAG本地知识库工作流

这是一个完整的n8n RAG（检索增强生成）工作流，支持文档上传、向量化存储、检索和基于本地ollama模型的智能问答。

## 功能特性

- 📄 **文档上传**: 支持 txt、md、pdf、docx 格式文档上传
- 🔄 **自动处理**: 自动文档切分、向量化和存储
- 🔍 **智能检索**: 基于向量相似度的文档检索
- 🎯 **智能重排序**: 使用BGE重排序模型优化检索结果
- 🤖 **本地模型**: 使用 ollama 部署的本地模型
- 🌊 **流式输出**: 支持流式响应和标准JSON响应
- 📡 **Webhook接口**: 提供RESTful API接口

## 📈 功能特点

### 🧠 Qwen3模型支持
- **嵌入模型**: `dengcao/Qwen3-Embedding-4B:Q4_K_M` - 高质量中文向量表示
- **聊天模型**: `qwen3:8b` - 强大的中文对话和理解能力
- 专为中文优化，支持更准确的语义理解

### 💾 内存存储系统
- 使用全局变量进行向量存储（避免文件系统限制）
- 支持会话期间的持久化存储
- 自动去重和更新机制

### 🎯 智能重排序
- 基于相似度和内容长度的综合排序
- 自动过滤低相关性文档
- 优化检索结果的相关性

### ⚠️ 重要说明
- **存储方式**: 当前版本使用内存存储，n8n重启后数据会丢失
- **生产建议**: 对于生产环境，建议集成专业的向量数据库
- **模块限制**: 工作流已优化，避免使用n8n中受限的Node.js模块

## 前置要求

### 1. 安装 ollama
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.com/install.sh | sh

# Windows
# 下载并安装 ollama 客户端
```

### 2. 安装必要的模型
```bash
# 安装嵌入模型
ollama pull dengcao/Qwen3-Embedding-4B:Q4_K_M

# 安装聊天模型
ollama pull qwen3:8b

# 安装重排序模型
ollama pull xitao/bge-reranker-v2-m3:latest
```

### 3. 启动 ollama 服务
```bash
ollama serve
```

## 安装和配置

### 1. 导入工作流
1. 打开 n8n 界面
2. 点击「工作流」→「导入」
3. 选择 `rag_workflow.json` 文件
4. 点击「导入」

### 2. 激活工作流
1. 在工作流编辑器中，点击右上角的「激活」开关
2. 确保所有节点都正确配置

### 3. 获取 Webhook URL
激活工作流后，你可以在以下节点获取webhook URL：
- **文档上传Webhook**: 用于上传文档
- **查询Webhook**: 用于查询知识库

## API 使用指南

### 1. 文档上传 API

**接口**: `POST /webhook/rag-upload`

**请求体**:
```json
{
  "files": [
    {
      "name": "document.txt",
      "content": "这是文档内容..."
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "message": "文档上传并处理成功",
  "processed_chunks": 5
}
```

### 2. 查询 API

**接口**: `POST /webhook/rag-query`

#### 标准查询
```json
{
  "query": "什么是人工智能？",
  "stream": false
}
```

**响应**:
```json
{
  "answer": "基于您的文档，人工智能是...",
  "sources": [
    {
      "similarity": 0.85,
      "content": "相关文档内容片段",
      "metadata": {
        "source": "ai_basics.txt",
        "chunk_index": 0
      }
    }
  ]
}
```

#### 流式查询
```json
{
  "query": "什么是机器学习？",
  "stream": true
}
```

**响应**: 流式文本响应

## 使用示例

### 1. 使用 curl 上传文档
```bash
curl -X POST "http://localhost:5678/webhook/rag-upload" \
  -H "Content-Type: application/json" \
  -d '{
    "files": [
      {
        "name": "ai_guide.txt",
        "content": "人工智能是计算机科学的一个分支，旨在创造能够执行通常需要人类智能的任务的机器。"
      }
    ]
  }'
```

### 2. 使用 curl 查询知识库
```bash
curl -X POST "http://localhost:5678/webhook/rag-query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是人工智能？",
    "stream": false
  }'
```

### 3. 使用 Python 调用 API
```python
import requests
import json

# 上传文档
def upload_document(content, filename):
    url = "http://localhost:5678/webhook/rag-upload"
    data = {
        "files": [
            {
                "name": filename,
                "content": content
            }
        ]
    }
    response = requests.post(url, json=data)
    return response.json()

# 查询知识库
def query_knowledge_base(query, stream=False):
    url = "http://localhost:5678/webhook/rag-query"
    data = {
        "query": query,
        "stream": stream
    }
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 上传文档
    with open("document.txt", "r", encoding="utf-8") as f:
        content = f.read()
    
    result = upload_document(content, "document.txt")
    print("上传结果:", result)
    
    # 查询
    answer = query_knowledge_base("文档的主要内容是什么？")
    print("查询结果:", answer)
```

## 工作流架构图

```mermaid
graph TD
    A[文档上传Webhook] --> B[处理上传文档]
    B --> C[文档切分]
    C --> D["生成向量嵌入<br/>(Qwen3-Embedding-4B)"]
    D --> E[存储向量]
    E --> F[上传响应]
    
    G[查询Webhook] --> H[处理查询]
    H --> I["查询向量化<br/>(Qwen3-Embedding-4B)"]
    I --> J[检索相关文档]
    J --> K["文档重排序<br/>(BGE-Reranker-v2-M3)"]
    K --> L["生成回答<br/>(Qwen3:8B)"]
    L --> M[检查流式输出]
    M --> N{流式输出?}
    N -->|是| O[流式响应]
    N -->|否| P[JSON响应]
    
    style A fill:#e1f5fe
    style G fill:#e1f5fe
    style D fill:#f3e5f5
    style I fill:#f3e5f5
    style K fill:#fff3e0
    style L fill:#e8f5e8
    style O fill:#fce4ec
    style P fill:#fce4ec
```

## 工作流节点说明

### 文档上传流程
1. **文档上传Webhook**: 接收文档上传请求
2. **处理上传文档**: 验证文件格式和内容
3. **文档切分**: 将文档按段落切分成小块
4. **生成向量嵌入**: 使用 Qwen3-Embedding-4B 模型生成向量
5. **存储向量**: 将向量和元数据存储到内存全局变量
6. **上传响应**: 返回处理结果

### 查询流程
1. **查询Webhook**: 接收查询请求
2. **处理查询**: 验证查询内容和参数
3. **查询向量化**: 将查询转换为向量
4. **检索相关文档**: 从内存数据库检索相关文档
5. **文档重排序**: 基于相似度和内容长度进行智能排序
6. **生成回答**: 使用 qwen3:8b 模型生成回答
7. **检查流式输出**: 判断是否需要流式响应
8. **响应**: 返回答案和相关源文档

## 自定义配置

### 修改模型
在相应的节点中修改模型名称：
- **嵌入模型**: 在「生成向量嵌入」和「查询向量化」节点中修改 `model` 参数（当前: `dengcao/Qwen3-Embedding-4B:Q4_K_M`）
- **聊天模型**: 在「生成回答」节点中修改 `model` 参数（当前: `qwen3:8b`）
- **重排序模型**: 在「文档重排序」节点中修改 `model` 参数（当前: `xitao/bge-reranker-v2-m3:latest`）

### 调整检索参数
在「检索相关文档」节点中可以调整：
- 检索结果数量：修改 `topResults = results.slice(0, 3)` 中的数字
- 相似度阈值：添加相似度过滤条件

### 更改存储路径
在「存储向量」节点中修改 `vectorDbPath` 变量来更改向量数据库存储路径。

## 故障排除

### 常见问题

1. **ollama 连接失败**
   - 确保 ollama 服务正在运行：`ollama serve`
   - 检查端口是否为 11434

2. **模型未找到**
   - 确保已安装所需模型：
     - `ollama pull dengcao/Qwen3-Embedding-4B:Q4_K_M`
     - `ollama pull qwen3:8b`
     - `ollama pull xitao/bge-reranker-v2-m3:latest`

3. **内存存储问题**
   - 数据存储在n8n进程的内存中
   - n8n重启后需要重新上传文档

4. **文档格式不支持**
   - 当前支持：txt、md、pdf、docx
   - 需要其他格式可以在「处理上传文档」节点中添加

## 生产环境建议

1. **向量数据库**: 使用专业的向量数据库如 Pinecone、Weaviate 或 Qdrant
2. **文档存储**: 使用对象存储服务如 AWS S3 或 MinIO
3. **负载均衡**: 配置多个 n8n 实例进行负载均衡
4. **监控**: 添加日志和监控节点
5. **安全**: 配置API密钥和访问控制

## 许可证

MIT License - 可自由使用和修改。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工作流。 