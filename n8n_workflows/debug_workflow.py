#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
n8n RAG工作流调试脚本
用于检查工作流是否正常运行
"""

import requests
import json

def test_webhook_connectivity():
    """测试webhook连接性"""
    print("🔍 测试webhook连接性...")
    
    # 使用固定的webhook路径进行测试
    upload_url = "http://localhost:5678/webhook/rag-upload"
    query_url = "http://localhost:5678/webhook/rag-query"
    
    # 测试上传webhook
    print("\n📄 测试上传webhook...")
    try:
        response = requests.get(upload_url, timeout=10)
        print(f"GET请求状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:300]}")
    except Exception as e:
        print(f"GET请求失败: {e}")
    
    # 测试POST请求
    print("\n📄 测试POST请求...")
    test_data = {
        "files": [
            {
                "name": "test.txt",
                "content": "这是一个测试文档"
            }
        ]
    }
    
    try:
        response = requests.post(upload_url, json=test_data, timeout=30)
        print(f"POST请求状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:500]}")
    except Exception as e:
        print(f"POST请求失败: {e}")
    
    # 测试查询webhook
    print("\n🔍 测试查询webhook...")
    query_data = {
        "query": "测试查询",
        "stream": False
    }
    
    try:
        response = requests.post(query_url, json=query_data, timeout=30)
        print(f"查询请求状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:500]}")
    except Exception as e:
        print(f"查询请求失败: {e}")

def check_n8n_health():
    """检查n8n服务健康状态"""
    print("\n🏥 检查n8n服务状态...")
    
    try:
        # 尝试访问n8n主页
        response = requests.get("http://localhost:5678", timeout=10)
        print(f"n8n主页状态码: {response.status_code}")
        
        # 检查是否是n8n的响应
        if "n8n" in response.text.lower():
            print("✅ n8n服务正常运行")
        else:
            print("⚠️  响应不像是n8n服务")
            
    except Exception as e:
        print(f"❌ n8n服务检查失败: {e}")

def check_ollama_health():
    """检查ollama服务状态"""
    print("\n🤖 检查ollama服务状态...")
    
    try:
        # 检查ollama服务
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        print(f"ollama状态码: {response.status_code}")
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ ollama服务正常，已安装模型: {len(models.get('models', []))}个")
            
            # 检查所需模型
            required_models = [
                'dengcao/Qwen3-Embedding-4B:Q4_K_M',
                'qwen3:8b',
                'xitao/bge-reranker-v2-m3:latest'
            ]
            
            installed_models = [m['name'] for m in models.get('models', [])]
            print(f"已安装模型: {installed_models}")
            
            for model in required_models:
                if any(model in installed for installed in installed_models):
                    print(f"✅ 模型已安装: {model}")
                else:
                    print(f"❌ 模型未安装: {model}")
        else:
            print("❌ ollama服务异常")
            
    except Exception as e:
        print(f"❌ ollama服务检查失败: {e}")

def main():
    """主函数"""
    print("🚀 开始n8n RAG工作流调试...")
    print("=" * 60)
    
    # 检查服务状态
    check_n8n_health()
    check_ollama_health()
    
    # 测试webhook连接性
    test_webhook_connectivity()
    
    print("\n" + "=" * 60)
    print("🎯 调试建议:")
    print("1. 确保n8n服务正在运行")
    print("2. 确保ollama服务正在运行且模型已安装")
    print("3. 确保工作流已激活")
    print("4. 检查工作流节点配置是否正确")
    print("5. 查看n8n的执行日志")

if __name__ == "__main__":
    main() 