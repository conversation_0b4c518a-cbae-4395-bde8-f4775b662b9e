{"name": "RAG本地知识库工作流", "nodes": [{"parameters": {"httpMethod": "POST", "path": "rag-upload", "responseMode": "responseNode", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}]}}}, "id": "upload-webhook", "name": "文档上传Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "upload-docs"}, {"parameters": {"httpMethod": "POST", "path": "rag-query", "responseMode": "responseNode", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "text/plain"}]}}}, "id": "query-webhook", "name": "查询Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600], "webhookId": "query-docs"}, {"parameters": {"jsCode": "// 处理文档上传\nconst input = $input.first();\nconsole.log('Input data:', JSON.stringify(input, null, 2));\n\n// 从webhook获取文件数据\nconst files = input.binary || {};\nconst body = input.json.body || {};\nconst filename = body.filename || 'unknown.txt';\n\nconst results = [];\n\n// 处理上传的文件\nif (files.file) {\n  const fileData = files.file;\n  const fileExt = filename.split('.').pop().toLowerCase();\n  \n  // 验证文件类型\n  const allowedTypes = ['txt', 'md', 'pdf', 'docx'];\n  \n  if (allowedTypes.includes(fileExt)) {\n    // 读取文件内容\n    let content = '';\n    if (fileData.data) {\n      // 将buffer转换为字符串\n      content = Buffer.from(fileData.data, 'base64').toString('utf-8');\n    }\n    \n    results.push({\n      filename: filename,\n      content: content,\n      type: fileExt,\n      timestamp: new Date().toISOString(),\n      document_id: 'doc_' + Date.now()\n    });\n  }\n}\n\n// 如果没有处理任何文件，返回错误信息\nif (results.length === 0) {\n  return [{ json: { error: '没有找到有效的文件或文件类型不支持', filename: filename } }];\n}\n\nreturn results.map(item => ({ json: item }));"}, "id": "process-upload", "name": "处理上传文档", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// 文档切分\nconst input = $input.first().json;\nconst content = input.content;\nconst filename = input.filename;\n\n// 简单的文本切分，按段落分割\nconst chunks = content.split('\\n\\n').filter(chunk => chunk.trim().length > 0);\nconst results = [];\n\nchunks.forEach((chunk, index) => {\n  if (chunk.trim().length > 50) { // 过滤太短的内容\n    results.push({\n      filename: filename,\n      chunk_id: `${filename}_${index}`,\n      content: chunk.trim(),\n      metadata: {\n        source: filename,\n        chunk_index: index,\n        timestamp: input.timestamp\n      }\n    });\n  }\n});\n\nreturn results.map(item => ({ json: item }));"}, "id": "chunk-documents", "name": "文档切分", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:11434/api/embeddings", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "dengcao/Qwen3-Embedding-4B:Q4_K_M"}, {"name": "prompt", "value": "={{$json.content}}"}]}}, "id": "generate-embeddings", "name": "生成向量嵌入", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [900, 300]}, {"parameters": {"jsCode": "// 存储向量到内存（简化版本，避免fs模块限制）\nconst input = $input.first().json;\nconst embedding = input.embedding;\nconst originalData = $node[\"文档切分\"].json;\n\n// 使用全局变量存储向量数据（内存存储）\nif (typeof global.vectorDatabase === 'undefined') {\n  global.vectorDatabase = [];\n}\n\n// 存储向量数据\nconst vectorData = {\n  id: originalData.chunk_id,\n  content: originalData.content,\n  embedding: embedding,\n  metadata: originalData.metadata,\n  created_at: new Date().toISOString()\n};\n\n// 检查是否已存在，如果存在则更新，否则添加\nconst existingIndex = global.vectorDatabase.findIndex(item => item.id === vectorData.id);\nif (existingIndex >= 0) {\n  global.vectorDatabase[existingIndex] = vectorData;\n} else {\n  global.vectorDatabase.push(vectorData);\n}\n\nconsole.log(`向量数据库当前大小: ${global.vectorDatabase.length}`);\n\nreturn [{ json: { \n  success: true, \n  stored_id: originalData.chunk_id,\n  database_size: global.vectorDatabase.length \n} }];"}, "id": "store-vectors", "name": "存储向量", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"document_id\": $node[\"处理上传文档\"].json.document_id || \"doc_\" + $runIndex, \"status\": \"completed\", \"message\": \"文档上传并处理成功\", \"filename\": $node[\"处理上传文档\"].json.filename || \"unknown\", \"chunks_count\": $itemIndex + 1 } }}"}, "id": "upload-response", "name": "上传响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// 处理查询请求\nconst body = $input.first().json.body;\n// 支持多种查询字段格式：query, message\nconst query = body.query || body.message || '';\nconst stream = body.stream || false;\nconst sessionId = body.session_id || body.sessionId || '';\n\nif (!query.trim()) {\n  return [{ json: { error: '查询内容不能为空' } }];\n}\n\nreturn [{ json: { \n  query: query.trim(), \n  stream: stream,\n  session_id: sessionId\n} }];"}, "id": "process-query", "name": "处理查询", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 600]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:11434/api/embeddings", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "dengcao/Qwen3-Embedding-4B:Q4_K_M"}, {"name": "prompt", "value": "={{$json.query}}"}]}}, "id": "query-embeddings", "name": "查询向量化", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 600]}, {"parameters": {"jsCode": "// 检索相似文档（从内存数据库）\nconst queryEmbedding = $input.first().json.embedding;\nconst queryText = $node[\"处理查询\"].json.query;\nconst sessionId = $node[\"处理查询\"].json.session_id;\n\nconsole.log(`开始检索相关文档，查询: \"${queryText}\", 会话ID: ${sessionId || 'none'}`);\n\n// 从全局变量读取向量数据库\nconst vectorDatabase = global.vectorDatabase || [];\nconst results = [];\n\nconsole.log(`从向量数据库检索，数据库大小: ${vectorDatabase.length}`);\n\n// 调试输出向量数据库内容\nif (vectorDatabase.length > 0) {\n  console.log('向量数据库内容预览:');\n  vectorDatabase.slice(0, 2).forEach((item, index) => {\n    console.log(`[${index}] ID: ${item.id}, 内容: ${item.content.substring(0, 50)}...`);\n  });\n  \n  for (const vectorData of vectorDatabase) {\n    // 计算余弦相似度\n    const similarity = cosineSimilarity(queryEmbedding, vectorData.embedding);\n    \n    results.push({\n      similarity: similarity,\n      content: vectorData.content,\n      metadata: vectorData.metadata,\n      id: vectorData.id\n    });\n  }\n} else {\n  console.log('警告: 向量数据库为空，请先上传文档');\n}\n\n// 排序并获取最相似的前5个结果\nresults.sort((a, b) => b.similarity - a.similarity);\nconst topResults = results.slice(0, 5);\n\nconsole.log(`检索到 ${topResults.length} 个相关文档`);\n\n// 输出检索结果预览\nif (topResults.length > 0) {\n  console.log('检索结果预览:');\n  topResults.forEach((doc, index) => {\n    console.log(`[${index}] 相似度: ${doc.similarity.toFixed(4)}, 内容: ${doc.content.substring(0, 50)}...`);\n  });\n}\n\n// 余弦相似度计算函数\nfunction cosineSimilarity(a, b) {\n  if (!a || !b || a.length !== b.length) {\n    return 0;\n  }\n  const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);\n  const normA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));\n  const normB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));\n  \n  if (normA === 0 || normB === 0) {\n    return 0;\n  }\n  \n  return dotProduct / (normA * normB);\n}\n\nreturn [{ json: { \n  query: queryText,\n  relevant_docs: topResults,\n  context: topResults.map(doc => doc.content).join('\\n\\n'),\n  database_size: vectorDatabase.length,\n  session_id: sessionId\n} }];"}, "id": "retrieve-docs", "name": "检索相关文档", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 600]}, {"parameters": {"jsCode": "// 简化的重排序逻辑（避免axios模块限制）\nconst input = $input.first().json;\nconst query = $node[\"处理查询\"].json.query;\nconst relevantDocs = input.relevant_docs || [];\n\nconsole.log(`重排序输入: ${relevantDocs.length} 个文档`);\n\nif (relevantDocs.length === 0) {\n  return [{ json: { \n    reranked_docs: [], \n    context: '',\n    query: query\n  } }];\n}\n\n// 简化版重排序：基于相似度分数进行排序（已经排序过了）\n// 可以添加更多的排序逻辑，比如基于文档长度、关键词匹配等\nconst rerankedDocs = relevantDocs\n  .filter(doc => doc.similarity > 0.1) // 过滤相似度太低的文档\n  .sort((a, b) => {\n    // 综合考虑相似度和内容长度\n    const scoreA = a.similarity * (1 + Math.log(a.content.length + 1) / 10);\n    const scoreB = b.similarity * (1 + Math.log(b.content.length + 1) / 10);\n    return scoreB - scoreA;\n  })\n  .slice(0, 3); // 取前3个最相关的\n\nconsole.log(`重排序输出: ${rerankedDocs.length} 个文档`);\n\nreturn [{ json: {\n  query: query,\n  reranked_docs: rerankedDocs,\n  context: rerankedDocs.map(doc => doc.content).join('\\n\\n'),\n  rerank_method: 'similarity_based'\n} }];"}, "id": "rerank-documents", "name": "文档重排序", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1010, 600]}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:11434/api/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "qwen3:8b"}, {"name": "prompt", "value": "=基于以下已重排序的上下文信息回答用户问题：\n\n上下文：\n{{$json.context}}\n\n用户问题：{{$json.query}}\n\n请基于上下文信息提供准确的回答，如果上下文中没有相关信息，请说明无法从提供的文档中找到答案。"}, {"name": "stream", "value": "={{$node[\"处理查询\"].json.stream}}"}]}}, "id": "generate-answer", "name": "生成回答", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [1230, 600]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{$node[\"处理查询\"].json.stream}}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}}, "id": "check-stream", "name": "检查流式输出", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1450, 600]}, {"parameters": {"respondWith": "text", "responseBody": "={{$json.response}}"}, "id": "stream-response", "name": "流式响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1670, 520]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"answer\": $node[\"生成回答\"].json.response || $node[\"生成回答\"].json.text || \"无法生成回答\", \"sources\": $node[\"文档重排序\"].json.reranked_docs || $node[\"检索相关文档\"].json.relevant_docs || [], \"query\": $node[\"处理查询\"].json.query || \"未知查询\", \"session_id\": $node[\"处理查询\"].json.session_id || \"\", \"database_size\": $node[\"检索相关文档\"].json.database_size || 0 } }}"}, "id": "json-response", "name": "JSON响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1670, 680]}], "pinData": {}, "connections": {"文档上传Webhook": {"main": [[{"node": "处理上传文档", "type": "main", "index": 0}]]}, "查询Webhook": {"main": [[{"node": "处理查询", "type": "main", "index": 0}]]}, "处理上传文档": {"main": [[{"node": "文档切分", "type": "main", "index": 0}]]}, "文档切分": {"main": [[{"node": "生成向量嵌入", "type": "main", "index": 0}]]}, "生成向量嵌入": {"main": [[{"node": "存储向量", "type": "main", "index": 0}]]}, "存储向量": {"main": [[{"node": "上传响应", "type": "main", "index": 0}]]}, "处理查询": {"main": [[{"node": "查询向量化", "type": "main", "index": 0}]]}, "查询向量化": {"main": [[{"node": "检索相关文档", "type": "main", "index": 0}]]}, "检索相关文档": {"main": [[{"node": "文档重排序", "type": "main", "index": 0}]]}, "文档重排序": {"main": [[{"node": "生成回答", "type": "main", "index": 0}]]}, "生成回答": {"main": [[{"node": "检查流式输出", "type": "main", "index": 0}]]}, "检查流式输出": {"main": [[{"node": "流式响应", "type": "main", "index": 0}], [{"node": "JSON响应", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "rag-workflow", "tags": []}