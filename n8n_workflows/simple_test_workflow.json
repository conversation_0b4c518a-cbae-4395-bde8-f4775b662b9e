{"name": "简单测试工作流", "nodes": [{"parameters": {"path": "test-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-test", "name": "测试Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "test-webhook-id"}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"success\", \"message\": \"测试成功\", \"timestamp\": new Date().toISOString() } }}"}, "id": "response-test", "name": "返回响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 300]}], "connections": {"测试Webhook": {"main": [[{"node": "返回响应", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "test-version"}