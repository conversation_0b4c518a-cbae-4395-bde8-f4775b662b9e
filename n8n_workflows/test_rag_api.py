#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
n8n RAG知识库API测试脚本
测试文档上传和查询功能
"""

import requests
import json
import time
from typing import Dict, Any, List, Optional

class RAGClient:
    def __init__(self, base_url: str = "http://localhost:5678", upload_webhook_id: Optional[str] = None, query_webhook_id: Optional[str] = None):
        self.base_url = base_url
        
        # 如果提供了具体的webhook ID，则使用完整URL
        if upload_webhook_id:
            self.upload_url = f"{base_url}/webhook/{upload_webhook_id}"
        else:
            # 使用默认路径（可能不工作，需要用户手动设置）
            self.upload_url = f"{base_url}/webhook/rag-upload"
            
        if query_webhook_id:
            self.query_url = f"{base_url}/webhook/{query_webhook_id}"
        else:
            # 使用默认路径（可能不工作，需要用户手动设置）
            self.query_url = f"{base_url}/webhook/rag-query"
    
    def set_webhook_urls(self, upload_url: str, query_url: str):
        """手动设置webhook URL"""
        self.upload_url = upload_url
        self.query_url = query_url
    
    def upload_document(self, filename: str, content: str) -> Dict[str, Any]:
        """上传文档到RAG知识库"""
        data = {
            "files": [
                {
                    "name": filename,
                    "content": content
                }
            ]
        }
        
        try:
            print(f"  📡 使用上传URL: {self.upload_url}")
            response = requests.post(self.upload_url, json=data, timeout=30)
            
            # 调试信息
            print(f"  📊 HTTP状态码: {response.status_code}")
            print(f"  📋 响应头: {response.headers.get('content-type', 'unknown')}")
            print(f"  📄 响应内容前200字符: {response.text[:200]}")
            
            response.raise_for_status()
            
            # 检查响应是否为JSON
            try:
                return response.json()
            except json.JSONDecodeError:
                return {"error": f"服务器返回非JSON响应: {response.text[:500]}"}
                
        except requests.exceptions.RequestException as e:
            return {"error": f"上传失败: {str(e)}"}
    
    def query_knowledge_base(self, query: str, stream: bool = False) -> Dict[str, Any]:
        """查询RAG知识库"""
        data = {
            "query": query,
            "stream": stream
        }
        
        try:
            print(f"  📡 使用查询URL: {self.query_url}")
            response = requests.post(self.query_url, json=data, timeout=30)
            
            # 调试信息
            print(f"  📊 HTTP状态码: {response.status_code}")
            print(f"  📋 响应头: {response.headers.get('content-type', 'unknown')}")
            print(f"  📄 响应内容前200字符: {response.text[:200]}")
            
            response.raise_for_status()
            
            if stream:
                return {"response": response.text}
            else:
                # 检查响应是否为JSON
                try:
                    return response.json()
                except json.JSONDecodeError:
                    return {"error": f"服务器返回非JSON响应: {response.text[:500]}"}
                    
        except requests.exceptions.RequestException as e:
            return {"error": f"查询失败: {str(e)}"}
    
    def upload_file(self, file_path: str) -> Dict[str, Any]:
        """从文件上传文档"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            filename = file_path.split('/')[-1]  # 获取文件名
            return self.upload_document(filename, content)
        except FileNotFoundError:
            return {"error": f"文件未找到: {file_path}"}
        except Exception as e:
            return {"error": f"读取文件失败: {str(e)}"}

def get_webhook_urls():
    """获取用户输入的webhook URL"""
    print("🔗 请从n8n界面中获取实际的webhook URL:")
    print("   1. 打开工作流编辑器")
    print("   2. 点击'文档上传Webhook'节点，复制Production URL")
    print("   3. 点击'查询Webhook'节点，复制Production URL")
    print()
    
    upload_url = input("📄 请输入文档上传Webhook URL: ").strip()
    query_url = input("🔍 请输入查询Webhook URL: ").strip()
    
    return upload_url, query_url

def test_rag_workflow():
    """测试RAG工作流"""
    print("🚀 开始测试n8n RAG工作流（使用Qwen3模型）...")
    
    # 获取webhook URL
    upload_url, query_url = get_webhook_urls()
    
    if not upload_url or not query_url:
        print("❌ 错误: 需要提供有效的webhook URL")
        return
    
    # 创建客户端并设置URL
    client = RAGClient()
    client.set_webhook_urls(upload_url, query_url)
    
    # 测试文档
    test_documents = [
        {
            "filename": "ai_basics.txt",
            "content": """人工智能简介

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，旨在创造能够执行通常需要人类智能的任务的机器。

AI的主要分类：
1. 弱人工智能（Narrow AI）：专门为特定任务设计的AI系统
2. 强人工智能（General AI）：具有与人类相当智能水平的AI系统
3. 超人工智能（Super AI）：在各个方面都超越人类智能的AI系统

机器学习是AI的核心技术之一，包括：
- 监督学习：使用标记数据训练模型
- 无监督学习：从未标记数据中发现模式
- 强化学习：通过试错学习最优策略

深度学习是机器学习的子集，使用神经网络模拟人脑的工作方式。"""
        },
        {
            "filename": "python_guide.txt",
            "content": """Python编程指南

Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。

Python的特点：
- 易于学习和使用
- 跨平台兼容
- 丰富的库和框架
- 活跃的社区支持

常用Python库：
1. NumPy：数值计算
2. Pandas：数据分析
3. Matplotlib：数据可视化
4. Scikit-learn：机器学习
5. TensorFlow/PyTorch：深度学习

Python在以下领域广泛应用：
- Web开发（Django、Flask）
- 数据科学和分析
- 人工智能和机器学习
- 自动化脚本
- 游戏开发"""
        }
    ]
    
    # 测试查询
    test_queries = [
        "什么是人工智能？",
        "Python有哪些特点？",
        "机器学习的主要类型有哪些？",
        "推荐一些Python的机器学习库",
        "深度学习和机器学习的关系是什么？"
    ]
    
    print("\n📄 测试文档上传...")
    for doc in test_documents:
        print(f"  上传文档: {doc['filename']}")
        result = client.upload_document(doc['filename'], doc['content'])
        if 'error' in result:
            print(f"  ❌ 错误: {result['error']}")
        else:
            print(f"  ✅ 成功: {result.get('message', '上传完成')}")
        time.sleep(2)  # 等待处理完成
    
    print("\n🔍 测试查询...")
    for query in test_queries:
        print(f"\n  📝 查询: {query}")
        result = client.query_knowledge_base(query, stream=False)
        
        if 'error' in result:
            print(f"  ❌ 错误: {result['error']}")
        else:
            print(f"  💬 回答: {result.get('answer', '未获取到回答')}")
            
            # 显示相关源文档
            sources = result.get('sources', [])
            if sources:
                print(f"  📚 相关文档 ({len(sources)}个):")
                for i, source in enumerate(sources[:2], 1):  # 只显示前2个
                    similarity = source.get('similarity', 0)
                    content = source.get('content', '')[:100] + '...'
                    print(f"    {i}. 相似度: {similarity:.3f}")
                    print(f"       内容: {content}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n🌊 测试流式查询...")
    stream_query = "详细解释什么是深度学习"
    print(f"  📝 流式查询: {stream_query}")
    result = client.query_knowledge_base(stream_query, stream=True)
    
    if 'error' in result:
        print(f"  ❌ 错误: {result['error']}")
    else:
        print(f"  💬 流式回答: {result.get('response', '未获取到回答')}")
    
    print("\n✅ 测试完成!")

def test_with_predefined_urls():
    """使用预定义URL进行快速测试（需要用户手动修改URL）"""
    print("🚀 使用预定义URL测试...")
    print("⚠️  注意: 请先修改下方的URL为你实际的webhook URL")
    
    # 用户需要手动替换这些URL
    upload_url = "http://localhost:5678/webhook/你的上传webhook-id"
    query_url = "http://localhost:5678/webhook/你的查询webhook-id"
    
    client = RAGClient()
    client.set_webhook_urls(upload_url, query_url)
    
    # 简单测试
    result = client.upload_document("test.txt", "这是一个测试文档")
    print(f"上传结果: {result}")

if __name__ == "__main__":
    try:
        print("选择测试模式:")
        print("1. 交互式测试（推荐）")
        print("2. 预定义URL测试")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "2":
            test_with_predefined_urls()
        else:
            test_rag_workflow()
            
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试失败: {str(e)}") 