# 🔧 n8n RAG工作流故障排除指南

## 🚨 常见错误分析

### 错误1: `Cannot find module 'fs'` ✅ 已修复
**含义**: n8n Code节点不支持Node.js的fs模块

**解决方案**: 
- ✅ 已在最新版本中修复
- 改用内存存储替代文件系统
- 重新导入最新的rag_workflow.json

### 错误2: `Expecting value: line 1 column 1 (char 0)`
**含义**: 服务器返回的不是JSON格式的数据

**可能原因**:
1. n8n工作流没有正确激活
2. 工作流执行时出现错误
3. 返回的是HTML错误页面而不是JSON
4. 节点配置错误

**解决方案**:
1. 运行调试脚本查看详细信息
2. 检查n8n工作流状态
3. 查看n8n执行日志

### 错误3: `404 Not Found`
**含义**: URL路径不存在

**可能原因**:
1. 使用了错误的webhook URL
2. 工作流未激活
3. n8n服务未运行

**解决方案**:
1. 从n8n界面获取正确的Production URL
2. 确保工作流已激活
3. 检查n8n服务状态

## 🔍 详细调试步骤

### 步骤1: 运行调试脚本
```bash
python debug_workflow.py
```

这会检查:
- n8n服务状态
- ollama服务状态
- webhook连接性
- 已安装的模型

### 步骤2: 运行带调试信息的测试
```bash
python test_rag_api.py
```

新版本会显示:
- HTTP状态码
- 响应头信息
- 响应内容前200字符

### 步骤3: 检查n8n工作流状态

在n8n界面中:
1. 确认工作流已激活（开关为蓝色）
2. 检查是否有执行错误
3. 查看执行历史记录

### 步骤4: 验证webhook URL

在n8n界面中:
1. 点击webhook节点
2. 检查"Production URL"
3. 确保URL格式正确

## 🛠️ 具体修复方案

### 修复方案1: 重新激活工作流
1. 在n8n中打开工作流
2. 点击"停用"然后"激活"
3. 等待所有节点状态更新

### 修复方案2: 检查节点配置
1. 检查webhook节点的"Path"设置
2. 确保"Response Mode"设置为"Response Node"
3. 检查"HTTP Method"设置为"POST"

### 修复方案3: 验证ollama连接
```bash
# 检查ollama服务
curl http://localhost:11434/api/tags

# 测试嵌入模型
curl -X POST http://localhost:11434/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "model": "dengcao/Qwen3-Embedding-4B:Q4_K_M",
    "prompt": "测试文本"
  }'
```

### 修复方案4: 检查模块兼容性
1. ✅ 已修复: 移除了fs模块依赖
2. ✅ 已修复: 移除了axios模块依赖  
3. ✅ 已修复: 使用内存存储替代文件系统
4. 确保n8n能访问ollama服务

## 📊 错误代码对照表

| 状态码 | 含义 | 可能原因 | 解决方案 |
|--------|------|----------|----------|
| 200 | 成功 | 请求正常 | 检查响应内容 |
| 404 | 未找到 | URL错误 | 使用正确的webhook URL |
| 500 | 服务器错误 | 工作流执行错误 | 查看n8n日志 |
| 502 | 网关错误 | 服务连接问题 | 检查ollama服务 |

## 🎯 快速诊断清单

### ✅ 服务状态检查
- [ ] n8n服务正在运行 (http://localhost:5678)
- [ ] ollama服务正在运行 (http://localhost:11434)
- [ ] 工作流已激活

### ✅ 模型检查
- [ ] dengcao/Qwen3-Embedding-4B:Q4_K_M 已安装
- [ ] qwen3:8b 已安装
- [ ] xitao/bge-reranker-v2-m3:latest 已安装

### ✅ 配置检查
- [ ] webhook URL正确
- [ ] 节点配置正确
- [ ] 权限设置正确

## 🔄 重置工作流

如果问题持续存在，可以尝试重置:

1. **重新导入工作流**:
   - 删除现有工作流
   - 重新导入rag_workflow.json
   - 重新激活

2. **清理内存数据**:
   - 重启n8n服务自动清理内存数据
   - 或者重新激活工作流

3. **重启服务**:
   ```bash
   # 重启ollama
   pkill ollama
   ollama serve
   
   # 重启n8n (如果使用docker)
   docker restart n8n
   ```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息:
1. 调试脚本的完整输出
2. n8n工作流的执行日志
3. 错误的具体截图
4. 使用的操作系统和版本

## 📚 相关资源

- [n8n官方文档](https://docs.n8n.io/)
- [ollama文档](https://ollama.com/docs)
- [工作流配置指南](./README.md) 