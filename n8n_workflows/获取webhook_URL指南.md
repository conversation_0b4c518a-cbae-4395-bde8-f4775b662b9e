# 📡 获取n8n Webhook URL指南

## 🚫 为什么会出现404错误？

n8n为了安全考虑，每个webhook节点在激活时都会生成唯一的URL，而不是使用我们在JSON中设置的固定路径。因此你需要从n8n界面中获取实际的webhook URL。

## 📋 获取步骤

### 1. 打开n8n工作流编辑器
- 进入你的n8n界面
- 找到并打开"RAG本地知识库工作流"

### 2. 获取文档上传Webhook URL
1. 点击"文档上传Webhook"节点
2. 在右侧面板中找到"Production URL"
3. 复制整个URL（类似：`http://localhost:5678/webhook/12345678-1234-1234-1234-123456789abc`）

### 3. 获取查询Webhook URL  
1. 点击"查询Webhook"节点
2. 在右侧面板中找到"Production URL"
3. 复制整个URL

### 4. 运行测试脚本
```bash
python test_rag_api.py
```

## 🎯 快速测试方法

### 方法1：交互式测试（推荐）
```bash
python test_rag_api.py
# 选择 1，然后输入复制的URL
```

### 方法2：直接修改代码
在 `test_rag_api.py` 文件中找到 `test_with_predefined_urls()` 函数，替换其中的URL：

```python
# 替换为你的实际URL
upload_url = "http://localhost:5678/webhook/你的上传webhook-id"
query_url = "http://localhost:5678/webhook/你的查询webhook-id"
```

## 🔧 URL格式示例

正确的URL格式应该是：
```
http://localhost:5678/webhook/f4e3d2c1-b9a8-7654-3210-fedcba098765
```

**不是**：
```
http://localhost:5678/webhook/rag-upload  ❌
```

## 🚨 常见问题

### Q: 工作流已激活但仍然404？
A: 确保复制的是"Production URL"而不是"Test URL"

### Q: URL复制正确但仍然失败？
A: 检查以下几点：
1. n8n服务是否正在运行
2. 工作流是否真的已激活（开关是蓝色的）
3. 端口5678是否正确
4. 是否有防火墙阻止

### Q: 如何验证URL是否正确？
A: 在浏览器中访问webhook URL，应该看到n8n的响应而不是404页面

## 📝 测试流程

1. ✅ 确保ollama服务运行中
2. ✅ 确保n8n工作流已激活
3. ✅ 获取正确的webhook URL
4. ✅ 运行测试脚本
5. ✅ 上传测试文档
6. ✅ 进行查询测试 