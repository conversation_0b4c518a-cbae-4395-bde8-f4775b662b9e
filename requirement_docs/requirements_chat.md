# 智能聊天系统前后端需求文档

## 1. 项目概述

### 1.1 项目背景
基于已部署的n8n RAG工作流，开发完整的前后端聊天系统。n8n已提供核心的文档处理和智能问答API，现需要构建用户友好的Web界面和业务逻辑层。

### 1.2 现有API能力
根据n8n workflow，已具备以下核心API：

#### 1.2.1 文档上传API
- **接口**: `POST /webhook/rag-upload`
- **功能**: 文档上传、切分、向量化、存储
- **支持格式**: txt, md, pdf, docx
- **返回**: 处理状态和切片数量

#### 1.2.2 智能问答API  
- **接口**: `POST /webhook/rag-query`
- **功能**: 基于知识库的智能问答
- **特性**: 支持流式输出，返回答案和源文档
- **模型**: 使用Qwen3系列模型

### 1.3 系统目标
- 构建现代化的聊天界面，支持流式对话体验
- 实现完整的文档管理功能
- 提供多轮对话和上下文管理
- 确保良好的用户体验和系统性能

## 2. 系统架构设计

### 2.1 整体架构
```
用户界面 (React) → 后端API (FastAPI) → n8n Workflows → Ollama Models
        ↓                    ↓
    本地存储          数据库 (SQLite)
```

### 2.2 技术栈选择

#### 2.2.1 前端技术栈
- **框架**: React 18 + TypeScript
- **样式**: Tailwind CSS
- **状态管理**: React Query/SWR
- **流式处理**: EventSource API (Server-Sent Events)
- **文件上传**: React Dropzone
- **Markdown渲染**: React Markdown
- **构建工具**: Vite

#### 2.2.2 后端技术栈
- **框架**: FastAPI (Python 3.9+)
- **数据库**: SQLite
- **ORM**: SQLAlchemy
- **HTTP客户端**: httpx (异步)
- **服务器**: uvicorn
- **任务队列**: 内置异步处理

## 3. 后端需求规格

### 3.1 核心功能模块

#### 3.1.1 会话管理服务
**需求描述**: 管理用户聊天会话和对话历史

**功能详情**:
- 生成和维护唯一session_id
- 存储对话历史记录（用户消息+AI回复）
- 实现上下文管理（因为n8n API无状态）
- 会话超时和清理机制

**API设计**:
```python
POST /api/chat/sessions          # 创建新会话
GET /api/chat/sessions/{id}      # 获取会话信息  
GET /api/chat/sessions/{id}/messages  # 获取会话历史
DELETE /api/chat/sessions/{id}   # 删除会话
```

**数据模型**:
```python
class ChatSession:
    id: str                    # 会话ID
    user_id: str              # 用户ID（可选）
    created_at: datetime      # 创建时间
    updated_at: datetime      # 最后更新时间
    title: str               # 会话标题（可选）

class ChatMessage:
    id: str                   # 消息ID
    session_id: str          # 会话ID
    role: str                # user/assistant/system
    content: str             # 消息内容
    sources: list           # 源文档引用（AI消息）
    timestamp: datetime      # 时间戳
```

#### 3.1.2 消息处理服务
**需求描述**: 处理用户消息，调用n8n API，管理流式响应

**功能详情**:
- 接收用户消息并构建上下文
- 调用n8n查询API（/webhook/rag-query）
- 处理流式响应和标准响应
- 保存对话记录

**API设计**:
```python
POST /api/chat/message           # 发送消息
GET /api/chat/message/{id}/stream # 获取流式响应
```

**请求/响应格式**:
```json
// 请求
{
  "session_id": "session_123",
  "message": "什么是人工智能？",
  "stream": true
}

// 标准响应
{
  "message_id": "msg_456", 
  "content": "基于文档，人工智能是...",
  "sources": [
    {
      "similarity": 0.85,
      "content": "文档片段内容",
      "metadata": {
        "source": "ai_guide.pdf",
        "chunk_index": 0
      }
    }
  ],
  "timestamp": "2024-01-01T00:00:00Z"
}

// 流式响应 (Server-Sent Events)
data: {"type": "token", "content": "基于"}
data: {"type": "token", "content": "文档"}
data: {"type": "sources", "sources": [...]}
data: {"type": "done"}
```

#### 3.1.3 文档管理服务
**需求描述**: 管理用户上传的文档，提供文档列表和元数据管理

**功能详情**:
- 接收文件上传请求
- 调用n8n上传API（/webhook/rag-upload）
- 存储文档元数据（名称、大小、类型、上传时间）
- 提供文档列表查询
- 文档删除功能（标记删除）

**API设计**:
```python
POST /api/documents/upload       # 上传文档
GET /api/documents              # 获取文档列表
GET /api/documents/{id}         # 获取文档详情
DELETE /api/documents/{id}      # 删除文档
```

**数据模型**:
```python
class Document:
    id: str                  # 文档ID
    filename: str           # 原始文件名
    file_type: str          # 文件类型
    file_size: int          # 文件大小
    upload_time: datetime   # 上传时间
    processing_status: str  # 处理状态: uploading/processing/completed/failed
    chunks_count: int       # 切片数量
    error_message: str      # 错误信息（如果有）
```

#### 3.1.4 代理服务层
**需求描述**: 封装n8n API调用，处理错误和重试

**功能详情**:
- 统一n8n API调用接口
- 处理网络错误和重试机制
- 响应格式标准化
- 请求日志和监控

### 3.2 非功能性需求

#### 3.2.1 性能要求
- **响应时间**: 
  - 消息发送后1秒内开始流式输出
  - 文档列表查询<300ms
  - 会话历史查询<500ms
- **并发**: 支持50+并发会话
- **文件处理**: 支持最大100MB文件上传

#### 3.2.2 可靠性要求
- API调用重试机制（最多3次）
- 数据库连接池管理
- 异常处理和错误日志
- 优雅关闭和重启

#### 3.2.3 安全要求
- 文件类型白名单验证
- 文件大小限制
- 请求频率限制（每分钟60次）
- 输入内容过滤和验证

## 4. 前端需求规格

### 4.1 页面结构设计

#### 4.1.1 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│  Header: 标题 + 设置 + 新建会话                                │
├─────────────┬───────────────────────────┬───────────────────┤
│             │                           │                   │
│  文档管理    │      聊天主区域             │   源文档引用       │
│  面板        │                           │   (可折叠)        │
│  (可折叠)    │                           │                   │
│             │                           │                   │
├─────────────┼───────────────────────────┴───────────────────┤
│             │  输入框 + 发送按钮 + 上传按钮                    │
└─────────────┴─────────────────────────────────────────────┘
```

#### 4.1.2 响应式设计
- **桌面端**: 三栏布局（文档管理 + 聊天 + 源引用）
- **平板端**: 两栏布局（聊天 + 可切换的侧边栏）
- **移动端**: 单栏布局，底部导航切换功能

### 4.2 核心功能组件

#### 4.2.1 聊天界面组件
**组件名**: `ChatInterface`

**功能需求**:
- 消息列表展示（用户消息、AI回复、系统提示）
- 支持Markdown渲染（代码高亮、表格、链接等）
- 流式消息显示（打字机效果）
- 消息状态指示（发送中、已送达、失败）
- 消息时间戳显示
- 滚动到底部自动跟随

**交互设计**:
```typescript
interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'failed';
  sources?: DocumentSource[];
}

interface DocumentSource {
  similarity: number;
  content: string;
  metadata: {
    source: string;
    chunk_index: number;
  };
}
```

**状态管理**:
- 当前会话消息列表
- 流式消息缓冲区
- 发送状态管理
- 滚动位置管理

#### 4.2.2 消息输入组件
**组件名**: `MessageInput`

**功能需求**:
- 多行文本输入（自动高度调整）
- 快捷键支持（Enter发送，Shift+Enter换行）
- 输入状态指示（正在输入、发送中）
- 字符计数和限制提示
- 发送按钮状态管理

**技术实现**:
```typescript
interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled: boolean;
  placeholder?: string;
  maxLength?: number;
}
```

#### 4.2.3 文档上传组件
**组件名**: `DocumentUpload`

**功能需求**:
- 拖拽上传支持
- 文件选择器
- 文件格式验证（txt, md, pdf, docx）
- 文件大小验证（最大100MB）
- 上传进度显示
- 批量上传支持

**交互设计**:
- 拖拽区域高亮提示
- 上传进度条和百分比
- 错误信息友好提示
- 成功上传确认反馈

**技术实现**:
```typescript
interface UploadFile {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}
```

#### 4.2.4 文档管理组件
**组件名**: `DocumentManager`

**功能需求**:
- 已上传文档列表展示
- 文档信息显示（名称、大小、类型、上传时间）
- 文档状态指示（处理中、已完成、失败）
- 文档删除功能
- 搜索和过滤功能

**界面设计**:
```typescript
interface DocumentItem {
  id: string;
  filename: string;
  fileType: string;
  fileSize: number;
  uploadTime: Date;
  status: 'processing' | 'completed' | 'failed';
  chunksCount?: number;
  errorMessage?: string;
}
```

#### 4.2.5 源文档引用组件
**组件名**: `SourceReferences`

**功能需求**:
- 显示当前AI回复的源文档片段
- 相似度分数显示
- 源文档链接和定位
- 引用内容高亮显示
- 展开/折叠详细内容

### 4.3 状态管理设计

#### 4.3.1 全局状态
使用React Query管理服务器状态：
```typescript
// 会话状态
const { data: sessions } = useQuery(['sessions'], fetchSessions);
const { data: currentSession } = useQuery(['session', sessionId], () => fetchSession(sessionId));

// 消息状态  
const { data: messages } = useQuery(['messages', sessionId], () => fetchMessages(sessionId));

// 文档状态
const { data: documents } = useQuery(['documents'], fetchDocuments);
```

#### 4.3.2 本地状态
使用useState/useReducer管理UI状态：
- 当前选中的会话ID
- 侧边栏折叠状态
- 主题设置
- 流式消息临时状态

### 4.4 流式响应处理

#### 4.4.1 Server-Sent Events处理
```typescript
const useStreamingMessage = (sessionId: string) => {
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const [sources, setSources] = useState<DocumentSource[]>([]);
  
  const sendMessage = async (content: string) => {
    const eventSource = new EventSource(`/api/chat/message/stream?session_id=${sessionId}&message=${encodeURIComponent(content)}`);
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'token':
          setStreamingMessage(prev => prev + data.content);
          break;
        case 'sources':
          setSources(data.sources);
          break;
        case 'done':
          eventSource.close();
          // 保存完整消息到历史记录
          break;
      }
    };
    
    eventSource.onerror = () => {
      eventSource.close();
      // 错误处理
    };
  };
  
  return { streamingMessage, sources, sendMessage };
};
```

### 4.5 用户体验设计

#### 4.5.1 首次使用引导
- 欢迎页面：介绍系统功能
- 上传引导：提示用户先上传文档
- 功能介绍：气泡提示各功能按钮

#### 4.5.2 加载和反馈状态
- **骨架屏**: 消息列表、文档列表加载
- **加载指示器**: 按钮和API调用状态
- **进度指示**: 文件上传进度
- **成功反馈**: Toast通知和动画

#### 4.5.3 错误处理
- **网络错误**: 重试按钮和离线提示
- **文件错误**: 格式、大小错误的具体提示
- **API错误**: 友好的错误信息展示
- **降级体验**: 流式失败时回退到标准响应

### 4.6 性能优化

#### 4.6.1 渲染优化
- 消息列表虚拟滚动（长对话性能）
- 图片懒加载
- 组件懒加载和代码分割
- React.memo防止不必要渲染

#### 4.6.2 数据优化
- React Query缓存策略
- 增量消息更新
- 分页加载历史消息
- 本地存储草稿消息

## 5. API接口设计

### 5.1 会话管理接口

```typescript
// 创建会话
POST /api/chat/sessions
Response: {
  session_id: string;
  created_at: string;
}

// 获取会话列表  
GET /api/chat/sessions
Response: {
  sessions: Array<{
    id: string;
    title: string;
    created_at: string;
    updated_at: string;
    message_count: number;
  }>;
}

// 获取会话消息
GET /api/chat/sessions/{session_id}/messages?limit=50&offset=0
Response: {
  messages: Message[];
  total: number;
  has_more: boolean;
}
```

### 5.2 消息接口

```typescript
// 发送消息（标准响应）
POST /api/chat/message
Request: {
  session_id: string;
  message: string;
  stream?: boolean;
}
Response: {
  message_id: string;
  content: string;
  sources: DocumentSource[];
  timestamp: string;
}

// 流式消息（Server-Sent Events）
GET /api/chat/message/stream?session_id={id}&message={text}
Response: text/event-stream
```

### 5.3 文档管理接口

```typescript
// 上传文档
POST /api/documents/upload
Request: FormData { file: File; }
Response: {
  document_id: string;
  filename: string;
  status: string;
  processing_status: string;
}

// 获取文档列表
GET /api/documents
Response: {
  documents: DocumentItem[];
  total: number;
}

// 删除文档
DELETE /api/documents/{document_id}
Response: { success: boolean; }
```

## 6. 数据库设计

### 6.1 表结构设计

```sql
-- 会话表
CREATE TABLE chat_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NULL,
    title VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 消息表  
CREATE TABLE chat_messages (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    role VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,
    sources JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id)
);

-- 文档表
CREATE TABLE documents (
    id VARCHAR(36) PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INTEGER NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_status VARCHAR(20) DEFAULT 'uploading',
    chunks_count INTEGER DEFAULT 0,
    error_message TEXT NULL
);
```

### 6.2 索引设计
```sql
-- 会话消息查询优化
CREATE INDEX idx_messages_session_time ON chat_messages(session_id, created_at);

-- 文档状态查询优化  
CREATE INDEX idx_documents_status ON documents(processing_status);

-- 会话更新时间排序
CREATE INDEX idx_sessions_updated ON chat_sessions(updated_at);
```

## 7. 部署配置

### 7.1 Docker Compose配置

```yaml
version: '3.8'

services:
  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend

  # 后端服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./chat.db
      - N8N_WEBHOOK_BASE_URL=http://n8n:5678
    volumes:
      - ./data:/app/data
    depends_on:
      - database

  # 数据库（可选PostgreSQL）
  database:
    image: postgres:14
    environment:
      - POSTGRES_DB=chatdb
      - POSTGRES_USER=chatuser
      - POSTGRES_PASSWORD=chatpass
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### 7.2 环境配置

```bash
# 后端环境变量
DATABASE_URL=sqlite:///./chat.db
N8N_WEBHOOK_BASE_URL=http://localhost:5678
MAX_FILE_SIZE=104857600  # 100MB
RATE_LIMIT_PER_MINUTE=60
DEBUG=false

# 前端环境变量  
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WEBSOCKET_URL=ws://localhost:8000
REACT_APP_MAX_FILE_SIZE=104857600
```

## 8. 开发计划

### 8.1 MVP开发阶段（2周）

#### 第1周：后端核心功能
- [x] 项目框架搭建（FastAPI + SQLAlchemy）
- [x] 会话管理API开发
- [x] 消息处理API开发
- [x] n8n代理层实现
- [x] 基础测试用例

#### 第2周：前端基础界面
- [x] React项目搭建（Vite + TypeScript）  
- [x] 基础聊天界面开发
- [x] 消息输入和显示组件
- [x] 流式响应处理
- [x] 基础样式和布局

### 8.2 完整功能开发（2周）

#### 第3周：文档管理功能
- [x] 文档上传API开发
- [x] 文档管理界面
- [x] 文件拖拽上传组件
- [x] 上传进度和状态管理
- [x] 源文档引用显示

#### 第4周：优化和完善
- [x] 流式响应优化
- [x] 错误处理和重试机制
- [x] 性能优化（虚拟滚动等）
- [x] 响应式设计和移动端适配
- [x] 部署配置和文档

### 8.3 验收标准

#### 8.3.1 功能验收
- [ ] 用户可以成功上传文档并看到处理状态
- [ ] 用户可以进行多轮对话，系统维护上下文
- [ ] 流式输出工作正常，响应流畅
- [ ] 源文档引用准确显示
- [ ] 文档管理功能完整可用

#### 8.3.2 性能验收
- [ ] 消息发送响应时间<1秒
- [ ] 文件上传进度实时显示
- [ ] 长对话列表滚动流畅
- [ ] 移动端体验良好

#### 8.3.3 用户体验验收
- [ ] 界面美观，操作直观
- [ ] 错误提示友好明确
- [ ] 加载状态提示完善
- [ ] 首次使用引导清晰

## 9. 风险评估与应对

### 9.1 技术风险

#### 9.1.1 流式响应稳定性
**风险**: Server-Sent Events连接不稳定，特别是移动端
**应对**: 
- 实现自动重连机制
- 提供降级方案（标准HTTP响应）
- 增加连接状态监控

#### 9.1.2 大文件上传性能
**风险**: 100MB文件上传可能导致超时或内存问题
**应对**:
- 实现分片上传机制
- 添加上传暂停/恢复功能
- 服务端流式处理文件

### 9.2 业务风险

#### 9.2.1 n8n API依赖
**风险**: n8n服务不可用影响整个系统
**应对**:
- 实现健康检查和重试机制
- 添加API降级和缓存策略
- 提供离线模式基础功能

#### 9.2.2 用户数据安全
**风险**: 用户上传的文档和对话数据安全
**应对**:
- 实现数据加密存储
- 添加数据备份机制
- 制定数据清理策略

## 10. 成功指标

### 10.1 技术指标
- API响应时间95%小于1秒
- 系统可用性99.5%以上
- 文件上传成功率95%以上
- 流式响应延迟小于200ms

### 10.2 用户体验指标  
- 用户首次使用成功率90%以上
- 平均会话长度>5轮对话
- 用户文档上传成功率95%以上
- 移动端可用性测试通过

### 10.3 功能完整性
- 所有核心功能正常运行
- 错误处理覆盖率100%
- 响应式设计支持主流设备
- 与n8n API集成稳定

---

*本需求文档基于现有n8n RAG workflow的API能力设计，在开发过程中可能根据实际情况进行调整优化。*