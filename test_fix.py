#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import requests
import json
import time

def test_backend_health():
    """测试后端健康状态"""
    print("🏥 测试后端健康状态...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        print(f"后端状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端服务正常: {data.get('service')}")
            print(f"数据库状态: {data.get('database')}")
        else:
            print("❌ 后端服务异常")
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")

def test_document_upload():
    """测试文档上传功能"""
    print("\n📄 测试文档上传功能...")
    
    # 创建测试文件
    test_content = "这是一个测试文档，用于验证上传功能是否正常工作。"
    
    try:
        # 准备文件数据
        files = {
            'file': ('test_document.txt', test_content.encode('utf-8'), 'text/plain')
        }
        
        # 发送上传请求
        response = requests.post(
            "http://localhost:8000/api/v1/documents/upload",
            files=files,
            timeout=30
        )
        
        print(f"上传状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文档上传成功")
            print(f"文档ID: {data.get('document_id')}")
            print(f"文件名: {data.get('filename')}")
            print(f"处理状态: {data.get('processing_status')}")
            return data.get('document_id')
        else:
            print(f"❌ 文档上传失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 文档上传异常: {e}")
        return None

def test_document_list():
    """测试文档列表功能"""
    print("\n📋 测试文档列表功能...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/documents/?page=1&page_size=5",
            timeout=10
        )
        
        print(f"列表状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文档列表获取成功")
            print(f"总文档数: {data.get('total')}")
            
            documents = data.get('documents', [])
            for i, doc in enumerate(documents[:3]):  # 只显示前3个
                print(f"  {i+1}. {doc.get('filename')} - {doc.get('processing_status')}")
                
        else:
            print(f"❌ 文档列表获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 文档列表获取异常: {e}")

def test_chat_functionality():
    """测试聊天功能"""
    print("\n💬 测试聊天功能...")
    
    try:
        chat_data = {
            "message": "你好，这是一个测试消息",
            "stream": False
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/chat/message",
            json=chat_data,
            timeout=30
        )
        
        print(f"聊天状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 聊天功能正常")
            print(f"会话ID: {data.get('session_id')}")
            print(f"回复: {data.get('message', '无回复')[:100]}...")
        else:
            print(f"❌ 聊天功能失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 聊天功能异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试修复效果...")
    print("=" * 60)
    
    # 测试各个功能
    test_backend_health()
    document_id = test_document_upload()
    test_document_list()
    test_chat_functionality()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("1. 如果文档上传成功但处理状态为failed，说明n8n工作流需要重新配置")
    print("2. 如果聊天功能失败，检查n8n的查询webhook是否正常")
    print("3. 建议在n8n界面中检查工作流的执行日志")
    print("\n💡 下一步操作:")
    print("1. 在n8n界面中重新导入并激活工作流")
    print("2. 确保所有webhook节点都是绿色状态")
    print("3. 测试工作流的各个节点是否正常执行")

if __name__ == "__main__":
    main()
