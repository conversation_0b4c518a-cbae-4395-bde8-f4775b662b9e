#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试n8n文档上传功能
"""

import requests
import time

def test_n8n_upload():
    """测试n8n文档上传"""
    print("🔧 测试n8n文档上传功能...")
    
    # 创建测试文件内容
    test_content = """这是一个测试文档。

人工智能（AI）是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。

深度学习是机器学习的一个子集，它模仿人脑的工作方式来处理数据。"""
    
    try:
        # 准备文件数据
        files = {
            'file': ('ai_test.txt', test_content.encode('utf-8'), 'text/plain')
        }
        
        data = {
            'filename': 'ai_test.txt'
        }
        
        print("📤 发送文件到n8n...")
        
        # 发送到n8n webhook
        response = requests.post(
            "http://localhost:5678/webhook/rag-upload",
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            try:
                result = response.json()
                if 'document_id' in result:
                    print(f"✅ n8n文档上传成功 - 文档ID: {result['document_id']}")
                    print(f"   文件名: {result.get('filename', 'unknown')}")
                    print(f"   状态: {result.get('status', 'unknown')}")
                    print(f"   分块数: {result.get('chunks_count', 0)}")
                else:
                    print("⚠️ n8n响应成功但格式异常")
            except:
                print("✅ n8n文档上传成功（响应格式为文本）")
            
            # 等待一下，然后测试查询
            print("\n⏳ 等待3秒后测试查询...")
            time.sleep(3)
            
            # 测试查询功能
            query_data = {
                "message": "什么是人工智能？",
                "session_id": "test_session"
            }
            
            print("🔍 测试查询功能...")
            query_response = requests.post(
                "http://localhost:5678/webhook/rag-query",
                json=query_data,
                timeout=30
            )
            
            print(f"查询状态码: {query_response.status_code}")
            print(f"查询响应: {query_response.text[:500]}...")
            
            if query_response.status_code == 200:
                print("✅ n8n查询功能正常")
            else:
                print("❌ n8n查询功能异常")
                
        else:
            print("❌ n8n文档上传失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试n8n工作流...")
    print("=" * 50)
    
    test_n8n_upload()
    
    print("\n" + "=" * 50)
    print("💡 如果测试失败，请检查:")
    print("1. n8n工作流是否已重新导入")
    print("2. 工作流是否已激活")
    print("3. 所有节点是否都是绿色状态")
    print("4. 查看n8n的执行日志获取详细错误信息")

if __name__ == "__main__":
    main()
