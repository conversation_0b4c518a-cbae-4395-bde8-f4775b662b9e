#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Ollama模型配置
"""

import requests
import json

def test_ollama_models():
    """测试Ollama模型"""
    print("🤖 测试Ollama模型配置...")
    
    # 1. 获取可用模型列表
    print("\n📋 获取可用模型列表...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            available_models = [m['name'] for m in models.get('models', [])]
            print(f"✅ 可用模型: {available_models}")
        else:
            print(f"❌ 获取模型列表失败: {response.text}")
            return
    except Exception as e:
        print(f"❌ 连接Ollama失败: {e}")
        return
    
    # 2. 测试嵌入模型
    print("\n🔤 测试嵌入模型 (bge-m3:latest)...")
    embedding_data = {
        "model": "bge-m3:latest",
        "prompt": "这是一个测试文本"
    }
    
    try:
        response = requests.post(
            "http://localhost:11434/api/embeddings",
            json=embedding_data,
            timeout=30
        )
        
        print(f"嵌入请求状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            embedding = result.get('embedding', [])
            print(f"✅ 嵌入成功，向量维度: {len(embedding)}")
            print(f"   向量前5个值: {embedding[:5]}")
        else:
            print(f"❌ 嵌入失败: {response.text}")
            
            # 尝试备用模型
            print("\n🔄 尝试备用嵌入模型...")
            for model in available_models:
                if 'embed' in model.lower() or 'bge' in model.lower():
                    print(f"   尝试模型: {model}")
                    embedding_data['model'] = model
                    try:
                        response = requests.post(
                            "http://localhost:11434/api/embeddings",
                            json=embedding_data,
                            timeout=30
                        )
                        if response.status_code == 200:
                            result = response.json()
                            embedding = result.get('embedding', [])
                            print(f"   ✅ {model} 可用，向量维度: {len(embedding)}")
                            break
                        else:
                            print(f"   ❌ {model} 不可用")
                    except:
                        print(f"   ❌ {model} 连接失败")
                        
    except Exception as e:
        print(f"❌ 嵌入请求异常: {e}")
    
    # 3. 测试生成模型
    print("\n💬 测试生成模型 (qwen3:8b)...")
    generate_data = {
        "model": "qwen3:8b",
        "prompt": "请简单介绍一下人工智能。",
        "stream": False
    }
    
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=generate_data,
            timeout=30
        )
        
        print(f"生成请求状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '')
            print(f"✅ 生成成功，回答长度: {len(response_text)}")
            print(f"   回答预览: {response_text[:100]}...")
        else:
            print(f"❌ 生成失败: {response.text}")
            
            # 尝试备用模型
            print("\n🔄 尝试备用生成模型...")
            for model in available_models:
                if model not in ['bge-m3:latest'] and 'embed' not in model.lower():
                    print(f"   尝试模型: {model}")
                    generate_data['model'] = model
                    try:
                        response = requests.post(
                            "http://localhost:11434/api/generate",
                            json=generate_data,
                            timeout=30
                        )
                        if response.status_code == 200:
                            result = response.json()
                            response_text = result.get('response', '')
                            print(f"   ✅ {model} 可用，回答长度: {len(response_text)}")
                            print(f"   回答预览: {response_text[:50]}...")
                            break
                        else:
                            print(f"   ❌ {model} 不可用")
                    except:
                        print(f"   ❌ {model} 连接失败")
                        
    except Exception as e:
        print(f"❌ 生成请求异常: {e}")

if __name__ == "__main__":
    test_ollama_models()
    
    print("\n" + "=" * 50)
    print("💡 配置建议:")
    print("1. 确保使用的模型名称与可用模型列表一致")
    print("2. 嵌入模型推荐: bge-m3:latest 或其他embedding模型")
    print("3. 生成模型推荐: qwen3:8b 或 llama3.2:latest")
    print("4. 如果模型不存在，使用 'ollama pull <model_name>' 下载")
