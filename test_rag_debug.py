#!/usr/bin/env python3
"""
RAG系统调试测试脚本
用于测试文档上传和查询功能
"""

import requests
import json
import time
import sys

# n8n webhook URLs
UPLOAD_URL = "http://localhost:5678/webhook/upload-document"
QUERY_URL = "http://localhost:5678/webhook/query-rag"

def test_upload_document():
    """测试文档上传"""
    print("=== 测试文档上传 ===")
    
    # 测试文档内容
    test_content = """# Gemini CLI 安装指南

## 安装步骤

1. 下载Gemini CLI工具
2. 解压到指定目录
3. 配置环境变量
4. 验证安装

## 使用方法

运行以下命令开始使用：
```bash
gemini-cli --help
```

## 常见问题

Q: 如何安装Gemini CLI?
A: 请按照上述安装步骤进行操作。
"""
    
    # 上传文档
    files = {
        'file': ('test_document.md', test_content, 'text/markdown')
    }
    
    try:
        response = requests.post(UPLOAD_URL, files=files, timeout=30)
        print(f"上传响应状态: {response.status_code}")
        print(f"上传响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文档上传成功: {result}")
            return True
        else:
            print(f"❌ 文档上传失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 上传请求异常: {e}")
        return False

def test_query_rag():
    """测试RAG查询"""
    print("\n=== 测试RAG查询 ===")
    
    query_data = {
        "query": "如何安装Gemini CLI?",
        "stream": False
    }
    
    try:
        response = requests.post(
            QUERY_URL, 
            json=query_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"查询响应状态: {response.status_code}")
        print(f"查询响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 查询成功")
            
            # 解析响应
            if 'response' in result:
                print(f"AI回答: {result['response']}")
            
            # 显示调试信息
            if 'debug_info' in result:
                debug = result['debug_info']
                print(f"调试信息:")
                print(f"  - 数据库大小: {debug.get('database_size', 'unknown')}")
                print(f"  - 检索结果数: {debug.get('total_results', 'unknown')}")
                print(f"  - 过滤结果数: {debug.get('filtered_results', 'unknown')}")
            
            return True
        else:
            print(f"❌ 查询失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 查询请求异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始RAG系统调试测试")
    
    # 测试上传
    upload_success = test_upload_document()
    
    if not upload_success:
        print("❌ 文档上传失败，终止测试")
        sys.exit(1)
    
    # 等待处理完成
    print("\n⏳ 等待文档处理完成...")
    time.sleep(5)
    
    # 测试查询
    query_success = test_query_rag()
    
    if query_success:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 查询测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
