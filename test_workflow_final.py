#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试n8n RAG工作流
"""

import requests
import time
import json

def test_rag_workflow():
    """测试完整RAG工作流"""
    print("🚀 测试n8n RAG工作流...")
    print("=" * 60)
    
    # 1. 上传文档
    print("📤 步骤1: 上传测试文档...")
    test_content = """人工智能（AI）是计算机科学的一个分支，旨在创造能够模拟人类智能的系统。

机器学习是AI的核心技术之一，它使计算机能够从数据中学习并改进，而无需明确编程。

深度学习是机器学习的一个子集，使用多层神经网络来分析数据。

自然语言处理（NLP）是AI的一个分支，专注于使计算机理解和生成人类语言。

计算机视觉是AI的另一个重要领域，使计算机能够从图像或视频中获取信息。

强化学习是一种机器学习方法，通过与环境交互来学习最佳行动策略。

n8n是一个强大的工作流自动化工具，可以连接各种API和服务。

RAG（检索增强生成）是一种结合检索系统和生成模型的技术，可以生成基于事实的回答。"""
    
    files = {
        'file': ('ai_concepts.txt', test_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'filename': 'ai_concepts.txt'
    }
    
    try:
        upload_response = requests.post(
            "http://localhost:5678/webhook/rag-upload",
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"上传状态码: {upload_response.status_code}")
        print(f"上传响应内容: {upload_response.text}")
        
        if upload_response.status_code == 200:
            try:
                result = upload_response.json()
                print(f"✅ 上传成功 - 文档ID: {result.get('document_id')}")
                print(f"   分块数: {result.get('chunks_count')}")
            except Exception as json_error:
                print(f"⚠️ 上传成功但响应不是JSON: {json_error}")
        else:
            print(f"❌ 上传失败: {upload_response.text}")
            return
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return
    
    # 2. 等待处理完成
    print("\n⏳ 步骤2: 等待10秒让向量处理完成...")
    time.sleep(10)
    
    # 3. 测试标准格式查询
    print("\n🔍 步骤3: 测试标准格式查询...")
    query_data = {
        "query": "什么是RAG技术？",
        "stream": False
    }
    
    try:
        query_response = requests.post(
            "http://localhost:5678/webhook/rag-query",
            json=query_data,
            timeout=60
        )
        
        print(f"查询状态码: {query_response.status_code}")
        if query_response.status_code == 200:
            try:
                result = query_response.json()
                print(f"✅ 查询成功")
                print(f"   回答长度: {len(result.get('answer', ''))}")
                print(f"   相关文档数: {len(result.get('sources', []))}")
                print(f"   数据库大小: {result.get('database_size', 'N/A')}")
                
                # 显示回答内容（前200字符）
                answer = result.get('answer', '')
                if answer:
                    print(f"   回答预览: {answer[:200]}...")
                
                # 显示相关文档
                sources = result.get('sources', [])
                if sources:
                    print(f"   相关文档预览:")
                    for i, source in enumerate(sources[:2]):
                        similarity = source.get('similarity', 0)
                        content = source.get('content', '')[:100]
                        print(f"     [{i}] 相似度: {similarity:.4f}, 内容: {content}...")
                else:
                    print("   ⚠️ 没有找到相关文档")
            except Exception as json_error:
                print(f"⚠️ 查询成功但响应不是JSON: {json_error}")
                print(f"   响应内容: {query_response.text[:200]}...")
        else:
            print(f"❌ 查询失败: {query_response.text}")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
    
    # 4. 测试前端格式查询
    print("\n🔍 步骤4: 测试前端格式查询...")
    frontend_query_data = {
        "message": "什么是机器学习？",
        "session_id": "test_session_001"
    }
    
    try:
        frontend_response = requests.post(
            "http://localhost:5678/webhook/rag-query",
            json=frontend_query_data,
            timeout=60
        )
        
        print(f"前端查询状态码: {frontend_response.status_code}")
        if frontend_response.status_code == 200:
            try:
                result = frontend_response.json()
                print(f"✅ 前端查询成功")
                print(f"   回答长度: {len(result.get('answer', ''))}")
                print(f"   相关文档数: {len(result.get('sources', []))}")
                print(f"   会话ID: {result.get('session_id', 'N/A')}")
                print(f"   数据库大小: {result.get('database_size', 'N/A')}")
                
                # 显示回答内容（前200字符）
                answer = result.get('answer', '')
                if answer:
                    print(f"   回答预览: {answer[:200]}...")
                else:
                    print("   ⚠️ 没有生成回答")
            except Exception as json_error:
                print(f"⚠️ 前端查询成功但响应不是JSON: {json_error}")
                print(f"   响应内容: {frontend_response.text[:200]}...")
        else:
            print(f"❌ 前端查询失败: {frontend_response.text}")
            
    except Exception as e:
        print(f"❌ 前端查询异常: {e}")
    
    print("\n" + "=" * 60)
    print("💡 测试结果总结:")
    print("1. 上传文档: " + ("✅ 成功" if upload_response.status_code == 200 else "❌ 失败"))
    print("2. 标准查询: " + ("✅ 成功" if query_response.status_code == 200 else "❌ 失败"))
    print("3. 前端查询: " + ("✅ 成功" if frontend_response.status_code == 200 else "❌ 失败"))
    print("\n如果测试成功，请重新导入工作流并激活！")

if __name__ == "__main__":
    test_rag_workflow()
