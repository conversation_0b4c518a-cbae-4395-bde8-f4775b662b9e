#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工作流状态
"""

import requests

def test_webhook_status():
    """测试webhook状态"""
    print("🔍 测试n8n webhook状态...")
    
    # 测试上传webhook
    print("\n📤 测试上传webhook...")
    try:
        response = requests.get("http://localhost:5678/webhook/rag-upload", timeout=10)
        print(f"GET状态码: {response.status_code}")
        print(f"GET响应: {response.text[:200]}")
    except Exception as e:
        print(f"GET请求失败: {e}")
    
    # 测试查询webhook
    print("\n🔍 测试查询webhook...")
    try:
        response = requests.get("http://localhost:5678/webhook/rag-query", timeout=10)
        print(f"GET状态码: {response.status_code}")
        print(f"GET响应: {response.text[:200]}")
    except Exception as e:
        print(f"GET请求失败: {e}")
    
    # 测试简单的POST请求
    print("\n📝 测试简单POST请求...")
    try:
        test_data = {"test": "data"}
        response = requests.post(
            "http://localhost:5678/webhook/rag-upload",
            json=test_data,
            timeout=10
        )
        print(f"POST状态码: {response.status_code}")
        print(f"POST响应: {response.text[:200]}")
        print(f"POST响应头: {dict(response.headers)}")
    except Exception as e:
        print(f"POST请求失败: {e}")

def test_ollama_status():
    """测试Ollama状态"""
    print("\n🤖 测试Ollama状态...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        print(f"Ollama状态码: {response.status_code}")
        if response.status_code == 200:
            models = response.json()
            print(f"可用模型: {[m['name'] for m in models.get('models', [])]}")
        else:
            print(f"Ollama响应: {response.text}")
    except Exception as e:
        print(f"Ollama连接失败: {e}")
        print("请确保Ollama服务正在运行: ollama serve")

if __name__ == "__main__":
    test_webhook_status()
    test_ollama_status()
    
    print("\n" + "=" * 50)
    print("💡 如果webhook返回404或其他错误:")
    print("1. 确认n8n服务正在运行")
    print("2. 确认工作流已导入并激活")
    print("3. 检查webhook路径是否正确")
    print("4. 查看n8n界面中的工作流状态")
